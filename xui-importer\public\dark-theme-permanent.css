/* TEMA OSCURO PERMANENTE - XUI IMPORTER */
/* Este archivo se carga directamente desde index.html y no se borra */

/* Variables de colores */
:root {
  --bg-primary: #1a1a1a;
  --bg-secondary: #2d2d2d;
  --bg-tertiary: #3a3a3a;
  --text-primary: #ffffff;
  --text-secondary: #e0e0e0;
  --text-muted: #b0b0b0;
  --border-color: #444444;
  --accent-red: #dc3545;
  --accent-red-hover: #c82333;
  --accent-red-light: rgba(220, 53, 69, 0.1);
  --success-color: #28a745;
  --warning-color: #ffc107;
  --info-color: #17a2b8;
}

/* Fondo principal FORZADO */
html, body, #root {
  background-color: var(--bg-primary) !important;
  color: var(--text-primary) !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* Contenedores principales */
.container, .container-fluid {
  background-color: transparent !important;
  color: var(--text-primary) !important;
}

/* Cards y paneles */
.card {
  background-color: var(--bg-secondary) !important;
  border: 1px solid var(--border-color) !important;
  color: var(--text-primary) !important;
}

.card-header {
  background-color: var(--bg-tertiary) !important;
  border-bottom: 1px solid var(--border-color) !important;
  color: var(--text-primary) !important;
}

.card-body {
  background-color: var(--bg-secondary) !important;
  color: var(--text-primary) !important;
}

/* Formularios FORZADOS */
.form-control, input, textarea, select {
  background-color: var(--bg-tertiary) !important;
  border: 1px solid var(--border-color) !important;
  color: var(--text-primary) !important;
}

.form-control:focus, input:focus, textarea:focus, select:focus {
  background-color: var(--bg-tertiary) !important;
  border-color: var(--accent-red) !important;
  box-shadow: 0 0 0 0.2rem var(--accent-red-light) !important;
  color: var(--text-primary) !important;
}

/* Botones */
.btn-primary {
  background-color: var(--accent-red) !important;
  border-color: var(--accent-red) !important;
  color: white !important;
}

.btn-primary:hover {
  background-color: var(--accent-red-hover) !important;
  border-color: var(--accent-red-hover) !important;
}

.btn-danger {
  background-color: var(--accent-red) !important;
  border-color: var(--accent-red) !important;
}

/* Eliminar TODOS los fondos blancos */
.bg-white, .bg-light {
  background-color: var(--bg-secondary) !important;
}

/* Forzar elementos específicos */
*[style*="background-color: white"],
*[style*="background-color: #fff"],
*[style*="background-color: #ffffff"],
*[style*="background: white"],
*[style*="background: #fff"],
*[style*="background: #ffffff"] {
  background-color: var(--bg-tertiary) !important;
  color: var(--text-primary) !important;
}

/* Texto */
h1, h2, h3, h4, h5, h6 {
  color: var(--text-primary) !important;
  font-weight: 600 !important;
}

p, span, div, label {
  color: var(--text-primary) !important;
}

.text-muted {
  color: var(--text-muted) !important;
}

.text-secondary {
  color: var(--text-secondary) !important;
}

.text-danger {
  color: var(--accent-red) !important;
}

/* Sidebar específico */
.sidebar {
  background: linear-gradient(135deg, #2d1b1b 0%, #3a2222 100%) !important;
  border-right: 2px solid var(--accent-red) !important;
}

.sidebar .btn-danger {
  background-color: var(--accent-red) !important;
}

.sidebar .btn-outline-light:hover {
  background-color: rgba(220, 53, 69, 0.1) !important;
  border-color: var(--accent-red) !important;
  color: var(--accent-red) !important;
}

/* Main content */
.main-content {
  background-color: var(--bg-primary) !important;
}

/* Alertas */
.alert-success {
  background-color: rgba(40, 167, 69, 0.1) !important;
  border-color: var(--success-color) !important;
  color: var(--success-color) !important;
}

.alert-danger {
  background-color: var(--accent-red-light) !important;
  border-color: var(--accent-red) !important;
  color: var(--accent-red) !important;
}

/* Badges */
.badge {
  font-weight: 600 !important;
}

.bg-danger {
  background-color: var(--accent-red) !important;
}

.bg-success {
  background-color: var(--success-color) !important;
}

.bg-warning {
  background-color: var(--warning-color) !important;
  color: #000 !important;
}

.bg-info {
  background-color: var(--info-color) !important;
}

/* Progress bars */
.progress {
  background-color: var(--bg-tertiary) !important;
}

.progress-bar {
  background-color: var(--accent-red) !important;
}

/* Tablas */
.table {
  color: var(--text-primary) !important;
}

.table td, .table th {
  color: var(--text-primary) !important;
  border-color: var(--border-color) !important;
}

/* Modales */
.modal-content {
  background-color: var(--bg-secondary) !important;
  border: 1px solid var(--border-color) !important;
}

/* Dropdown */
.dropdown-menu {
  background-color: var(--bg-secondary) !important;
  border: 1px solid var(--border-color) !important;
}

.dropdown-item {
  color: var(--text-primary) !important;
}

.dropdown-item:hover {
  background-color: var(--bg-tertiary) !important;
  color: var(--text-primary) !important;
}

/* REGLA FINAL - Capturar cualquier elemento blanco */
* {
  /* Si tiene fondo blanco, cambiarlo */
}

/* Elementos específicos que NUNCA deben ser blancos */
input:not(.btn), textarea:not(.btn), select:not(.btn) {
  background-color: var(--bg-tertiary) !important;
  color: var(--text-primary) !important;
  border-color: var(--border-color) !important;
}

/* Scrollbars */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--text-muted);
}
