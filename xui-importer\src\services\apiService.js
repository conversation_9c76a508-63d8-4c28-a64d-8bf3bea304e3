/**
 * 🌐 Servicio de API - XUI Importer Frontend
 * Cliente para conectar con el backend real
 */

import { getApiBaseUrl, testApiConnectivity } from '../config/apiConfig';

/**
 * 🔧 Configuración base para fetch con manejo mejorado de errores
 */
const apiRequest = async (endpoint, options = {}) => {
  const url = `${getApiBaseUrl()}${endpoint}`;

  const defaultOptions = {
    headers: {
      'Content-Type': 'application/json',
      ...options.headers
    },
    timeout: 30000 // 30 segundos de timeout
  };

  const config = { ...defaultOptions, ...options };

  try {
    if (window.debugLog) {
      window.debugLog('info', `🌐 API Request: ${config.method || 'GET'} ${url}`);
    } else {
      console.log(`🌐 API Request: ${config.method || 'GET'} ${url}`);
    }

    const response = await fetch(url, config);

    // Verificar si la respuesta es JSON válida
    let data;
    try {
      data = await response.json();
    } catch (jsonError) {
      throw new Error(`Respuesta no válida del servidor. Verifique que el backend esté ejecutándose en ${getApiBaseUrl()}`);
    }

    if (!response.ok) {
      throw new Error(data.message || `HTTP ${response.status}: ${response.statusText}`);
    }

    if (window.debugLog) {
      window.debugLog('success', `✅ API Response: ${endpoint}`);
    } else {
      console.log(`✅ API Response: ${endpoint}`);
    }

    return data;

  } catch (error) {
    // Mejorar mensajes de error
    let errorMessage = error.message;

    if (error.name === 'TypeError' && error.message.includes('fetch')) {
      errorMessage = `No se puede conectar al servidor backend. Asegúrese de que el servidor esté ejecutándose en ${getApiBaseUrl()}`;
    } else if (error.message.includes('Failed to fetch')) {
      errorMessage = `Error de conexión con el servidor backend en ${getApiBaseUrl()}. Verifique que el servidor esté activo.`;
    }

    if (window.debugLog) {
      window.debugLog('error', `❌ API Error: ${endpoint} - ${errorMessage}`);
    } else {
      console.error(`❌ API Error: ${endpoint} - ${errorMessage}`);
    }

    // Crear error con información adicional
    const enhancedError = new Error(errorMessage);
    enhancedError.originalError = error;
    enhancedError.endpoint = endpoint;
    enhancedError.url = url;

    throw enhancedError;
  }
};

/**
 * 🔥 API de Importación
 */
export const importAPI = {
  // Importar series completas
  importSeries: async (importData) => {
    return apiRequest('/import/series', {
      method: 'POST',
      body: JSON.stringify(importData)
    });
  },

  // Preview de importación
  previewSeries: async (previewData) => {
    return apiRequest('/import/preview/series', {
      method: 'POST',
      body: JSON.stringify(previewData)
    });
  },

  // Resumen de importaciones
  getImportSummary: async (dateFrom, dateTo) => {
    const params = new URLSearchParams();
    if (dateFrom) params.append('date_from', dateFrom);
    if (dateTo) params.append('date_to', dateTo);
    
    return apiRequest(`/import/summary?${params.toString()}`);
  },

  // Reprocesar serie
  reprocessSeries: async (seriesId, options = {}) => {
    return apiRequest(`/import/reprocess/${seriesId}`, {
      method: 'POST',
      body: JSON.stringify(options)
    });
  }
};

/**
 * 📺 API de Series
 */
export const seriesAPI = {
  // Listar series
  getSeries: async (page = 1, limit = 20, filters = {}) => {
    const params = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
      ...filters
    });
    
    return apiRequest(`/series?${params.toString()}`);
  },

  // Obtener serie por ID
  getSeriesById: async (id) => {
    return apiRequest(`/series/${id}`);
  },

  // Crear nueva serie
  createSeries: async (seriesData, episodesData) => {
    return apiRequest('/series', {
      method: 'POST',
      body: JSON.stringify({ seriesData, episodesData })
    });
  },

  // Actualizar serie
  updateSeries: async (id, seriesData, episodesData, updateEpisodes = false) => {
    return apiRequest(`/series/${id}`, {
      method: 'PUT',
      body: JSON.stringify({ seriesData, episodesData, updateEpisodes })
    });
  },

  // Eliminar serie
  deleteSeries: async (id) => {
    return apiRequest(`/series/${id}`, {
      method: 'DELETE'
    });
  },

  // Buscar series
  searchSeries: async (title, tmdbId = null) => {
    const params = new URLSearchParams({ title });
    if (tmdbId) params.append('tmdb_id', tmdbId);
    
    return apiRequest(`/series/search/${encodeURIComponent(title)}?${params.toString()}`);
  },

  // Enrichecer con TMDB
  enrichSeries: async (id, forceUpdate = false) => {
    return apiRequest(`/series/${id}/enrich`, {
      method: 'POST',
      body: JSON.stringify({ force_update: forceUpdate })
    });
  },

  // Estadísticas de series
  getSeriesStats: async () => {
    return apiRequest('/series/stats/overview');
  }
};

/**
 * 🗄️ API de Base de Datos
 */
export const databaseAPI = {
  // Test de conexión
  testConnection: async (connectionData) => {
    return apiRequest('/database/test', {
      method: 'POST',
      body: JSON.stringify(connectionData)
    });
  },

  // Test conexión actual
  testCurrentConnection: async () => {
    return apiRequest('/database/test/current');
  },

  // Estadísticas de BD
  getStats: async () => {
    return apiRequest('/database/stats');
  },

  // Verificar tablas
  verifyTables: async () => {
    return apiRequest('/database/verify/tables');
  },

  // Información de tablas
  getTablesInfo: async () => {
    return apiRequest('/database/info/tables');
  },

  // Reparar integridad
  repairIntegrity: async (executeRepair = false) => {
    return apiRequest('/database/repair/integrity', {
      method: 'POST',
      body: JSON.stringify({ execute_repair: executeRepair })
    });
  },

  // Limpiar datos de prueba
  cleanupTestData: async (pattern = 'TEST_%') => {
    return apiRequest('/database/cleanup/test-data', {
      method: 'DELETE',
      body: JSON.stringify({ 
        confirm_cleanup: true, 
        test_pattern: pattern 
      })
    });
  },

  // Obtener servidores de streaming
  getServers: async () => {
    return apiRequest('/database/servers');
  },

  // Obtener categorías populares para VOD
  getCategories: async (type = 'vod', limit = 20) => {
    return apiRequest(`/categories?type=${type}&limit=${limit}`);
  },

  // Métodos de categorías comentados - no se usan actualmente
  /*
  getCategoriesByType: async (categoryType = null) => {
    const params = categoryType ? `?type=${categoryType}` : '';
    return apiRequest(`/database/categories-by-type${params}`);
  },

  getBouquets: async () => {
    return apiRequest('/database/bouquets');
  },

  getBouquetCategories: async (bouquetId, contentType = 'all') => {
    return apiRequest(`/database/bouquet/${bouquetId}/categories?content_type=${contentType}`);
  },
  */

  // Obtener estadísticas del servidor
  getServerStats: async () => {
    return apiRequest('/database/server-stats');
  },

  // Obtener datos completos del dashboard
  getDashboardData: async () => {
    return apiRequest('/database/dashboard-data');
  },

  // 🏯 Obtener tipos de stream (Paso 1)
  getStreamTypes: async () => {
    return apiRequest('/database/stream-types');
  },

  // 🟢 Obtener muestra de streams (Paso 2)
  getStreamsSample: async (limit = 100) => {
    return apiRequest(`/database/streams-sample?limit=${limit}`);
  },

  // 🔴 Análisis de estructura de series (Pasos 4A y 4B)
  getSeriesStructureAnalysis: async () => {
    return apiRequest('/database/series-structure-analysis');
  },

  // 🟢 Obtener streams con paginación completa
  getStreams: async (page = 1, limit = 1000) => {
    return apiRequest(`/database/streams?page=${page}&limit=${limit}`);
  },

  // 📊 Obtener estadísticas completas de contenido
  getContentStats: async () => {
    return apiRequest('/database/content-stats');
  },

  // 🚀 Optimizar índices de base de datos
  optimizeIndexes: async () => {
    return apiRequest('/database/optimize-indexes', {
      method: 'POST'
    });
  },

  // 📊 Analizar rendimiento de consultas
  getQueryPerformance: async () => {
    return apiRequest('/database/query-performance');
  }
};

/**
 * 🎬 API de TMDB
 */
export const tmdbAPI = {
  // Buscar serie
  searchSeries: async (title, year = null, page = 1) => {
    const params = new URLSearchParams({ title, page: page.toString() });
    if (year) params.append('year', year);
    
    return apiRequest(`/tmdb/search/series?${params.toString()}`);
  },

  // Buscar película
  searchMovie: async (title, year = null, page = 1) => {
    const params = new URLSearchParams({ title, page: page.toString() });
    if (year) params.append('year', year);
    
    return apiRequest(`/tmdb/search/movie?${params.toString()}`);
  },

  // Búsqueda múltiple
  searchMulti: async (title, year = null) => {
    const params = new URLSearchParams({ title });
    if (year) params.append('year', year);
    
    return apiRequest(`/tmdb/search/multi?${params.toString()}`);
  },

  // Detalles de serie
  getSeriesDetails: async (id, appendToResponse = null) => {
    const params = new URLSearchParams();
    if (appendToResponse) params.append('append_to_response', appendToResponse);
    
    return apiRequest(`/tmdb/series/${id}?${params.toString()}`);
  },

  // Detalles de película
  getMovieDetails: async (id, appendToResponse = null) => {
    const params = new URLSearchParams();
    if (appendToResponse) params.append('append_to_response', appendToResponse);
    
    return apiRequest(`/tmdb/movie/${id}?${params.toString()}`);
  },

  // Temporadas de serie
  getSeriesSeasons: async (id) => {
    return apiRequest(`/tmdb/series/${id}/seasons`);
  },

  // Episodios de temporada
  getSeasonEpisodes: async (id, seasonNumber) => {
    return apiRequest(`/tmdb/series/${id}/season/${seasonNumber}`);
  },

  // Imágenes de serie
  getSeriesImages: async (id) => {
    return apiRequest(`/tmdb/series/${id}/images`);
  },

  // Imágenes de película
  getMovieImages: async (id) => {
    return apiRequest(`/tmdb/movie/${id}/images`);
  },

  // URL de imagen
  getImageUrl: async (path, size = 'w500') => {
    const params = new URLSearchParams({ path, size });
    return apiRequest(`/tmdb/image/url?${params.toString()}`);
  },

  // Formatear para XUI
  formatForXUI: async (tmdbData, contentType = 'series') => {
    return apiRequest(`/tmdb/format/${contentType}`, {
      method: 'POST',
      body: JSON.stringify({ tmdb_data: tmdbData })
    });
  },

  // Búsqueda avanzada
  searchAdvanced: async (searchOptions) => {
    return apiRequest('/tmdb/search/advanced', {
      method: 'POST',
      body: JSON.stringify(searchOptions)
    });
  },

  // Test conexión TMDB
  testConnection: async () => {
    return apiRequest('/tmdb/test');
  }
};

/**
 * 📁 API de M3U
 */
export const m3uAPI = {
  // Subir archivo
  uploadFile: async (file, parseType = 'auto', previewOnly = false) => {
    const formData = new FormData();
    formData.append('m3uFile', file);
    formData.append('parse_type', parseType);
    formData.append('preview_only', previewOnly.toString());
    
    return apiRequest('/m3u/upload', {
      method: 'POST',
      body: formData,
      headers: {} // Dejar que el navegador establezca Content-Type
    });
  },

  // Parsear contenido
  parseContent: async (content, parseType = 'auto', previewOnly = false) => {
    return apiRequest('/m3u/parse', {
      method: 'POST',
      body: JSON.stringify({ content, parse_type: parseType, preview_only: previewOnly })
    });
  },

  // Validar M3U
  validateContent: async (content) => {
    return apiRequest('/m3u/validate', {
      method: 'POST',
      body: JSON.stringify({ content })
    });
  },

  // Analizar estadísticas
  analyzeContent: async (content) => {
    return apiRequest('/m3u/analyze', {
      method: 'POST',
      body: JSON.stringify({ content })
    });
  },

  // Analizar archivo M3U
  analyzeFile: async (file) => {
    try {
      // Leer contenido del archivo
      const content = await new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = (e) => resolve(e.target.result);
        reader.onerror = () => reject(new Error('Error leyendo archivo'));
        reader.readAsText(file);
      });

      // Enviar contenido para análisis
      return apiRequest('/m3u/analyze', {
        method: 'POST',
        body: JSON.stringify({ content })
      });
    } catch (error) {
      throw new Error(`Error procesando archivo: ${error.message}`);
    }
  },

  // Convertir formato
  convertContent: async (content, targetFormat = 'xui', includeMetadata = true) => {
    return apiRequest('/m3u/convert', {
      method: 'POST',
      body: JSON.stringify({ 
        content, 
        target_format: targetFormat, 
        include_metadata: includeMetadata 
      })
    });
  }
};

/**
 * ❤️ API de Sistema
 */
export const systemAPI = {
  // Health check
  getHealth: async () => {
    return apiRequest('/health');
  },

  // Estadísticas generales
  getStats: async () => {
    return apiRequest('/stats');
  }
};

/**
 * 🧪 API de utilidades del sistema
 */
export const utilsAPI = {
  // Probar conectividad
  testConnectivity: async () => {
    return testApiConnectivity();
  },

  // Obtener información de configuración
  getConfigInfo: async () => {
    return apiRequest('/system/config');
  },

  // Health check
  healthCheck: async () => {
    return apiRequest('/health');
  }
};

/**
 * 🔗 API combinada para facilitar uso
 */
export const api = {
  importAPI,
  seriesAPI,
  databaseAPI,
  tmdbAPI,
  m3uAPI,
  systemAPI,
  utilsAPI
};

export default api;
