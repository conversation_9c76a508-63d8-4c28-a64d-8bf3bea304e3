{"ast": null, "code": "var _jsxFileName = \"F:\\\\WORKSPACE\\\\XUI IMPORTER\\\\xui-importer\\\\src\\\\components\\\\ImportVODM3U.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Row, Col, Card, Button, Form, Alert, ProgressBar, Table, Badge } from 'react-bootstrap';\nimport BackendStatus from './BackendStatus';\nimport { checkSystemHealth } from '../utils/seriesLogic';\nimport { api, databaseAPI } from '../services/apiService';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ImportVODM3U = () => {\n  _s();\n  var _fileAnalysis$basic_a, _fileAnalysis$basic_a2, _fileAnalysis$basic_a3, _fileAnalysis$basic_a4, _fileAnalysis$basic_a5, _fileAnalysis$file_in, _fileAnalysis$parse_r, _fileAnalysis$parse_r2, _fileAnalysis$parse_r3;\n  const [selectedFile, setSelectedFile] = useState(null);\n  const [isImporting, setIsImporting] = useState(false);\n  const [importProgress, setImportProgress] = useState(0);\n  const [showAlert, setShowAlert] = useState(false);\n  const [alertMessage, setAlertMessage] = useState('');\n  const [alertType, setAlertType] = useState('info');\n\n  // Estados para backend\n  const [backendStatus, setBackendStatus] = useState('checking');\n  const [fileAnalysis, setFileAnalysis] = useState(null);\n  const [isProcessingFile, setIsProcessingFile] = useState(false);\n\n  // Configuración fija para VOD/Movies\n  const contentType = 'movie';\n  const [streamsServer, setStreamsServer] = useState('');\n  const [sourceConfig, setSourceConfig] = useState({\n    directSource: true,\n    directProxy: false,\n    loadBalancing: false\n  });\n  const [selectedCategories, setSelectedCategories] = useState([]);\n\n  // Estados para datos dinámicos del backend\n  const [availableServers, setAvailableServers] = useState([]);\n  const [existingCategories, setExistingCategories] = useState([]);\n  const checkBackendStatus = async () => {\n    try {\n      const health = await checkSystemHealth();\n      setBackendStatus(health.success ? 'connected' : 'error');\n      if (!health.success) {\n        displayAlert('warning', 'Backend no disponible. Funcionando en modo offline.');\n      }\n    } catch (error) {\n      setBackendStatus('error');\n      displayAlert('danger', 'No se puede conectar al backend');\n    }\n  };\n  const loadInitialData = async () => {\n    try {\n      console.log(`🔄 Cargando datos iniciales. Backend status: ${backendStatus}`);\n\n      // Cargar servidores y categorías desde backend si está disponible\n      if (backendStatus === 'connected') {\n        console.log('✅ Backend conectado, cargando datos reales...');\n        await loadRealServers();\n        await loadRealCategories();\n      } else {\n        console.log('⚠️ Backend no conectado, usando datos mock...');\n        // Fallback a mock data si no hay conexión\n        loadMockData();\n      }\n    } catch (error) {\n      console.error('Error cargando datos iniciales:', error);\n      if (window.debugLog) {\n        window.debugLog('error', `Error cargando datos iniciales: ${error.message}`);\n      }\n      // Fallback a mock data en caso de error\n      loadMockData();\n    }\n  };\n\n  // Cargar servidores reales desde la base de datos\n  const loadRealServers = async () => {\n    try {\n      console.log('🔄 Iniciando carga de servidores reales...');\n      const response = await fetch('http://localhost:5001/api/database/streaming-servers');\n      console.log('📡 Respuesta del servidor:', response.status, response.statusText);\n      const result = await response.json();\n      console.log('📊 Datos recibidos:', result);\n      if (result.success && result.data) {\n        const servers = result.data.map(server => ({\n          id: server.server_id,\n          name: server.server_name || `Server ${server.server_id}`,\n          ip: server.server_ip || 'Unknown IP',\n          load: `${server.total_streams || 0} streams`,\n          // Mostrar cantidad de streams como \"carga\"\n          total_streams: server.total_streams || 0,\n          status: server.server_status === 1 ? 'Active' : 'Inactive'\n        }));\n        console.log('🖥️ Servidores mapeados:', servers);\n        setAvailableServers(servers);\n        console.log('✅ Estado actualizado con', servers.length, 'servidores');\n        if (window.debugLog) {\n          window.debugLog('success', `✅ Cargados ${servers.length} servidores reales desde BD`);\n        }\n      } else {\n        console.error('❌ Respuesta no exitosa:', result);\n        throw new Error(result.error || 'No se pudieron cargar servidores');\n      }\n    } catch (error) {\n      console.error('❌ Error cargando servidores:', error);\n      if (window.debugLog) {\n        window.debugLog('error', `❌ Error cargando servidores: ${error.message}`);\n      }\n      throw error;\n    }\n  };\n\n  // Estados para manejo de categorías\n  const [categorySearch, setCategorySearch] = useState('');\n  const [allCategories, setAllCategories] = useState([]);\n  const [filteredCategories, setFilteredCategories] = useState([]);\n  const [categoriesLoading, setCategoriesLoading] = useState(false);\n\n  // Cargar TODAS las categorías desde la base de datos con nombres reales\n  const loadRealCategories = async () => {\n    try {\n      setCategoriesLoading(true);\n      console.log('🔄 Cargando TODAS las categorías con nombres reales...');\n      const result = await databaseAPI.getCategories();\n      if (result.success && result.categories) {\n        const categories = result.categories.map(cat => ({\n          id: cat.id,\n          name: cat.name,\n          usage_count: cat.usage_count,\n          category_type: cat.category_type,\n          has_real_name: cat.has_real_name,\n          category_id_raw: `[${cat.id}]` // Formato JSON array\n        }));\n        setAllCategories(categories);\n        setFilteredCategories(categories);\n        setExistingCategories(categories);\n        console.log('✅ Categorías cargadas:', categories.length);\n        if (window.debugLog) {\n          window.debugLog('success', `✅ Cargadas ${categories.length} categorías reales desde BD`);\n        }\n      } else {\n        throw new Error('No se pudieron cargar categorías');\n      }\n    } catch (error) {\n      console.error('Error cargando categorías:', error);\n      if (window.debugLog) {\n        window.debugLog('error', `❌ Error cargando categorías: ${error.message}`);\n      }\n      // Fallback a categorías mock\n      loadMockCategories();\n    } finally {\n      setCategoriesLoading(false);\n    }\n  };\n\n  // Filtrar categorías basándose en la búsqueda\n  const filterCategories = searchTerm => {\n    if (!searchTerm.trim()) {\n      setFilteredCategories(allCategories);\n      return;\n    }\n    const searchLower = searchTerm.toLowerCase();\n    const filtered = allCategories.filter(cat => cat.name.toLowerCase().includes(searchLower) || cat.id.toString().includes(searchTerm) || cat.category_type && cat.category_type.toLowerCase().includes(searchLower));\n    setFilteredCategories(filtered);\n  };\n\n  // Manejar cambio en búsqueda de categorías\n  const handleCategorySearch = e => {\n    const value = e.target.value;\n    setCategorySearch(value);\n    filterCategories(value);\n  };\n\n  // Cargar categorías mock como fallback\n  const loadMockCategories = () => {\n    const mockCategories = [{\n      id: 1964,\n      name: 'Películas Generales',\n      usage_count: 2279\n    }, {\n      id: 1763,\n      name: 'Acción',\n      usage_count: 2242\n    }, {\n      id: 1762,\n      name: 'Drama',\n      usage_count: 2142\n    }, {\n      id: 1767,\n      name: 'Comedia',\n      usage_count: 1333\n    }, {\n      id: 1855,\n      name: 'Terror/Horror',\n      usage_count: 1200\n    }];\n    setAllCategories(mockCategories);\n    setFilteredCategories(mockCategories);\n    setExistingCategories(mockCategories);\n    console.log('⚠️ Usando categorías mock:', mockCategories);\n  };\n\n  // Detectar tipo de categoría basado en el nombre\n  const detectCategoryType = categoryName => {\n    const name = categoryName.toLowerCase();\n    if (name.includes('movie') || name.includes('film') || name.includes('cinema')) {\n      return 'movie';\n    } else if (name.includes('series') || name.includes('show') || name.includes('drama')) {\n      return 'series';\n    } else if (name.includes('live') || name.includes('tv') || name.includes('channel') || name.includes('news') || name.includes('sport')) {\n      return 'live';\n    } else if (name.includes('radio') || name.includes('music') || name.includes('fm')) {\n      return 'radio';\n    }\n    return 'movie'; // Default a movie para VOD si no se puede detectar\n  };\n\n  // Datos mock como fallback\n  const loadMockData = () => {\n    setAvailableServers([{\n      id: 1,\n      name: 'Main Server US',\n      ip: '*************',\n      load: '45%'\n    }, {\n      id: 2,\n      name: 'EU Server',\n      ip: '*************',\n      load: '32%'\n    }, {\n      id: 3,\n      name: 'Asia Server',\n      ip: '*************',\n      load: '67%'\n    }, {\n      id: 4,\n      name: 'Backup Server',\n      ip: '*************',\n      load: '12%'\n    }]);\n    setExistingCategories([{\n      id: 1,\n      name: 'Action Movies',\n      type: 'movie'\n    }, {\n      id: 2,\n      name: 'Comedy Movies',\n      type: 'movie'\n    }, {\n      id: 3,\n      name: 'Drama Movies',\n      type: 'movie'\n    }, {\n      id: 4,\n      name: 'Horror Movies',\n      type: 'movie'\n    }, {\n      id: 5,\n      name: 'Sci-Fi Movies',\n      type: 'movie'\n    }, {\n      id: 6,\n      name: 'Documentary',\n      type: 'movie'\n    }, {\n      id: 7,\n      name: 'Animation',\n      type: 'movie'\n    }, {\n      id: 8,\n      name: 'Thriller',\n      type: 'movie'\n    }, {\n      id: 9,\n      name: 'Romance',\n      type: 'movie'\n    }]);\n    if (window.debugLog) {\n      window.debugLog('warning', '⚠️ Usando datos mock - backend no disponible');\n    }\n  };\n  const showAlertMessage = (message, type = 'info') => {\n    setAlertMessage(message);\n    setAlertType(type);\n    setShowAlert(true);\n    setTimeout(() => setShowAlert(false), 5000);\n  };\n\n  // Helper function to show alert with better formatting\n  const displayAlert = (type, message) => {\n    showAlertMessage(message, type);\n  };\n\n  // File handling functions\n  const handleBrowseFiles = () => {\n    const fileInput = document.createElement('input');\n    fileInput.type = 'file';\n    fileInput.accept = '.m3u,.m3u8';\n    fileInput.onchange = e => {\n      const file = e.target.files[0];\n      if (file) {\n        handleFileSelect({\n          target: {\n            files: [file]\n          }\n        });\n      }\n    };\n    fileInput.click();\n  };\n  const handleAnalyzeFile = async () => {\n    if (!selectedFile) return;\n    setIsProcessingFile(true);\n    setFileAnalysis(null);\n    try {\n      displayAlert('info', 'Analizando archivo M3U...');\n      const response = await api.m3uAPI.analyzeFile(selectedFile);\n      if (response.success) {\n        var _response$data$basic_;\n        setFileAnalysis(response.data);\n\n        // Análisis completado - configurado para VOD/Movies\n        displayAlert('success', `✅ Archivo analizado correctamente. Se detectaron ${((_response$data$basic_ = response.data.basic_analysis) === null || _response$data$basic_ === void 0 ? void 0 : _response$data$basic_.estimated_entries) || 0} entradas. Configurado para importar como películas/VOD.`);\n      } else {\n        throw new Error(response.error || 'Error analizando archivo');\n      }\n    } catch (error) {\n      console.error('Error analyzing file:', error);\n      displayAlert('danger', `❌ Error analizando archivo: ${error.message}`);\n    } finally {\n      setIsProcessingFile(false);\n    }\n  };\n  const handleClearAnalysis = () => {\n    setFileAnalysis(null);\n    setSelectedFile(null);\n    // Content type fijo para VOD\n    displayAlert('info', 'Análisis limpiado. Selecciona un nuevo archivo.');\n  };\n\n  // Verificar estado del backend al cargar\n  useEffect(() => {\n    checkBackendStatus();\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n\n  // Cargar datos cuando el backend status cambie\n  useEffect(() => {\n    if (backendStatus !== 'checking') {\n      loadInitialData();\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [backendStatus]);\n  const handleFileSelect = async event => {\n    const file = event.target.files[0];\n    setSelectedFile(file);\n    setFileAnalysis(null);\n    if (file) {\n      displayAlert('info', `Archivo seleccionado: ${file.name} (${(file.size / 1024 / 1024).toFixed(2)} MB)`);\n\n      // Solo mostrar información básica, el análisis se hace manualmente\n      if (window.debugLog) {\n        window.debugLog(`📁 File selected: ${file.name}`, 'info');\n        window.debugLog(`📊 File size: ${(file.size / 1024 / 1024).toFixed(2)} MB`, 'info');\n      }\n    }\n  };\n  const handleImport = async () => {\n    if (!selectedFile || !streamsServer) {\n      displayAlert('warning', '⚠️ Por favor completa todos los campos requeridos (archivo y servidor).');\n      return;\n    }\n    if (!fileAnalysis) {\n      displayAlert('warning', '⚠️ Por favor analiza el archivo antes de importar.');\n      return;\n    }\n    if (window.debugLog) {\n      window.debugLog(`📥 Starting VOD import of ${selectedFile.name}`, 'info');\n      window.debugLog(`📊 File size: ${(selectedFile.size / 1024 / 1024).toFixed(2)} MB`, 'info');\n      window.debugLog(`🎯 Content type: ${contentType}`, 'info');\n      window.debugLog(`🖥️ Target server: ${streamsServer}`, 'info');\n    }\n    setIsImporting(true);\n    setImportProgress(0);\n    try {\n      // Preparar configuración de importación para VOD\n      const importConfig = {\n        contentType,\n        streamsServer,\n        sourceConfig,\n        categories: selectedCategories,\n        tmdbEnabled: true,\n        autoAssignCategories: true\n      };\n      displayAlert('info', '🔍 Iniciando proceso de importación VOD...');\n      setImportProgress(10);\n\n      // Paso 1: Subir archivo\n      const formData = new FormData();\n      formData.append('file', selectedFile);\n      formData.append('config', JSON.stringify(importConfig));\n      if (window.debugLog) {\n        window.debugLog('📤 Uploading VOD file to backend...', 'info');\n      }\n\n      // Paso 1: Analizar archivo M3U\n      const analyzeResponse = await api.m3uAPI.analyzeFile(selectedFile);\n      setImportProgress(30);\n      if (!analyzeResponse.success) {\n        throw new Error(analyzeResponse.error || 'Error analyzing file');\n      }\n      displayAlert('info', '🎯 Archivo subido, procesando contenido VOD...');\n      // Paso 2: Leer contenido del archivo para parsear películas\n      const fileContent = await new Promise((resolve, reject) => {\n        const reader = new FileReader();\n        reader.onload = e => resolve(e.target.result);\n        reader.onerror = () => reject(new Error('Error reading file'));\n        reader.readAsText(selectedFile);\n      });\n      setImportProgress(40);\n\n      // Paso 3: Parsear contenido del M3U como películas/VOD\n      const parseResponse = await fetch('http://localhost:5001/api/import/parse-movies', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          m3uContent: fileContent\n        })\n      });\n      const parseResult = await parseResponse.json();\n      if (!parseResult.success) {\n        throw new Error(parseResult.error || 'Error parsing VOD content');\n      }\n      setImportProgress(60);\n\n      // Paso 4: Importar películas a la base de datos (tabla streams con type=2)\n      const importPayload = {\n        movies: parseResult.data.movies,\n        server_id: parseInt(streamsServer),\n        category_id: selectedCategories.length > 0 ? parseInt(selectedCategories[0]) : null,\n        tmdb_search: true // Usar integración TMDB existente\n      };\n      const importResponse = await fetch('http://localhost:5001/api/import/movies', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify(importPayload)\n      });\n      const importResult = await importResponse.json();\n\n      // Debug: Log de la respuesta completa\n      console.log('🔍 VOD Import Response:', importResult);\n      if (!importResult.success) {\n        throw new Error(importResult.error || 'Error importing VOD content');\n      }\n\n      // Paso 5: Finalizar importación\n      setImportProgress(100);\n\n      // Mostrar estadísticas de películas (acceso seguro)\n      const stats = importResult.data || importResult;\n      const successMessage = `✅ Importación VOD completada exitosamente!\\n📊 Estadísticas:\\n• ${stats.imported || 0} elementos importados\\n• ${stats.errors || 0} errores\\n• ${stats.movies_created || 0} películas creadas\\n• ${stats.metadata_enriched || 0} con metadata TMDB`;\n      displayAlert('success', successMessage);\n      if (window.debugLog) {\n        window.debugLog(`✅ VOD Import completed successfully: ${selectedFile.name}`, 'success');\n        window.debugLog(`📊 Stats: ${JSON.stringify(importResult)}`, 'info');\n      }\n\n      // Limpiar estado después de importación exitosa\n      setTimeout(() => {\n        setSelectedFile(null);\n        setFileAnalysis(null);\n        // Content type fijo para VOD\n        setStreamsServer('');\n        setSelectedCategories([]);\n      }, 3000);\n    } catch (error) {\n      console.error('VOD Import error:', error);\n      displayAlert('danger', `❌ Error durante la importación VOD: ${error.message}`);\n      if (window.debugLog) {\n        window.debugLog(`❌ VOD Import failed: ${error.message}`, 'error');\n      }\n    } finally {\n      setIsImporting(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      width: '100%',\n      maxWidth: 'none'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"d-flex justify-content-between align-items-center mb-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"text-primary\",\n        children: \"\\uD83C\\uDFAC Import VOD M3U Files\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 473,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(BackendStatus, {\n        status: backendStatus,\n        onRetry: checkBackendStatus\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 474,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 472,\n      columnNumber: 7\n    }, this), showAlert && /*#__PURE__*/_jsxDEV(Alert, {\n      variant: alertType,\n      dismissible: true,\n      onClose: () => setShowAlert(false),\n      children: [/*#__PURE__*/_jsxDEV(Alert.Heading, {\n        children: [alertType === 'success' && '✅ Success!', alertType === 'danger' && '❌ Error!', alertType === 'warning' && '⚠️ Warning!', alertType === 'info' && 'ℹ️ Information']\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 482,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: alertMessage\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 488,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 481,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      className: \"mb-4\",\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        lg: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"shadow-sm h-100\",\n          children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n            className: \"bg-primary text-white d-flex justify-content-between align-items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n              className: \"mb-0\",\n              children: \"\\uD83D\\uDCC2 File Upload\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 496,\n              columnNumber: 15\n            }, this), backendStatus === 'connected' && /*#__PURE__*/_jsxDEV(Badge, {\n              bg: \"success\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"bi bi-cloud-check\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 499,\n                columnNumber: 19\n              }, this), \" Backend Ready\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 498,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 495,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n            children: /*#__PURE__*/_jsxDEV(Form, {\n              children: [/*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Select M3U File\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 506,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  type: \"file\",\n                  accept: \".m3u,.m3u8\",\n                  onChange: handleFileSelect,\n                  disabled: isImporting\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 507,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Text, {\n                  className: \"text-muted\",\n                  children: \"Supported formats: .m3u, .m3u8 (Max size: 50MB)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 513,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 505,\n                columnNumber: 17\n              }, this), selectedFile && /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(Alert, {\n                  variant: \"info\",\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Selected File:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 521,\n                    columnNumber: 23\n                  }, this), \" \", selectedFile.name, /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 521,\n                    columnNumber: 74\n                  }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Size:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 522,\n                    columnNumber: 23\n                  }, this), \" \", (selectedFile.size / 1024 / 1024).toFixed(2), \" MB\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 520,\n                  columnNumber: 21\n                }, this), isProcessingFile && /*#__PURE__*/_jsxDEV(Alert, {\n                  variant: \"secondary\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"d-flex align-items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"spinner-border spinner-border-sm me-2\",\n                      role: \"status\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 528,\n                      columnNumber: 27\n                    }, this), \"Analizando archivo...\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 527,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 526,\n                  columnNumber: 23\n                }, this), fileAnalysis && !isProcessingFile && /*#__PURE__*/_jsxDEV(Alert, {\n                  variant: \"success\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                    children: \"\\uD83D\\uDCCA An\\xE1lisis del Archivo\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 536,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Row, {\n                    children: [/*#__PURE__*/_jsxDEV(Col, {\n                      md: 6,\n                      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: \"Total Lines:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 539,\n                        columnNumber: 29\n                      }, this), \" \", ((_fileAnalysis$basic_a = fileAnalysis.basic_analysis) === null || _fileAnalysis$basic_a === void 0 ? void 0 : _fileAnalysis$basic_a.total_lines) || 0, /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 539,\n                        columnNumber: 106\n                      }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: \"EXTINF Entries:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 540,\n                        columnNumber: 29\n                      }, this), \" \", ((_fileAnalysis$basic_a2 = fileAnalysis.basic_analysis) === null || _fileAnalysis$basic_a2 === void 0 ? void 0 : _fileAnalysis$basic_a2.extinf_lines) || 0, /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 540,\n                        columnNumber: 110\n                      }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: \"URL Entries:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 541,\n                        columnNumber: 29\n                      }, this), \" \", ((_fileAnalysis$basic_a3 = fileAnalysis.basic_analysis) === null || _fileAnalysis$basic_a3 === void 0 ? void 0 : _fileAnalysis$basic_a3.url_lines) || 0]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 538,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Col, {\n                      md: 6,\n                      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: \"Estimated Entries:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 544,\n                        columnNumber: 29\n                      }, this), \" \", ((_fileAnalysis$basic_a4 = fileAnalysis.basic_analysis) === null || _fileAnalysis$basic_a4 === void 0 ? void 0 : _fileAnalysis$basic_a4.estimated_entries) || 0, /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 544,\n                        columnNumber: 118\n                      }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: \"Valid M3U:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 545,\n                        columnNumber: 29\n                      }, this), \" \", (_fileAnalysis$basic_a5 = fileAnalysis.basic_analysis) !== null && _fileAnalysis$basic_a5 !== void 0 && _fileAnalysis$basic_a5.has_valid_m3u_header ? '✅ Yes' : '❌ No', /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 545,\n                        columnNumber: 127\n                      }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: \"File Size:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 546,\n                        columnNumber: 29\n                      }, this), \" \", ((_fileAnalysis$file_in = fileAnalysis.file_info) === null || _fileAnalysis$file_in === void 0 ? void 0 : _fileAnalysis$file_in.size_mb) || 0, \" MB\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 543,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 537,\n                    columnNumber: 25\n                  }, this), ((_fileAnalysis$parse_r = fileAnalysis.parse_results) === null || _fileAnalysis$parse_r === void 0 ? void 0 : (_fileAnalysis$parse_r2 = _fileAnalysis$parse_r.movies) === null || _fileAnalysis$parse_r2 === void 0 ? void 0 : _fileAnalysis$parse_r2.success) && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"mt-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Movies Detected:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 552,\n                      columnNumber: 29\n                    }, this), \" \", ((_fileAnalysis$parse_r3 = fileAnalysis.parse_results.movies.data) === null || _fileAnalysis$parse_r3 === void 0 ? void 0 : _fileAnalysis$parse_r3.length) || 0]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 551,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 535,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Button, {\n                  onClick: handleBrowseFiles,\n                  variant: \"primary\",\n                  disabled: isProcessingFile,\n                  children: \"Seleccionar Archivo M3U\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 561,\n                  columnNumber: 19\n                }, this), selectedFile && !fileAnalysis && /*#__PURE__*/_jsxDEV(Button, {\n                  onClick: handleAnalyzeFile,\n                  variant: \"info\",\n                  className: \"ms-2\",\n                  disabled: isProcessingFile,\n                  children: isProcessingFile ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"spinner-border spinner-border-sm me-2\",\n                      role: \"status\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 578,\n                      columnNumber: 27\n                    }, this), \"Analizando...\"]\n                  }, void 0, true) : 'Analizar Archivo'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 570,\n                  columnNumber: 21\n                }, this), fileAnalysis && /*#__PURE__*/_jsxDEV(Button, {\n                  onClick: handleClearAnalysis,\n                  variant: \"outline-secondary\",\n                  className: \"ms-2\",\n                  disabled: isProcessingFile,\n                  children: \"Limpiar An\\xE1lisis\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 588,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 560,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mb-3\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"alert alert-info\",\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"\\uD83C\\uDFAF Tipo de Contenido:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 602,\n                    columnNumber: 21\n                  }, this), \" \\uD83C\\uDFAC Pel\\xEDculas/VOD (fijo)\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 603,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                    children: \"Este importador est\\xE1 configurado espec\\xEDficamente para pel\\xEDculas y contenido VOD.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 604,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 601,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 600,\n                columnNumber: 17\n              }, this), true && /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex justify-content-between align-items-center mb-2\",\n                  children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                    className: \"mb-0\",\n                    children: \"\\uD83D\\uDDA5\\uFE0F Target Streams Server\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 611,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Button, {\n                    variant: \"outline-secondary\",\n                    size: \"sm\",\n                    onClick: loadRealServers,\n                    disabled: isImporting || backendStatus !== 'connected',\n                    children: \"\\uD83D\\uDD04 Refresh\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 612,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 610,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n                  value: streamsServer,\n                  onChange: e => setStreamsServer(e.target.value),\n                  disabled: isImporting,\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\",\n                    children: \"Select streams server...\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 626,\n                    columnNumber: 23\n                  }, this), availableServers.map(server => /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: server.id,\n                    children: [server.name, \" (\", server.ip, \") - \", server.load]\n                  }, server.id, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 628,\n                    columnNumber: 25\n                  }, this))]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 621,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Form.Text, {\n                  className: \"text-muted\",\n                  children: [\"Server where VOD content will be hosted and served from.\", availableServers.length > 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-success\",\n                    children: [\" \\u2705 \", availableServers.length, \" servers loaded\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 636,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 633,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 609,\n                columnNumber: 19\n              }, this), streamsServer && /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(Form.Group, {\n                  className: \"mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                    children: \"\\uD83D\\uDD17 Source Configuration\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 645,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Form.Check, {\n                    type: \"checkbox\",\n                    label: \"\\u2705 Direct Source (recommended for better performance)\",\n                    checked: sourceConfig.directSource,\n                    onChange: e => setSourceConfig(prev => ({\n                      ...prev,\n                      directSource: e.target.checked\n                    })),\n                    disabled: isImporting\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 646,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Form.Check, {\n                    type: \"checkbox\",\n                    label: \"\\uD83D\\uDD04 Direct Proxy (for geo-restricted content)\",\n                    checked: sourceConfig.directProxy,\n                    onChange: e => setSourceConfig(prev => ({\n                      ...prev,\n                      directProxy: e.target.checked\n                    })),\n                    disabled: isImporting\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 656,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Form.Check, {\n                    type: \"checkbox\",\n                    label: \"\\u2696\\uFE0F Load Balancing (distribute across servers)\",\n                    checked: sourceConfig.loadBalancing,\n                    onChange: e => setSourceConfig(prev => ({\n                      ...prev,\n                      loadBalancing: e.target.checked\n                    })),\n                    disabled: isImporting\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 666,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 644,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n                  className: \"mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                    children: \"\\uD83C\\uDFF7\\uFE0F Categories Assignment\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 679,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(Form.Control, {\n                      type: \"text\",\n                      placeholder: \"Buscar categor\\xEDas por nombre, ID o contenido...\",\n                      value: categorySearch,\n                      onChange: handleCategorySearch,\n                      disabled: isImporting || categoriesLoading,\n                      className: \"mb-2\",\n                      size: \"sm\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 682,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        maxHeight: '200px',\n                        overflowY: 'auto',\n                        border: '1px solid #ddd',\n                        padding: '8px',\n                        borderRadius: '4px'\n                      },\n                      children: categoriesLoading ? /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-center text-muted\",\n                        children: /*#__PURE__*/_jsxDEV(\"small\", {\n                          children: \"Cargando categor\\xEDas...\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 696,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 695,\n                        columnNumber: 29\n                      }, this) : filteredCategories.length > 0 ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"text-muted mb-2\",\n                          children: /*#__PURE__*/_jsxDEV(\"small\", {\n                            children: [\"Mostrando \", filteredCategories.length, \" de \", allCategories.length, \" categor\\xEDas\", categorySearch && ` (filtradas por \"${categorySearch}\")`]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 701,\n                            columnNumber: 33\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 700,\n                          columnNumber: 31\n                        }, this), filteredCategories.slice(0, 100).map(category => /*#__PURE__*/_jsxDEV(Form.Check, {\n                          type: \"radio\",\n                          name: \"categorySelection\",\n                          label: /*#__PURE__*/_jsxDEV(\"div\", {\n                            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                              children: category.name\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 713,\n                              columnNumber: 39\n                            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                              className: \"text-muted\",\n                              children: [\" (ID: \", category.id, \")\"]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 714,\n                              columnNumber: 39\n                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                              style: {\n                                fontSize: '0.85em',\n                                color: '#666',\n                                marginTop: '2px'\n                              },\n                              children: [category.usage_count || 0, \" pel\\xEDculas\", category.category_type && /*#__PURE__*/_jsxDEV(\"span\", {\n                                children: [\" \\u2022 Tipo: \", category.category_type]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 718,\n                                columnNumber: 43\n                              }, this), category.has_real_name && /*#__PURE__*/_jsxDEV(\"span\", {\n                                className: \"text-success\",\n                                children: \" \\u2022 \\u2713 Nombre oficial\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 721,\n                                columnNumber: 43\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 715,\n                              columnNumber: 39\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 712,\n                            columnNumber: 37\n                          }, this),\n                          checked: selectedCategories.includes(category.id),\n                          onChange: e => {\n                            if (e.target.checked) {\n                              setSelectedCategories([category.id]); // Solo una categoría para VOD\n                            }\n                          },\n                          disabled: isImporting,\n                          className: \"mb-2\"\n                        }, category.id, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 707,\n                          columnNumber: 33\n                        }, this)), filteredCategories.length > 100 && /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"text-muted text-center mt-2\",\n                          children: /*#__PURE__*/_jsxDEV(\"small\", {\n                            children: \"Mostrando primeras 100 categor\\xEDas. Use la b\\xFAsqueda para filtrar m\\xE1s.\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 738,\n                            columnNumber: 35\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 737,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true) : /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-muted text-center\",\n                        children: /*#__PURE__*/_jsxDEV(\"small\", {\n                          children: categorySearch ? `No se encontraron categorías que coincidan con \"${categorySearch}\"` : 'No hay categorías disponibles'\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 744,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 743,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 693,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 680,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Form.Text, {\n                    className: \"text-muted\",\n                    children: \"Select existing movie categories or new ones will be created automatically\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 754,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 678,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true), /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"\\u2699\\uFE0F Import Settings\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 762,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Check, {\n                  type: \"checkbox\",\n                  label: \"\\uD83D\\uDD04 Auto-rename with TMDB data\",\n                  defaultChecked: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 763,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Check, {\n                  type: \"checkbox\",\n                  label: \"\\uD83D\\uDCC2 Auto-assign categories\",\n                  defaultChecked: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 768,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Check, {\n                  type: \"checkbox\",\n                  label: \"\\uD83C\\uDFAC Process movie metadata\",\n                  defaultChecked: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 773,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 761,\n                columnNumber: 17\n              }, this), isImporting && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Import Progress\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 782,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(ProgressBar, {\n                  now: importProgress,\n                  label: `${importProgress}%`,\n                  variant: importProgress === 100 ? 'success' : 'primary',\n                  animated: importProgress < 100\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 783,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 781,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"success\",\n                size: \"lg\",\n                onClick: handleImport,\n                disabled: !selectedFile || !fileAnalysis || !streamsServer || isImporting || isProcessingFile,\n                className: \"w-100\",\n                children: isImporting ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"spinner-border spinner-border-sm me-2\",\n                    role: \"status\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 801,\n                    columnNumber: 23\n                  }, this), \"Importando... \", importProgress, \"%\"]\n                }, void 0, true) : !selectedFile ? '📁 Selecciona un archivo M3U' : !fileAnalysis ? '🔍 Analiza el archivo primero' : !streamsServer ? '⚙️ Selecciona un servidor' : '🚀 Iniciar Importación VOD'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 792,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 504,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 503,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 494,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 493,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        lg: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"shadow-sm h-100\",\n          children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n            className: \"bg-info text-white\",\n            children: /*#__PURE__*/_jsxDEV(\"h5\", {\n              className: \"mb-0\",\n              children: \"\\u2139\\uFE0F VOD Import Guidelines\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 822,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 821,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n            children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n              children: \"\\uD83D\\uDCCB Supported VOD Content:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 825,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"\\uD83C\\uDFAC Movies:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 827,\n                  columnNumber: 21\n                }, this), \" Feature films and documentaries\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 827,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"\\uD83C\\uDFAD Short Films:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 828,\n                  columnNumber: 21\n                }, this), \" Independent and festival content\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 828,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"\\uD83D\\uDCFA Specials:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 829,\n                  columnNumber: 21\n                }, this), \" TV movies and special events\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 829,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"\\uD83C\\uDFAA Stand-up:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 830,\n                  columnNumber: 21\n                }, this), \" Comedy specials and performances\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 830,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 826,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h6\", {\n              children: \"\\u2699\\uFE0F Processing Features:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 833,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"\\uD83C\\uDFAF TMDB Integration:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 835,\n                  columnNumber: 21\n                }, this), \" Auto-fetch movie metadata\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 835,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"\\uD83C\\uDFF7\\uFE0F Category Assignment:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 836,\n                  columnNumber: 21\n                }, this), \" Smart movie categorization\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 836,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"\\uD83D\\uDDBC\\uFE0F Poster Download:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 837,\n                  columnNumber: 21\n                }, this), \" High-quality movie artwork\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 837,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"\\uD83D\\uDCDD Description Parsing:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 838,\n                  columnNumber: 21\n                }, this), \" Extract movie info\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 838,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 834,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h6\", {\n              children: \"\\u26A1 Performance Tips:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 841,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Files under 10MB import faster\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 843,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Use UTF-8 encoding for special characters\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 844,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Clean duplicate entries before import\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 845,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Ensure stable internet for TMDB metadata\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 846,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 842,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 824,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 820,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 819,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 492,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      children: /*#__PURE__*/_jsxDEV(Col, {\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"shadow-sm\",\n          children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n            className: \"bg-secondary text-white\",\n            children: /*#__PURE__*/_jsxDEV(\"h5\", {\n              className: \"mb-0\",\n              children: \"\\uD83D\\uDCCA Recent VOD Import Queue\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 857,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 856,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n            children: /*#__PURE__*/_jsxDEV(Table, {\n              striped: true,\n              hover: true,\n              children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"\\uD83D\\uDCC4 File\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 863,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"\\uD83D\\uDCC5 Queued\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 864,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"\\uD83D\\uDCCA Status\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 865,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"\\uD83C\\uDFAF Target\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 866,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"\\u26A1 Actions\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 867,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 862,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 861,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                children: [/*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"movies_collection.m3u\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 872,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 872,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: \"2025-07-15 16:30\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 873,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"badge bg-warning\",\n                      children: \"\\u23F3 Queued\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 874,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 874,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: \"Main Server\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 875,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: [/*#__PURE__*/_jsxDEV(Button, {\n                      size: \"sm\",\n                      variant: \"outline-primary\",\n                      className: \"me-1\",\n                      children: \"\\u25B6\\uFE0F\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 877,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Button, {\n                      size: \"sm\",\n                      variant: \"outline-danger\",\n                      children: \"\\u274C\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 878,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 876,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 871,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"action_movies.m3u\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 882,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 882,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: \"2025-07-15 16:25\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 883,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"badge bg-success\",\n                      children: \"\\u2705 Processing\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 884,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 884,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: \"Cloud Server\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 885,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(Button, {\n                      size: \"sm\",\n                      variant: \"outline-info\",\n                      children: \"\\uD83D\\uDCCA\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 887,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 886,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 881,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 870,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 860,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 859,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 855,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 854,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 853,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 471,\n    columnNumber: 5\n  }, this);\n};\n_s(ImportVODM3U, \"4x9fvcB4D3cNbFjL2rFkd82nXL8=\");\n_c = ImportVODM3U;\nexport default ImportVODM3U;\nvar _c;\n$RefreshReg$(_c, \"ImportVODM3U\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Row", "Col", "Card", "<PERSON><PERSON>", "Form", "<PERSON><PERSON>", "ProgressBar", "Table", "Badge", "BackendStatus", "checkSystemHealth", "api", "databaseAPI", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ImportVODM3U", "_s", "_fileAnalysis$basic_a", "_fileAnalysis$basic_a2", "_fileAnalysis$basic_a3", "_fileAnalysis$basic_a4", "_fileAnalysis$basic_a5", "_fileAnalysis$file_in", "_fileAnalysis$parse_r", "_fileAnalysis$parse_r2", "_fileAnalysis$parse_r3", "selectedFile", "setSelectedFile", "isImporting", "setIsImporting", "importProgress", "setImportProgress", "show<PERSON><PERSON><PERSON>", "setShowAlert", "alertMessage", "setAlertMessage", "alertType", "setAlertType", "backendStatus", "setBackendStatus", "fileAnalysis", "setFileAnalysis", "isProcessingFile", "setIsProcessingFile", "contentType", "streamsServer", "setStreamsServer", "sourceConfig", "setSourceConfig", "directSource", "directProxy", "loadBalancing", "selectedCategories", "setSelectedCategories", "availableServers", "setAvailableServers", "existingCategories", "setExistingCategories", "checkBackendStatus", "health", "success", "displayAlert", "error", "loadInitialData", "console", "log", "loadRealServers", "loadRealCategories", "loadMockData", "window", "debugLog", "message", "response", "fetch", "status", "statusText", "result", "json", "data", "servers", "map", "server", "id", "server_id", "name", "server_name", "ip", "server_ip", "load", "total_streams", "server_status", "length", "Error", "categorySearch", "setCategorySearch", "allCategories", "setAllCategories", "filteredCategories", "setFilteredCategories", "categoriesLoading", "setCategoriesLoading", "getCategories", "categories", "cat", "usage_count", "category_type", "has_real_name", "category_id_raw", "loadMockCategories", "filterCategories", "searchTerm", "trim", "searchLower", "toLowerCase", "filtered", "filter", "includes", "toString", "handleCategorySearch", "e", "value", "target", "mockCategories", "detectCategoryType", "categoryName", "type", "showAlertMessage", "setTimeout", "handleBrowseFiles", "fileInput", "document", "createElement", "accept", "onchange", "file", "files", "handleFileSelect", "click", "handleAnalyzeFile", "m3uAPI", "analyzeFile", "_response$data$basic_", "basic_analysis", "estimated_entries", "handleClearAnalysis", "event", "size", "toFixed", "handleImport", "importConfig", "tmdbEnabled", "autoAssignCategories", "formData", "FormData", "append", "JSON", "stringify", "analyzeResponse", "fileContent", "Promise", "resolve", "reject", "reader", "FileReader", "onload", "onerror", "readAsText", "parseResponse", "method", "headers", "body", "m3uContent", "parseResult", "importPayload", "movies", "parseInt", "category_id", "tmdb_search", "importResponse", "importResult", "stats", "successMessage", "imported", "errors", "movies_created", "metadata_enriched", "style", "width", "max<PERSON><PERSON><PERSON>", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onRetry", "variant", "dismissible", "onClose", "Heading", "lg", "Header", "bg", "Body", "Group", "Label", "Control", "onChange", "disabled", "Text", "role", "md", "total_lines", "extinf_lines", "url_lines", "has_valid_m3u_header", "file_info", "size_mb", "parse_results", "onClick", "Select", "Check", "label", "checked", "prev", "placeholder", "maxHeight", "overflowY", "border", "padding", "borderRadius", "slice", "category", "fontSize", "color", "marginTop", "defaultChecked", "now", "animated", "striped", "hover", "_c", "$RefreshReg$"], "sources": ["F:/WORKSPACE/XUI IMPORTER/xui-importer/src/components/ImportVODM3U.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Row, Col, Card, Button, Form, Alert, ProgressBar, Table, Badge } from 'react-bootstrap';\nimport BackendStatus from './BackendStatus';\n\nimport {\n  checkSystemHealth\n} from '../utils/seriesLogic';\nimport { api, databaseAPI } from '../services/apiService';\n\nconst ImportVODM3U = () => {\n  const [selectedFile, setSelectedFile] = useState(null);\n  const [isImporting, setIsImporting] = useState(false);\n  const [importProgress, setImportProgress] = useState(0);\n  const [showAlert, setShowAlert] = useState(false);\n  const [alertMessage, setAlertMessage] = useState('');\n  const [alertType, setAlertType] = useState('info');\n  \n  // Estados para backend\n  const [backendStatus, setBackendStatus] = useState('checking');\n  const [fileAnalysis, setFileAnalysis] = useState(null);\n  const [isProcessingFile, setIsProcessingFile] = useState(false);\n  \n  // Configuración fija para VOD/Movies\n  const contentType = 'movie';\n  const [streamsServer, setStreamsServer] = useState('');\n  const [sourceConfig, setSourceConfig] = useState({\n    directSource: true,\n    directProxy: false,\n    loadBalancing: false\n  });\n  const [selectedCategories, setSelectedCategories] = useState([]);\n\n  // Estados para datos dinámicos del backend\n  const [availableServers, setAvailableServers] = useState([]);\n  const [existingCategories, setExistingCategories] = useState([]);\n\n  const checkBackendStatus = async () => {\n    try {\n      const health = await checkSystemHealth();\n      setBackendStatus(health.success ? 'connected' : 'error');\n      \n      if (!health.success) {\n        displayAlert('warning', 'Backend no disponible. Funcionando en modo offline.');\n      }\n    } catch (error) {\n      setBackendStatus('error');\n      displayAlert('danger', 'No se puede conectar al backend');\n    }\n  };\n\n  const loadInitialData = async () => {\n    try {\n      console.log(`🔄 Cargando datos iniciales. Backend status: ${backendStatus}`);\n\n      // Cargar servidores y categorías desde backend si está disponible\n      if (backendStatus === 'connected') {\n        console.log('✅ Backend conectado, cargando datos reales...');\n        await loadRealServers();\n        await loadRealCategories();\n      } else {\n        console.log('⚠️ Backend no conectado, usando datos mock...');\n        // Fallback a mock data si no hay conexión\n        loadMockData();\n      }\n\n    } catch (error) {\n      console.error('Error cargando datos iniciales:', error);\n      if (window.debugLog) {\n        window.debugLog('error', `Error cargando datos iniciales: ${error.message}`);\n      }\n      // Fallback a mock data en caso de error\n      loadMockData();\n    }\n  };\n\n  // Cargar servidores reales desde la base de datos\n  const loadRealServers = async () => {\n    try {\n      console.log('🔄 Iniciando carga de servidores reales...');\n\n      const response = await fetch('http://localhost:5001/api/database/streaming-servers');\n      console.log('📡 Respuesta del servidor:', response.status, response.statusText);\n\n      const result = await response.json();\n      console.log('📊 Datos recibidos:', result);\n\n      if (result.success && result.data) {\n        const servers = result.data.map(server => ({\n          id: server.server_id,\n          name: server.server_name || `Server ${server.server_id}`,\n          ip: server.server_ip || 'Unknown IP',\n          load: `${server.total_streams || 0} streams`, // Mostrar cantidad de streams como \"carga\"\n          total_streams: server.total_streams || 0,\n          status: server.server_status === 1 ? 'Active' : 'Inactive'\n        }));\n\n        console.log('🖥️ Servidores mapeados:', servers);\n        setAvailableServers(servers);\n        console.log('✅ Estado actualizado con', servers.length, 'servidores');\n\n        if (window.debugLog) {\n          window.debugLog('success', `✅ Cargados ${servers.length} servidores reales desde BD`);\n        }\n      } else {\n        console.error('❌ Respuesta no exitosa:', result);\n        throw new Error(result.error || 'No se pudieron cargar servidores');\n      }\n    } catch (error) {\n      console.error('❌ Error cargando servidores:', error);\n      if (window.debugLog) {\n        window.debugLog('error', `❌ Error cargando servidores: ${error.message}`);\n      }\n      throw error;\n    }\n  };\n\n  // Estados para manejo de categorías\n  const [categorySearch, setCategorySearch] = useState('');\n  const [allCategories, setAllCategories] = useState([]);\n  const [filteredCategories, setFilteredCategories] = useState([]);\n  const [categoriesLoading, setCategoriesLoading] = useState(false);\n\n  // Cargar TODAS las categorías desde la base de datos con nombres reales\n  const loadRealCategories = async () => {\n    try {\n      setCategoriesLoading(true);\n      console.log('🔄 Cargando TODAS las categorías con nombres reales...');\n      const result = await databaseAPI.getCategories();\n\n      if (result.success && result.categories) {\n        const categories = result.categories.map(cat => ({\n          id: cat.id,\n          name: cat.name,\n          usage_count: cat.usage_count,\n          category_type: cat.category_type,\n          has_real_name: cat.has_real_name,\n          category_id_raw: `[${cat.id}]` // Formato JSON array\n        }));\n\n        setAllCategories(categories);\n        setFilteredCategories(categories);\n        setExistingCategories(categories);\n        console.log('✅ Categorías cargadas:', categories.length);\n\n        if (window.debugLog) {\n          window.debugLog('success', `✅ Cargadas ${categories.length} categorías reales desde BD`);\n        }\n      } else {\n        throw new Error('No se pudieron cargar categorías');\n      }\n    } catch (error) {\n      console.error('Error cargando categorías:', error);\n      if (window.debugLog) {\n        window.debugLog('error', `❌ Error cargando categorías: ${error.message}`);\n      }\n      // Fallback a categorías mock\n      loadMockCategories();\n    } finally {\n      setCategoriesLoading(false);\n    }\n  };\n\n  // Filtrar categorías basándose en la búsqueda\n  const filterCategories = (searchTerm) => {\n    if (!searchTerm.trim()) {\n      setFilteredCategories(allCategories);\n      return;\n    }\n\n    const searchLower = searchTerm.toLowerCase();\n    const filtered = allCategories.filter(cat =>\n      cat.name.toLowerCase().includes(searchLower) ||\n      cat.id.toString().includes(searchTerm) ||\n      (cat.category_type && cat.category_type.toLowerCase().includes(searchLower))\n    );\n\n    setFilteredCategories(filtered);\n  };\n\n  // Manejar cambio en búsqueda de categorías\n  const handleCategorySearch = (e) => {\n    const value = e.target.value;\n    setCategorySearch(value);\n    filterCategories(value);\n  };\n\n  // Cargar categorías mock como fallback\n  const loadMockCategories = () => {\n    const mockCategories = [\n      { id: 1964, name: 'Películas Generales', usage_count: 2279 },\n      { id: 1763, name: 'Acción', usage_count: 2242 },\n      { id: 1762, name: 'Drama', usage_count: 2142 },\n      { id: 1767, name: 'Comedia', usage_count: 1333 },\n      { id: 1855, name: 'Terror/Horror', usage_count: 1200 }\n    ];\n    setAllCategories(mockCategories);\n    setFilteredCategories(mockCategories);\n    setExistingCategories(mockCategories);\n    console.log('⚠️ Usando categorías mock:', mockCategories);\n  };\n\n  // Detectar tipo de categoría basado en el nombre\n  const detectCategoryType = (categoryName) => {\n    const name = categoryName.toLowerCase();\n\n    if (name.includes('movie') || name.includes('film') || name.includes('cinema')) {\n      return 'movie';\n    } else if (name.includes('series') || name.includes('show') || name.includes('drama')) {\n      return 'series';\n    } else if (name.includes('live') || name.includes('tv') || name.includes('channel') || name.includes('news') || name.includes('sport')) {\n      return 'live';\n    } else if (name.includes('radio') || name.includes('music') || name.includes('fm')) {\n      return 'radio';\n    }\n\n    return 'movie'; // Default a movie para VOD si no se puede detectar\n  };\n\n  // Datos mock como fallback\n  const loadMockData = () => {\n    setAvailableServers([\n      { id: 1, name: 'Main Server US', ip: '*************', load: '45%' },\n      { id: 2, name: 'EU Server', ip: '*************', load: '32%' },\n      { id: 3, name: 'Asia Server', ip: '*************', load: '67%' },\n      { id: 4, name: 'Backup Server', ip: '*************', load: '12%' }\n    ]);\n\n    setExistingCategories([\n      { id: 1, name: 'Action Movies', type: 'movie' },\n      { id: 2, name: 'Comedy Movies', type: 'movie' },\n      { id: 3, name: 'Drama Movies', type: 'movie' },\n      { id: 4, name: 'Horror Movies', type: 'movie' },\n      { id: 5, name: 'Sci-Fi Movies', type: 'movie' },\n      { id: 6, name: 'Documentary', type: 'movie' },\n      { id: 7, name: 'Animation', type: 'movie' },\n      { id: 8, name: 'Thriller', type: 'movie' },\n      { id: 9, name: 'Romance', type: 'movie' }\n    ]);\n\n    if (window.debugLog) {\n      window.debugLog('warning', '⚠️ Usando datos mock - backend no disponible');\n    }\n  };\n\n  const showAlertMessage = (message, type = 'info') => {\n    setAlertMessage(message);\n    setAlertType(type);\n    setShowAlert(true);\n    setTimeout(() => setShowAlert(false), 5000);\n  };\n\n  // Helper function to show alert with better formatting\n  const displayAlert = (type, message) => {\n    showAlertMessage(message, type);\n  };\n\n  // File handling functions\n  const handleBrowseFiles = () => {\n    const fileInput = document.createElement('input');\n    fileInput.type = 'file';\n    fileInput.accept = '.m3u,.m3u8';\n    fileInput.onchange = (e) => {\n      const file = e.target.files[0];\n      if (file) {\n        handleFileSelect({ target: { files: [file] } });\n      }\n    };\n    fileInput.click();\n  };\n\n  const handleAnalyzeFile = async () => {\n    if (!selectedFile) return;\n    \n    setIsProcessingFile(true);\n    setFileAnalysis(null);\n    \n    try {\n      displayAlert('info', 'Analizando archivo M3U...');\n      \n      const response = await api.m3uAPI.analyzeFile(selectedFile);\n      \n      if (response.success) {\n        setFileAnalysis(response.data);\n\n        // Análisis completado - configurado para VOD/Movies\n        displayAlert('success', `✅ Archivo analizado correctamente. Se detectaron ${response.data.basic_analysis?.estimated_entries || 0} entradas. Configurado para importar como películas/VOD.`);\n      } else {\n        throw new Error(response.error || 'Error analizando archivo');\n      }\n    } catch (error) {\n      console.error('Error analyzing file:', error);\n      displayAlert('danger', `❌ Error analizando archivo: ${error.message}`);\n    } finally {\n      setIsProcessingFile(false);\n    }\n  };\n\n  const handleClearAnalysis = () => {\n    setFileAnalysis(null);\n    setSelectedFile(null);\n    // Content type fijo para VOD\n    displayAlert('info', 'Análisis limpiado. Selecciona un nuevo archivo.');\n  };\n\n  // Verificar estado del backend al cargar\n  useEffect(() => {\n    checkBackendStatus();\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n\n  // Cargar datos cuando el backend status cambie\n  useEffect(() => {\n    if (backendStatus !== 'checking') {\n      loadInitialData();\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [backendStatus]);\n\n  const handleFileSelect = async (event) => {\n    const file = event.target.files[0];\n    setSelectedFile(file);\n    setFileAnalysis(null);\n\n    if (file) {\n      displayAlert('info', `Archivo seleccionado: ${file.name} (${(file.size / 1024 / 1024).toFixed(2)} MB)`);\n\n      // Solo mostrar información básica, el análisis se hace manualmente\n      if (window.debugLog) {\n        window.debugLog(`📁 File selected: ${file.name}`, 'info');\n        window.debugLog(`📊 File size: ${(file.size / 1024 / 1024).toFixed(2)} MB`, 'info');\n      }\n    }\n  };\n\n  const handleImport = async () => {\n    if (!selectedFile || !streamsServer) {\n      displayAlert('warning', '⚠️ Por favor completa todos los campos requeridos (archivo y servidor).');\n      return;\n    }\n\n    if (!fileAnalysis) {\n      displayAlert('warning', '⚠️ Por favor analiza el archivo antes de importar.');\n      return;\n    }\n\n    if (window.debugLog) {\n      window.debugLog(`📥 Starting VOD import of ${selectedFile.name}`, 'info');\n      window.debugLog(`📊 File size: ${(selectedFile.size / 1024 / 1024).toFixed(2)} MB`, 'info');\n      window.debugLog(`🎯 Content type: ${contentType}`, 'info');\n      window.debugLog(`🖥️ Target server: ${streamsServer}`, 'info');\n    }\n\n    setIsImporting(true);\n    setImportProgress(0);\n\n    try {\n      // Preparar configuración de importación para VOD\n      const importConfig = {\n        contentType,\n        streamsServer,\n        sourceConfig,\n        categories: selectedCategories,\n        tmdbEnabled: true,\n        autoAssignCategories: true\n      };\n\n      displayAlert('info', '🔍 Iniciando proceso de importación VOD...');\n      setImportProgress(10);\n\n      // Paso 1: Subir archivo\n      const formData = new FormData();\n      formData.append('file', selectedFile);\n      formData.append('config', JSON.stringify(importConfig));\n\n      if (window.debugLog) {\n        window.debugLog('📤 Uploading VOD file to backend...', 'info');\n      }\n\n      // Paso 1: Analizar archivo M3U\n      const analyzeResponse = await api.m3uAPI.analyzeFile(selectedFile);\n      setImportProgress(30);\n\n      if (!analyzeResponse.success) {\n        throw new Error(analyzeResponse.error || 'Error analyzing file');\n      }\n\n      displayAlert('info', '🎯 Archivo subido, procesando contenido VOD...');\n      // Paso 2: Leer contenido del archivo para parsear películas\n      const fileContent = await new Promise((resolve, reject) => {\n        const reader = new FileReader();\n        reader.onload = (e) => resolve(e.target.result);\n        reader.onerror = () => reject(new Error('Error reading file'));\n        reader.readAsText(selectedFile);\n      });\n\n      setImportProgress(40);\n\n      // Paso 3: Parsear contenido del M3U como películas/VOD\n      const parseResponse = await fetch('http://localhost:5001/api/import/parse-movies', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({ m3uContent: fileContent })\n      });\n\n      const parseResult = await parseResponse.json();\n      if (!parseResult.success) {\n        throw new Error(parseResult.error || 'Error parsing VOD content');\n      }\n\n      setImportProgress(60);\n\n      // Paso 4: Importar películas a la base de datos (tabla streams con type=2)\n      const importPayload = {\n        movies: parseResult.data.movies,\n        server_id: parseInt(streamsServer),\n        category_id: selectedCategories.length > 0 ? parseInt(selectedCategories[0]) : null,\n        tmdb_search: true // Usar integración TMDB existente\n      };\n\n      const importResponse = await fetch('http://localhost:5001/api/import/movies', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify(importPayload)\n      });\n\n      const importResult = await importResponse.json();\n\n      // Debug: Log de la respuesta completa\n      console.log('🔍 VOD Import Response:', importResult);\n\n      if (!importResult.success) {\n        throw new Error(importResult.error || 'Error importing VOD content');\n      }\n\n      // Paso 5: Finalizar importación\n      setImportProgress(100);\n\n      // Mostrar estadísticas de películas (acceso seguro)\n      const stats = importResult.data || importResult;\n      const successMessage = `✅ Importación VOD completada exitosamente!\\n📊 Estadísticas:\\n• ${stats.imported || 0} elementos importados\\n• ${stats.errors || 0} errores\\n• ${stats.movies_created || 0} películas creadas\\n• ${stats.metadata_enriched || 0} con metadata TMDB`;\n\n      displayAlert('success', successMessage);\n\n      if (window.debugLog) {\n        window.debugLog(`✅ VOD Import completed successfully: ${selectedFile.name}`, 'success');\n        window.debugLog(`📊 Stats: ${JSON.stringify(importResult)}`, 'info');\n      }\n\n      // Limpiar estado después de importación exitosa\n      setTimeout(() => {\n        setSelectedFile(null);\n        setFileAnalysis(null);\n        // Content type fijo para VOD\n        setStreamsServer('');\n        setSelectedCategories([]);\n      }, 3000);\n\n    } catch (error) {\n      console.error('VOD Import error:', error);\n      displayAlert('danger', `❌ Error durante la importación VOD: ${error.message}`);\n\n      if (window.debugLog) {\n        window.debugLog(`❌ VOD Import failed: ${error.message}`, 'error');\n      }\n    } finally {\n      setIsImporting(false);\n    }\n  };\n\n  return (\n    <div style={{ width: '100%', maxWidth: 'none' }}>\n      <div className=\"d-flex justify-content-between align-items-center mb-4\">\n        <h1 className=\"text-primary\">🎬 Import VOD M3U Files</h1>\n        <BackendStatus\n          status={backendStatus}\n          onRetry={checkBackendStatus}\n        />\n      </div>\n\n      {showAlert && (\n        <Alert variant={alertType} dismissible onClose={() => setShowAlert(false)}>\n          <Alert.Heading>\n            {alertType === 'success' && '✅ Success!'}\n            {alertType === 'danger' && '❌ Error!'}\n            {alertType === 'warning' && '⚠️ Warning!'}\n            {alertType === 'info' && 'ℹ️ Information'}\n          </Alert.Heading>\n          <p>{alertMessage}</p>\n        </Alert>\n      )}\n\n      <Row className=\"mb-4\">\n        <Col lg={6}>\n          <Card className=\"shadow-sm h-100\">\n            <Card.Header className=\"bg-primary text-white d-flex justify-content-between align-items-center\">\n              <h5 className=\"mb-0\">📂 File Upload</h5>\n              {backendStatus === 'connected' && (\n                <Badge bg=\"success\">\n                  <i className=\"bi bi-cloud-check\"></i> Backend Ready\n                </Badge>\n              )}\n            </Card.Header>\n            <Card.Body>\n              <Form>\n                <Form.Group className=\"mb-3\">\n                  <Form.Label>Select M3U File</Form.Label>\n                  <Form.Control\n                    type=\"file\"\n                    accept=\".m3u,.m3u8\"\n                    onChange={handleFileSelect}\n                    disabled={isImporting}\n                  />\n                  <Form.Text className=\"text-muted\">\n                    Supported formats: .m3u, .m3u8 (Max size: 50MB)\n                  </Form.Text>\n                </Form.Group>\n\n                {selectedFile && (\n                  <>\n                    <Alert variant=\"info\">\n                      <strong>Selected File:</strong> {selectedFile.name}<br/>\n                      <strong>Size:</strong> {(selectedFile.size / 1024 / 1024).toFixed(2)} MB\n                    </Alert>\n\n                    {isProcessingFile && (\n                      <Alert variant=\"secondary\">\n                        <div className=\"d-flex align-items-center\">\n                          <div className=\"spinner-border spinner-border-sm me-2\" role=\"status\"></div>\n                          Analizando archivo...\n                        </div>\n                      </Alert>\n                    )}\n\n                    {fileAnalysis && !isProcessingFile && (\n                      <Alert variant=\"success\">\n                        <h6>📊 Análisis del Archivo</h6>\n                        <Row>\n                          <Col md={6}>\n                            <strong>Total Lines:</strong> {fileAnalysis.basic_analysis?.total_lines || 0}<br/>\n                            <strong>EXTINF Entries:</strong> {fileAnalysis.basic_analysis?.extinf_lines || 0}<br/>\n                            <strong>URL Entries:</strong> {fileAnalysis.basic_analysis?.url_lines || 0}\n                          </Col>\n                          <Col md={6}>\n                            <strong>Estimated Entries:</strong> {fileAnalysis.basic_analysis?.estimated_entries || 0}<br/>\n                            <strong>Valid M3U:</strong> {fileAnalysis.basic_analysis?.has_valid_m3u_header ? '✅ Yes' : '❌ No'}<br/>\n                            <strong>File Size:</strong> {fileAnalysis.file_info?.size_mb || 0} MB\n                          </Col>\n                        </Row>\n\n                        {fileAnalysis.parse_results?.movies?.success && (\n                          <div className=\"mt-2\">\n                            <strong>Movies Detected:</strong> {fileAnalysis.parse_results.movies.data?.length || 0}\n                          </div>\n                        )}\n                      </Alert>\n                    )}\n                  </>\n                )}\n\n                <div className=\"mb-3\">\n                  <Button\n                    onClick={handleBrowseFiles}\n                    variant=\"primary\"\n                    disabled={isProcessingFile}\n                  >\n                    Seleccionar Archivo M3U\n                  </Button>\n\n                  {selectedFile && !fileAnalysis && (\n                    <Button\n                      onClick={handleAnalyzeFile}\n                      variant=\"info\"\n                      className=\"ms-2\"\n                      disabled={isProcessingFile}\n                    >\n                      {isProcessingFile ? (\n                        <>\n                          <span className=\"spinner-border spinner-border-sm me-2\" role=\"status\"></span>\n                          Analizando...\n                        </>\n                      ) : (\n                        'Analizar Archivo'\n                      )}\n                    </Button>\n                  )}\n\n                  {fileAnalysis && (\n                    <Button\n                      onClick={handleClearAnalysis}\n                      variant=\"outline-secondary\"\n                      className=\"ms-2\"\n                      disabled={isProcessingFile}\n                    >\n                      Limpiar Análisis\n                    </Button>\n                  )}\n                </div>\n\n                {/* Content type fijo para VOD */}\n                <div className=\"mb-3\">\n                  <div className=\"alert alert-info\">\n                    <strong>🎯 Tipo de Contenido:</strong> 🎬 Películas/VOD (fijo)\n                    <br />\n                    <small>Este importador está configurado específicamente para películas y contenido VOD.</small>\n                  </div>\n                </div>\n\n                {true && (\n                  <Form.Group className=\"mb-3\">\n                    <div className=\"d-flex justify-content-between align-items-center mb-2\">\n                      <Form.Label className=\"mb-0\">🖥️ Target Streams Server</Form.Label>\n                      <Button\n                        variant=\"outline-secondary\"\n                        size=\"sm\"\n                        onClick={loadRealServers}\n                        disabled={isImporting || backendStatus !== 'connected'}\n                      >\n                        🔄 Refresh\n                      </Button>\n                    </div>\n                    <Form.Select\n                      value={streamsServer}\n                      onChange={(e) => setStreamsServer(e.target.value)}\n                      disabled={isImporting}\n                    >\n                      <option value=\"\">Select streams server...</option>\n                      {availableServers.map(server => (\n                        <option key={server.id} value={server.id}>\n                          {server.name} ({server.ip}) - {server.load}\n                        </option>\n                      ))}\n                    </Form.Select>\n                    <Form.Text className=\"text-muted\">\n                      Server where VOD content will be hosted and served from.\n                      {availableServers.length > 0 && (\n                        <span className=\"text-success\"> ✅ {availableServers.length} servers loaded</span>\n                      )}\n                    </Form.Text>\n                  </Form.Group>\n                )}\n\n                {streamsServer && (\n                  <>\n                    <Form.Group className=\"mb-3\">\n                      <Form.Label>🔗 Source Configuration</Form.Label>\n                      <Form.Check\n                        type=\"checkbox\"\n                        label=\"✅ Direct Source (recommended for better performance)\"\n                        checked={sourceConfig.directSource}\n                        onChange={(e) => setSourceConfig(prev => ({\n                          ...prev,\n                          directSource: e.target.checked\n                        }))}\n                        disabled={isImporting}\n                      />\n                      <Form.Check\n                        type=\"checkbox\"\n                        label=\"🔄 Direct Proxy (for geo-restricted content)\"\n                        checked={sourceConfig.directProxy}\n                        onChange={(e) => setSourceConfig(prev => ({\n                          ...prev,\n                          directProxy: e.target.checked\n                        }))}\n                        disabled={isImporting}\n                      />\n                      <Form.Check\n                        type=\"checkbox\"\n                        label=\"⚖️ Load Balancing (distribute across servers)\"\n                        checked={sourceConfig.loadBalancing}\n                        onChange={(e) => setSourceConfig(prev => ({\n                          ...prev,\n                          loadBalancing: e.target.checked\n                        }))}\n                        disabled={isImporting}\n                      />\n                    </Form.Group>\n\n                    <Form.Group className=\"mb-3\">\n                      <Form.Label>🏷️ Categories Assignment</Form.Label>\n                      <div>\n                        {/* Buscador de categorías */}\n                        <Form.Control\n                          type=\"text\"\n                          placeholder=\"Buscar categorías por nombre, ID o contenido...\"\n                          value={categorySearch}\n                          onChange={handleCategorySearch}\n                          disabled={isImporting || categoriesLoading}\n                          className=\"mb-2\"\n                          size=\"sm\"\n                        />\n\n                        {/* Lista de categorías */}\n                        <div style={{maxHeight: '200px', overflowY: 'auto', border: '1px solid #ddd', padding: '8px', borderRadius: '4px'}}>\n                          {categoriesLoading ? (\n                            <div className=\"text-center text-muted\">\n                              <small>Cargando categorías...</small>\n                            </div>\n                          ) : filteredCategories.length > 0 ? (\n                            <>\n                              <div className=\"text-muted mb-2\">\n                                <small>\n                                  Mostrando {filteredCategories.length} de {allCategories.length} categorías\n                                  {categorySearch && ` (filtradas por \"${categorySearch}\")`}\n                                </small>\n                              </div>\n                              {filteredCategories.slice(0, 100).map(category => (\n                                <Form.Check\n                                  key={category.id}\n                                  type=\"radio\"\n                                  name=\"categorySelection\"\n                                  label={\n                                    <div>\n                                      <strong>{category.name}</strong>\n                                      <span className=\"text-muted\"> (ID: {category.id})</span>\n                                      <div style={{fontSize: '0.85em', color: '#666', marginTop: '2px'}}>\n                                        {category.usage_count || 0} películas\n                                        {category.category_type && (\n                                          <span> • Tipo: {category.category_type}</span>\n                                        )}\n                                        {category.has_real_name && (\n                                          <span className=\"text-success\"> • ✓ Nombre oficial</span>\n                                        )}\n                                      </div>\n                                    </div>\n                                  }\n                                  checked={selectedCategories.includes(category.id)}\n                                  onChange={(e) => {\n                                    if (e.target.checked) {\n                                      setSelectedCategories([category.id]); // Solo una categoría para VOD\n                                    }\n                                  }}\n                                  disabled={isImporting}\n                                  className=\"mb-2\"\n                                />\n                              ))}\n                              {filteredCategories.length > 100 && (\n                                <div className=\"text-muted text-center mt-2\">\n                                  <small>Mostrando primeras 100 categorías. Use la búsqueda para filtrar más.</small>\n                                </div>\n                              )}\n                            </>\n                          ) : (\n                            <div className=\"text-muted text-center\">\n                              <small>\n                                {categorySearch ?\n                                  `No se encontraron categorías que coincidan con \"${categorySearch}\"` :\n                                  'No hay categorías disponibles'\n                                }\n                              </small>\n                            </div>\n                          )}\n                        </div>\n                      </div>\n                      <Form.Text className=\"text-muted\">\n                        Select existing movie categories or new ones will be created automatically\n                      </Form.Text>\n                    </Form.Group>\n                  </>\n                )}\n\n                <Form.Group className=\"mb-3\">\n                  <Form.Label>⚙️ Import Settings</Form.Label>\n                  <Form.Check\n                    type=\"checkbox\"\n                    label=\"🔄 Auto-rename with TMDB data\"\n                    defaultChecked\n                  />\n                  <Form.Check\n                    type=\"checkbox\"\n                    label=\"📂 Auto-assign categories\"\n                    defaultChecked\n                  />\n                  <Form.Check\n                    type=\"checkbox\"\n                    label=\"🎬 Process movie metadata\"\n                    defaultChecked\n                  />\n                </Form.Group>\n\n                {isImporting && (\n                  <div className=\"mb-3\">\n                    <Form.Label>Import Progress</Form.Label>\n                    <ProgressBar\n                      now={importProgress}\n                      label={`${importProgress}%`}\n                      variant={importProgress === 100 ? 'success' : 'primary'}\n                      animated={importProgress < 100}\n                    />\n                  </div>\n                )}\n\n                <Button\n                  variant=\"success\"\n                  size=\"lg\"\n                  onClick={handleImport}\n                  disabled={!selectedFile || !fileAnalysis || !streamsServer || isImporting || isProcessingFile}\n                  className=\"w-100\"\n                >\n                  {isImporting ? (\n                    <>\n                      <span className=\"spinner-border spinner-border-sm me-2\" role=\"status\"></span>\n                      Importando... {importProgress}%\n                    </>\n                  ) : !selectedFile ? (\n                    '📁 Selecciona un archivo M3U'\n                  ) : !fileAnalysis ? (\n                    '🔍 Analiza el archivo primero'\n                  ) : !streamsServer ? (\n                    '⚙️ Selecciona un servidor'\n                  ) : (\n                    '🚀 Iniciar Importación VOD'\n                  )}\n                </Button>\n              </Form>\n            </Card.Body>\n          </Card>\n        </Col>\n\n        <Col lg={6}>\n          <Card className=\"shadow-sm h-100\">\n            <Card.Header className=\"bg-info text-white\">\n              <h5 className=\"mb-0\">ℹ️ VOD Import Guidelines</h5>\n            </Card.Header>\n            <Card.Body>\n              <h6>📋 Supported VOD Content:</h6>\n              <ul>\n                <li><strong>🎬 Movies:</strong> Feature films and documentaries</li>\n                <li><strong>🎭 Short Films:</strong> Independent and festival content</li>\n                <li><strong>📺 Specials:</strong> TV movies and special events</li>\n                <li><strong>🎪 Stand-up:</strong> Comedy specials and performances</li>\n              </ul>\n\n              <h6>⚙️ Processing Features:</h6>\n              <ul>\n                <li><strong>🎯 TMDB Integration:</strong> Auto-fetch movie metadata</li>\n                <li><strong>🏷️ Category Assignment:</strong> Smart movie categorization</li>\n                <li><strong>🖼️ Poster Download:</strong> High-quality movie artwork</li>\n                <li><strong>📝 Description Parsing:</strong> Extract movie info</li>\n              </ul>\n\n              <h6>⚡ Performance Tips:</h6>\n              <ul>\n                <li>Files under 10MB import faster</li>\n                <li>Use UTF-8 encoding for special characters</li>\n                <li>Clean duplicate entries before import</li>\n                <li>Ensure stable internet for TMDB metadata</li>\n              </ul>\n            </Card.Body>\n          </Card>\n        </Col>\n      </Row>\n\n      <Row>\n        <Col>\n          <Card className=\"shadow-sm\">\n            <Card.Header className=\"bg-secondary text-white\">\n              <h5 className=\"mb-0\">📊 Recent VOD Import Queue</h5>\n            </Card.Header>\n            <Card.Body>\n              <Table striped hover>\n                <thead>\n                  <tr>\n                    <th>📄 File</th>\n                    <th>📅 Queued</th>\n                    <th>📊 Status</th>\n                    <th>🎯 Target</th>\n                    <th>⚡ Actions</th>\n                  </tr>\n                </thead>\n                <tbody>\n                  <tr>\n                    <td><strong>movies_collection.m3u</strong></td>\n                    <td>2025-07-15 16:30</td>\n                    <td><span className=\"badge bg-warning\">⏳ Queued</span></td>\n                    <td>Main Server</td>\n                    <td>\n                      <Button size=\"sm\" variant=\"outline-primary\" className=\"me-1\">▶️</Button>\n                      <Button size=\"sm\" variant=\"outline-danger\">❌</Button>\n                    </td>\n                  </tr>\n                  <tr>\n                    <td><strong>action_movies.m3u</strong></td>\n                    <td>2025-07-15 16:25</td>\n                    <td><span className=\"badge bg-success\">✅ Processing</span></td>\n                    <td>Cloud Server</td>\n                    <td>\n                      <Button size=\"sm\" variant=\"outline-info\">📊</Button>\n                    </td>\n                  </tr>\n                </tbody>\n              </Table>\n            </Card.Body>\n          </Card>\n        </Col>\n      </Row>\n    </div>\n  );\n};\n\nexport default ImportVODM3U;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAEC,MAAM,EAAEC,IAAI,EAAEC,KAAK,EAAEC,WAAW,EAAEC,KAAK,EAAEC,KAAK,QAAQ,iBAAiB;AAChG,OAAOC,aAAa,MAAM,iBAAiB;AAE3C,SACEC,iBAAiB,QACZ,sBAAsB;AAC7B,SAASC,GAAG,EAAEC,WAAW,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE1D,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA;EACzB,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAG/B,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACgC,WAAW,EAAEC,cAAc,CAAC,GAAGjC,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACkC,cAAc,EAAEC,iBAAiB,CAAC,GAAGnC,QAAQ,CAAC,CAAC,CAAC;EACvD,MAAM,CAACoC,SAAS,EAAEC,YAAY,CAAC,GAAGrC,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACsC,YAAY,EAAEC,eAAe,CAAC,GAAGvC,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACwC,SAAS,EAAEC,YAAY,CAAC,GAAGzC,QAAQ,CAAC,MAAM,CAAC;;EAElD;EACA,MAAM,CAAC0C,aAAa,EAAEC,gBAAgB,CAAC,GAAG3C,QAAQ,CAAC,UAAU,CAAC;EAC9D,MAAM,CAAC4C,YAAY,EAAEC,eAAe,CAAC,GAAG7C,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAAC8C,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG/C,QAAQ,CAAC,KAAK,CAAC;;EAE/D;EACA,MAAMgD,WAAW,GAAG,OAAO;EAC3B,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGlD,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACmD,YAAY,EAAEC,eAAe,CAAC,GAAGpD,QAAQ,CAAC;IAC/CqD,YAAY,EAAE,IAAI;IAClBC,WAAW,EAAE,KAAK;IAClBC,aAAa,EAAE;EACjB,CAAC,CAAC;EACF,MAAM,CAACC,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGzD,QAAQ,CAAC,EAAE,CAAC;;EAEhE;EACA,MAAM,CAAC0D,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG3D,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAAC4D,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG7D,QAAQ,CAAC,EAAE,CAAC;EAEhE,MAAM8D,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI;MACF,MAAMC,MAAM,GAAG,MAAMnD,iBAAiB,CAAC,CAAC;MACxC+B,gBAAgB,CAACoB,MAAM,CAACC,OAAO,GAAG,WAAW,GAAG,OAAO,CAAC;MAExD,IAAI,CAACD,MAAM,CAACC,OAAO,EAAE;QACnBC,YAAY,CAAC,SAAS,EAAE,qDAAqD,CAAC;MAChF;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdvB,gBAAgB,CAAC,OAAO,CAAC;MACzBsB,YAAY,CAAC,QAAQ,EAAE,iCAAiC,CAAC;IAC3D;EACF,CAAC;EAED,MAAME,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACFC,OAAO,CAACC,GAAG,CAAC,gDAAgD3B,aAAa,EAAE,CAAC;;MAE5E;MACA,IAAIA,aAAa,KAAK,WAAW,EAAE;QACjC0B,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;QAC5D,MAAMC,eAAe,CAAC,CAAC;QACvB,MAAMC,kBAAkB,CAAC,CAAC;MAC5B,CAAC,MAAM;QACLH,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;QAC5D;QACAG,YAAY,CAAC,CAAC;MAChB;IAEF,CAAC,CAAC,OAAON,KAAK,EAAE;MACdE,OAAO,CAACF,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvD,IAAIO,MAAM,CAACC,QAAQ,EAAE;QACnBD,MAAM,CAACC,QAAQ,CAAC,OAAO,EAAE,mCAAmCR,KAAK,CAACS,OAAO,EAAE,CAAC;MAC9E;MACA;MACAH,YAAY,CAAC,CAAC;IAChB;EACF,CAAC;;EAED;EACA,MAAMF,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACFF,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAC;MAEzD,MAAMO,QAAQ,GAAG,MAAMC,KAAK,CAAC,sDAAsD,CAAC;MACpFT,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEO,QAAQ,CAACE,MAAM,EAAEF,QAAQ,CAACG,UAAU,CAAC;MAE/E,MAAMC,MAAM,GAAG,MAAMJ,QAAQ,CAACK,IAAI,CAAC,CAAC;MACpCb,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEW,MAAM,CAAC;MAE1C,IAAIA,MAAM,CAAChB,OAAO,IAAIgB,MAAM,CAACE,IAAI,EAAE;QACjC,MAAMC,OAAO,GAAGH,MAAM,CAACE,IAAI,CAACE,GAAG,CAACC,MAAM,KAAK;UACzCC,EAAE,EAAED,MAAM,CAACE,SAAS;UACpBC,IAAI,EAAEH,MAAM,CAACI,WAAW,IAAI,UAAUJ,MAAM,CAACE,SAAS,EAAE;UACxDG,EAAE,EAAEL,MAAM,CAACM,SAAS,IAAI,YAAY;UACpCC,IAAI,EAAE,GAAGP,MAAM,CAACQ,aAAa,IAAI,CAAC,UAAU;UAAE;UAC9CA,aAAa,EAAER,MAAM,CAACQ,aAAa,IAAI,CAAC;UACxCf,MAAM,EAAEO,MAAM,CAACS,aAAa,KAAK,CAAC,GAAG,QAAQ,GAAG;QAClD,CAAC,CAAC,CAAC;QAEH1B,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEc,OAAO,CAAC;QAChDxB,mBAAmB,CAACwB,OAAO,CAAC;QAC5Bf,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEc,OAAO,CAACY,MAAM,EAAE,YAAY,CAAC;QAErE,IAAItB,MAAM,CAACC,QAAQ,EAAE;UACnBD,MAAM,CAACC,QAAQ,CAAC,SAAS,EAAE,cAAcS,OAAO,CAACY,MAAM,6BAA6B,CAAC;QACvF;MACF,CAAC,MAAM;QACL3B,OAAO,CAACF,KAAK,CAAC,yBAAyB,EAAEc,MAAM,CAAC;QAChD,MAAM,IAAIgB,KAAK,CAAChB,MAAM,CAACd,KAAK,IAAI,kCAAkC,CAAC;MACrE;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdE,OAAO,CAACF,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD,IAAIO,MAAM,CAACC,QAAQ,EAAE;QACnBD,MAAM,CAACC,QAAQ,CAAC,OAAO,EAAE,gCAAgCR,KAAK,CAACS,OAAO,EAAE,CAAC;MAC3E;MACA,MAAMT,KAAK;IACb;EACF,CAAC;;EAED;EACA,MAAM,CAAC+B,cAAc,EAAEC,iBAAiB,CAAC,GAAGlG,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACmG,aAAa,EAAEC,gBAAgB,CAAC,GAAGpG,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACqG,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGtG,QAAQ,CAAC,EAAE,CAAC;EAChE,MAAM,CAACuG,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGxG,QAAQ,CAAC,KAAK,CAAC;;EAEjE;EACA,MAAMuE,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI;MACFiC,oBAAoB,CAAC,IAAI,CAAC;MAC1BpC,OAAO,CAACC,GAAG,CAAC,wDAAwD,CAAC;MACrE,MAAMW,MAAM,GAAG,MAAMlE,WAAW,CAAC2F,aAAa,CAAC,CAAC;MAEhD,IAAIzB,MAAM,CAAChB,OAAO,IAAIgB,MAAM,CAAC0B,UAAU,EAAE;QACvC,MAAMA,UAAU,GAAG1B,MAAM,CAAC0B,UAAU,CAACtB,GAAG,CAACuB,GAAG,KAAK;UAC/CrB,EAAE,EAAEqB,GAAG,CAACrB,EAAE;UACVE,IAAI,EAAEmB,GAAG,CAACnB,IAAI;UACdoB,WAAW,EAAED,GAAG,CAACC,WAAW;UAC5BC,aAAa,EAAEF,GAAG,CAACE,aAAa;UAChCC,aAAa,EAAEH,GAAG,CAACG,aAAa;UAChCC,eAAe,EAAE,IAAIJ,GAAG,CAACrB,EAAE,GAAG,CAAC;QACjC,CAAC,CAAC,CAAC;QAEHc,gBAAgB,CAACM,UAAU,CAAC;QAC5BJ,qBAAqB,CAACI,UAAU,CAAC;QACjC7C,qBAAqB,CAAC6C,UAAU,CAAC;QACjCtC,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEqC,UAAU,CAACX,MAAM,CAAC;QAExD,IAAItB,MAAM,CAACC,QAAQ,EAAE;UACnBD,MAAM,CAACC,QAAQ,CAAC,SAAS,EAAE,cAAcgC,UAAU,CAACX,MAAM,6BAA6B,CAAC;QAC1F;MACF,CAAC,MAAM;QACL,MAAM,IAAIC,KAAK,CAAC,kCAAkC,CAAC;MACrD;IACF,CAAC,CAAC,OAAO9B,KAAK,EAAE;MACdE,OAAO,CAACF,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClD,IAAIO,MAAM,CAACC,QAAQ,EAAE;QACnBD,MAAM,CAACC,QAAQ,CAAC,OAAO,EAAE,gCAAgCR,KAAK,CAACS,OAAO,EAAE,CAAC;MAC3E;MACA;MACAqC,kBAAkB,CAAC,CAAC;IACtB,CAAC,SAAS;MACRR,oBAAoB,CAAC,KAAK,CAAC;IAC7B;EACF,CAAC;;EAED;EACA,MAAMS,gBAAgB,GAAIC,UAAU,IAAK;IACvC,IAAI,CAACA,UAAU,CAACC,IAAI,CAAC,CAAC,EAAE;MACtBb,qBAAqB,CAACH,aAAa,CAAC;MACpC;IACF;IAEA,MAAMiB,WAAW,GAAGF,UAAU,CAACG,WAAW,CAAC,CAAC;IAC5C,MAAMC,QAAQ,GAAGnB,aAAa,CAACoB,MAAM,CAACZ,GAAG,IACvCA,GAAG,CAACnB,IAAI,CAAC6B,WAAW,CAAC,CAAC,CAACG,QAAQ,CAACJ,WAAW,CAAC,IAC5CT,GAAG,CAACrB,EAAE,CAACmC,QAAQ,CAAC,CAAC,CAACD,QAAQ,CAACN,UAAU,CAAC,IACrCP,GAAG,CAACE,aAAa,IAAIF,GAAG,CAACE,aAAa,CAACQ,WAAW,CAAC,CAAC,CAACG,QAAQ,CAACJ,WAAW,CAC5E,CAAC;IAEDd,qBAAqB,CAACgB,QAAQ,CAAC;EACjC,CAAC;;EAED;EACA,MAAMI,oBAAoB,GAAIC,CAAC,IAAK;IAClC,MAAMC,KAAK,GAAGD,CAAC,CAACE,MAAM,CAACD,KAAK;IAC5B1B,iBAAiB,CAAC0B,KAAK,CAAC;IACxBX,gBAAgB,CAACW,KAAK,CAAC;EACzB,CAAC;;EAED;EACA,MAAMZ,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,MAAMc,cAAc,GAAG,CACrB;MAAExC,EAAE,EAAE,IAAI;MAAEE,IAAI,EAAE,qBAAqB;MAAEoB,WAAW,EAAE;IAAK,CAAC,EAC5D;MAAEtB,EAAE,EAAE,IAAI;MAAEE,IAAI,EAAE,QAAQ;MAAEoB,WAAW,EAAE;IAAK,CAAC,EAC/C;MAAEtB,EAAE,EAAE,IAAI;MAAEE,IAAI,EAAE,OAAO;MAAEoB,WAAW,EAAE;IAAK,CAAC,EAC9C;MAAEtB,EAAE,EAAE,IAAI;MAAEE,IAAI,EAAE,SAAS;MAAEoB,WAAW,EAAE;IAAK,CAAC,EAChD;MAAEtB,EAAE,EAAE,IAAI;MAAEE,IAAI,EAAE,eAAe;MAAEoB,WAAW,EAAE;IAAK,CAAC,CACvD;IACDR,gBAAgB,CAAC0B,cAAc,CAAC;IAChCxB,qBAAqB,CAACwB,cAAc,CAAC;IACrCjE,qBAAqB,CAACiE,cAAc,CAAC;IACrC1D,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEyD,cAAc,CAAC;EAC3D,CAAC;;EAED;EACA,MAAMC,kBAAkB,GAAIC,YAAY,IAAK;IAC3C,MAAMxC,IAAI,GAAGwC,YAAY,CAACX,WAAW,CAAC,CAAC;IAEvC,IAAI7B,IAAI,CAACgC,QAAQ,CAAC,OAAO,CAAC,IAAIhC,IAAI,CAACgC,QAAQ,CAAC,MAAM,CAAC,IAAIhC,IAAI,CAACgC,QAAQ,CAAC,QAAQ,CAAC,EAAE;MAC9E,OAAO,OAAO;IAChB,CAAC,MAAM,IAAIhC,IAAI,CAACgC,QAAQ,CAAC,QAAQ,CAAC,IAAIhC,IAAI,CAACgC,QAAQ,CAAC,MAAM,CAAC,IAAIhC,IAAI,CAACgC,QAAQ,CAAC,OAAO,CAAC,EAAE;MACrF,OAAO,QAAQ;IACjB,CAAC,MAAM,IAAIhC,IAAI,CAACgC,QAAQ,CAAC,MAAM,CAAC,IAAIhC,IAAI,CAACgC,QAAQ,CAAC,IAAI,CAAC,IAAIhC,IAAI,CAACgC,QAAQ,CAAC,SAAS,CAAC,IAAIhC,IAAI,CAACgC,QAAQ,CAAC,MAAM,CAAC,IAAIhC,IAAI,CAACgC,QAAQ,CAAC,OAAO,CAAC,EAAE;MACtI,OAAO,MAAM;IACf,CAAC,MAAM,IAAIhC,IAAI,CAACgC,QAAQ,CAAC,OAAO,CAAC,IAAIhC,IAAI,CAACgC,QAAQ,CAAC,OAAO,CAAC,IAAIhC,IAAI,CAACgC,QAAQ,CAAC,IAAI,CAAC,EAAE;MAClF,OAAO,OAAO;IAChB;IAEA,OAAO,OAAO,CAAC,CAAC;EAClB,CAAC;;EAED;EACA,MAAMhD,YAAY,GAAGA,CAAA,KAAM;IACzBb,mBAAmB,CAAC,CAClB;MAAE2B,EAAE,EAAE,CAAC;MAAEE,IAAI,EAAE,gBAAgB;MAAEE,EAAE,EAAE,eAAe;MAAEE,IAAI,EAAE;IAAM,CAAC,EACnE;MAAEN,EAAE,EAAE,CAAC;MAAEE,IAAI,EAAE,WAAW;MAAEE,EAAE,EAAE,eAAe;MAAEE,IAAI,EAAE;IAAM,CAAC,EAC9D;MAAEN,EAAE,EAAE,CAAC;MAAEE,IAAI,EAAE,aAAa;MAAEE,EAAE,EAAE,eAAe;MAAEE,IAAI,EAAE;IAAM,CAAC,EAChE;MAAEN,EAAE,EAAE,CAAC;MAAEE,IAAI,EAAE,eAAe;MAAEE,EAAE,EAAE,eAAe;MAAEE,IAAI,EAAE;IAAM,CAAC,CACnE,CAAC;IAEF/B,qBAAqB,CAAC,CACpB;MAAEyB,EAAE,EAAE,CAAC;MAAEE,IAAI,EAAE,eAAe;MAAEyC,IAAI,EAAE;IAAQ,CAAC,EAC/C;MAAE3C,EAAE,EAAE,CAAC;MAAEE,IAAI,EAAE,eAAe;MAAEyC,IAAI,EAAE;IAAQ,CAAC,EAC/C;MAAE3C,EAAE,EAAE,CAAC;MAAEE,IAAI,EAAE,cAAc;MAAEyC,IAAI,EAAE;IAAQ,CAAC,EAC9C;MAAE3C,EAAE,EAAE,CAAC;MAAEE,IAAI,EAAE,eAAe;MAAEyC,IAAI,EAAE;IAAQ,CAAC,EAC/C;MAAE3C,EAAE,EAAE,CAAC;MAAEE,IAAI,EAAE,eAAe;MAAEyC,IAAI,EAAE;IAAQ,CAAC,EAC/C;MAAE3C,EAAE,EAAE,CAAC;MAAEE,IAAI,EAAE,aAAa;MAAEyC,IAAI,EAAE;IAAQ,CAAC,EAC7C;MAAE3C,EAAE,EAAE,CAAC;MAAEE,IAAI,EAAE,WAAW;MAAEyC,IAAI,EAAE;IAAQ,CAAC,EAC3C;MAAE3C,EAAE,EAAE,CAAC;MAAEE,IAAI,EAAE,UAAU;MAAEyC,IAAI,EAAE;IAAQ,CAAC,EAC1C;MAAE3C,EAAE,EAAE,CAAC;MAAEE,IAAI,EAAE,SAAS;MAAEyC,IAAI,EAAE;IAAQ,CAAC,CAC1C,CAAC;IAEF,IAAIxD,MAAM,CAACC,QAAQ,EAAE;MACnBD,MAAM,CAACC,QAAQ,CAAC,SAAS,EAAE,8CAA8C,CAAC;IAC5E;EACF,CAAC;EAED,MAAMwD,gBAAgB,GAAGA,CAACvD,OAAO,EAAEsD,IAAI,GAAG,MAAM,KAAK;IACnD1F,eAAe,CAACoC,OAAO,CAAC;IACxBlC,YAAY,CAACwF,IAAI,CAAC;IAClB5F,YAAY,CAAC,IAAI,CAAC;IAClB8F,UAAU,CAAC,MAAM9F,YAAY,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC;EAC7C,CAAC;;EAED;EACA,MAAM4B,YAAY,GAAGA,CAACgE,IAAI,EAAEtD,OAAO,KAAK;IACtCuD,gBAAgB,CAACvD,OAAO,EAAEsD,IAAI,CAAC;EACjC,CAAC;;EAED;EACA,MAAMG,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,MAAMC,SAAS,GAAGC,QAAQ,CAACC,aAAa,CAAC,OAAO,CAAC;IACjDF,SAAS,CAACJ,IAAI,GAAG,MAAM;IACvBI,SAAS,CAACG,MAAM,GAAG,YAAY;IAC/BH,SAAS,CAACI,QAAQ,GAAId,CAAC,IAAK;MAC1B,MAAMe,IAAI,GAAGf,CAAC,CAACE,MAAM,CAACc,KAAK,CAAC,CAAC,CAAC;MAC9B,IAAID,IAAI,EAAE;QACRE,gBAAgB,CAAC;UAAEf,MAAM,EAAE;YAAEc,KAAK,EAAE,CAACD,IAAI;UAAE;QAAE,CAAC,CAAC;MACjD;IACF,CAAC;IACDL,SAAS,CAACQ,KAAK,CAAC,CAAC;EACnB,CAAC;EAED,MAAMC,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI,CAAChH,YAAY,EAAE;IAEnBiB,mBAAmB,CAAC,IAAI,CAAC;IACzBF,eAAe,CAAC,IAAI,CAAC;IAErB,IAAI;MACFoB,YAAY,CAAC,MAAM,EAAE,2BAA2B,CAAC;MAEjD,MAAMW,QAAQ,GAAG,MAAM/D,GAAG,CAACkI,MAAM,CAACC,WAAW,CAAClH,YAAY,CAAC;MAE3D,IAAI8C,QAAQ,CAACZ,OAAO,EAAE;QAAA,IAAAiF,qBAAA;QACpBpG,eAAe,CAAC+B,QAAQ,CAACM,IAAI,CAAC;;QAE9B;QACAjB,YAAY,CAAC,SAAS,EAAE,oDAAoD,EAAAgF,qBAAA,GAAArE,QAAQ,CAACM,IAAI,CAACgE,cAAc,cAAAD,qBAAA,uBAA5BA,qBAAA,CAA8BE,iBAAiB,KAAI,CAAC,0DAA0D,CAAC;MAC7L,CAAC,MAAM;QACL,MAAM,IAAInD,KAAK,CAACpB,QAAQ,CAACV,KAAK,IAAI,0BAA0B,CAAC;MAC/D;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdE,OAAO,CAACF,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7CD,YAAY,CAAC,QAAQ,EAAE,+BAA+BC,KAAK,CAACS,OAAO,EAAE,CAAC;IACxE,CAAC,SAAS;MACR5B,mBAAmB,CAAC,KAAK,CAAC;IAC5B;EACF,CAAC;EAED,MAAMqG,mBAAmB,GAAGA,CAAA,KAAM;IAChCvG,eAAe,CAAC,IAAI,CAAC;IACrBd,eAAe,CAAC,IAAI,CAAC;IACrB;IACAkC,YAAY,CAAC,MAAM,EAAE,iDAAiD,CAAC;EACzE,CAAC;;EAED;EACAhE,SAAS,CAAC,MAAM;IACd6D,kBAAkB,CAAC,CAAC;IACpB;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA7D,SAAS,CAAC,MAAM;IACd,IAAIyC,aAAa,KAAK,UAAU,EAAE;MAChCyB,eAAe,CAAC,CAAC;IACnB;IACA;EACF,CAAC,EAAE,CAACzB,aAAa,CAAC,CAAC;EAEnB,MAAMkG,gBAAgB,GAAG,MAAOS,KAAK,IAAK;IACxC,MAAMX,IAAI,GAAGW,KAAK,CAACxB,MAAM,CAACc,KAAK,CAAC,CAAC,CAAC;IAClC5G,eAAe,CAAC2G,IAAI,CAAC;IACrB7F,eAAe,CAAC,IAAI,CAAC;IAErB,IAAI6F,IAAI,EAAE;MACRzE,YAAY,CAAC,MAAM,EAAE,yBAAyByE,IAAI,CAAClD,IAAI,KAAK,CAACkD,IAAI,CAACY,IAAI,GAAG,IAAI,GAAG,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC;;MAEvG;MACA,IAAI9E,MAAM,CAACC,QAAQ,EAAE;QACnBD,MAAM,CAACC,QAAQ,CAAC,qBAAqBgE,IAAI,CAAClD,IAAI,EAAE,EAAE,MAAM,CAAC;QACzDf,MAAM,CAACC,QAAQ,CAAC,iBAAiB,CAACgE,IAAI,CAACY,IAAI,GAAG,IAAI,GAAG,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC,KAAK,EAAE,MAAM,CAAC;MACrF;IACF;EACF,CAAC;EAED,MAAMC,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI,CAAC1H,YAAY,IAAI,CAACmB,aAAa,EAAE;MACnCgB,YAAY,CAAC,SAAS,EAAE,yEAAyE,CAAC;MAClG;IACF;IAEA,IAAI,CAACrB,YAAY,EAAE;MACjBqB,YAAY,CAAC,SAAS,EAAE,oDAAoD,CAAC;MAC7E;IACF;IAEA,IAAIQ,MAAM,CAACC,QAAQ,EAAE;MACnBD,MAAM,CAACC,QAAQ,CAAC,6BAA6B5C,YAAY,CAAC0D,IAAI,EAAE,EAAE,MAAM,CAAC;MACzEf,MAAM,CAACC,QAAQ,CAAC,iBAAiB,CAAC5C,YAAY,CAACwH,IAAI,GAAG,IAAI,GAAG,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC,KAAK,EAAE,MAAM,CAAC;MAC3F9E,MAAM,CAACC,QAAQ,CAAC,oBAAoB1B,WAAW,EAAE,EAAE,MAAM,CAAC;MAC1DyB,MAAM,CAACC,QAAQ,CAAC,sBAAsBzB,aAAa,EAAE,EAAE,MAAM,CAAC;IAChE;IAEAhB,cAAc,CAAC,IAAI,CAAC;IACpBE,iBAAiB,CAAC,CAAC,CAAC;IAEpB,IAAI;MACF;MACA,MAAMsH,YAAY,GAAG;QACnBzG,WAAW;QACXC,aAAa;QACbE,YAAY;QACZuD,UAAU,EAAElD,kBAAkB;QAC9BkG,WAAW,EAAE,IAAI;QACjBC,oBAAoB,EAAE;MACxB,CAAC;MAED1F,YAAY,CAAC,MAAM,EAAE,4CAA4C,CAAC;MAClE9B,iBAAiB,CAAC,EAAE,CAAC;;MAErB;MACA,MAAMyH,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;MAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAEhI,YAAY,CAAC;MACrC8H,QAAQ,CAACE,MAAM,CAAC,QAAQ,EAAEC,IAAI,CAACC,SAAS,CAACP,YAAY,CAAC,CAAC;MAEvD,IAAIhF,MAAM,CAACC,QAAQ,EAAE;QACnBD,MAAM,CAACC,QAAQ,CAAC,qCAAqC,EAAE,MAAM,CAAC;MAChE;;MAEA;MACA,MAAMuF,eAAe,GAAG,MAAMpJ,GAAG,CAACkI,MAAM,CAACC,WAAW,CAAClH,YAAY,CAAC;MAClEK,iBAAiB,CAAC,EAAE,CAAC;MAErB,IAAI,CAAC8H,eAAe,CAACjG,OAAO,EAAE;QAC5B,MAAM,IAAIgC,KAAK,CAACiE,eAAe,CAAC/F,KAAK,IAAI,sBAAsB,CAAC;MAClE;MAEAD,YAAY,CAAC,MAAM,EAAE,gDAAgD,CAAC;MACtE;MACA,MAAMiG,WAAW,GAAG,MAAM,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;QACzD,MAAMC,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;QAC/BD,MAAM,CAACE,MAAM,GAAI7C,CAAC,IAAKyC,OAAO,CAACzC,CAAC,CAACE,MAAM,CAAC7C,MAAM,CAAC;QAC/CsF,MAAM,CAACG,OAAO,GAAG,MAAMJ,MAAM,CAAC,IAAIrE,KAAK,CAAC,oBAAoB,CAAC,CAAC;QAC9DsE,MAAM,CAACI,UAAU,CAAC5I,YAAY,CAAC;MACjC,CAAC,CAAC;MAEFK,iBAAiB,CAAC,EAAE,CAAC;;MAErB;MACA,MAAMwI,aAAa,GAAG,MAAM9F,KAAK,CAAC,+CAA+C,EAAE;QACjF+F,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UAAE,cAAc,EAAE;QAAmB,CAAC;QAC/CC,IAAI,EAAEf,IAAI,CAACC,SAAS,CAAC;UAAEe,UAAU,EAAEb;QAAY,CAAC;MAClD,CAAC,CAAC;MAEF,MAAMc,WAAW,GAAG,MAAML,aAAa,CAAC1F,IAAI,CAAC,CAAC;MAC9C,IAAI,CAAC+F,WAAW,CAAChH,OAAO,EAAE;QACxB,MAAM,IAAIgC,KAAK,CAACgF,WAAW,CAAC9G,KAAK,IAAI,2BAA2B,CAAC;MACnE;MAEA/B,iBAAiB,CAAC,EAAE,CAAC;;MAErB;MACA,MAAM8I,aAAa,GAAG;QACpBC,MAAM,EAAEF,WAAW,CAAC9F,IAAI,CAACgG,MAAM;QAC/B3F,SAAS,EAAE4F,QAAQ,CAAClI,aAAa,CAAC;QAClCmI,WAAW,EAAE5H,kBAAkB,CAACuC,MAAM,GAAG,CAAC,GAAGoF,QAAQ,CAAC3H,kBAAkB,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI;QACnF6H,WAAW,EAAE,IAAI,CAAC;MACpB,CAAC;MAED,MAAMC,cAAc,GAAG,MAAMzG,KAAK,CAAC,yCAAyC,EAAE;QAC5E+F,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UAAE,cAAc,EAAE;QAAmB,CAAC;QAC/CC,IAAI,EAAEf,IAAI,CAACC,SAAS,CAACiB,aAAa;MACpC,CAAC,CAAC;MAEF,MAAMM,YAAY,GAAG,MAAMD,cAAc,CAACrG,IAAI,CAAC,CAAC;;MAEhD;MACAb,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEkH,YAAY,CAAC;MAEpD,IAAI,CAACA,YAAY,CAACvH,OAAO,EAAE;QACzB,MAAM,IAAIgC,KAAK,CAACuF,YAAY,CAACrH,KAAK,IAAI,6BAA6B,CAAC;MACtE;;MAEA;MACA/B,iBAAiB,CAAC,GAAG,CAAC;;MAEtB;MACA,MAAMqJ,KAAK,GAAGD,YAAY,CAACrG,IAAI,IAAIqG,YAAY;MAC/C,MAAME,cAAc,GAAG,mEAAmED,KAAK,CAACE,QAAQ,IAAI,CAAC,4BAA4BF,KAAK,CAACG,MAAM,IAAI,CAAC,eAAeH,KAAK,CAACI,cAAc,IAAI,CAAC,yBAAyBJ,KAAK,CAACK,iBAAiB,IAAI,CAAC,oBAAoB;MAE3Q5H,YAAY,CAAC,SAAS,EAAEwH,cAAc,CAAC;MAEvC,IAAIhH,MAAM,CAACC,QAAQ,EAAE;QACnBD,MAAM,CAACC,QAAQ,CAAC,wCAAwC5C,YAAY,CAAC0D,IAAI,EAAE,EAAE,SAAS,CAAC;QACvFf,MAAM,CAACC,QAAQ,CAAC,aAAaqF,IAAI,CAACC,SAAS,CAACuB,YAAY,CAAC,EAAE,EAAE,MAAM,CAAC;MACtE;;MAEA;MACApD,UAAU,CAAC,MAAM;QACfpG,eAAe,CAAC,IAAI,CAAC;QACrBc,eAAe,CAAC,IAAI,CAAC;QACrB;QACAK,gBAAgB,CAAC,EAAE,CAAC;QACpBO,qBAAqB,CAAC,EAAE,CAAC;MAC3B,CAAC,EAAE,IAAI,CAAC;IAEV,CAAC,CAAC,OAAOS,KAAK,EAAE;MACdE,OAAO,CAACF,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC;MACzCD,YAAY,CAAC,QAAQ,EAAE,uCAAuCC,KAAK,CAACS,OAAO,EAAE,CAAC;MAE9E,IAAIF,MAAM,CAACC,QAAQ,EAAE;QACnBD,MAAM,CAACC,QAAQ,CAAC,wBAAwBR,KAAK,CAACS,OAAO,EAAE,EAAE,OAAO,CAAC;MACnE;IACF,CAAC,SAAS;MACR1C,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;EAED,oBACEjB,OAAA;IAAK8K,KAAK,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,QAAQ,EAAE;IAAO,CAAE;IAAAC,QAAA,gBAC9CjL,OAAA;MAAKkL,SAAS,EAAC,wDAAwD;MAAAD,QAAA,gBACrEjL,OAAA;QAAIkL,SAAS,EAAC,cAAc;QAAAD,QAAA,EAAC;MAAuB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACzDtL,OAAA,CAACL,aAAa;QACZmE,MAAM,EAAEpC,aAAc;QACtB6J,OAAO,EAAEzI;MAAmB;QAAAqI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7B,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,EAELlK,SAAS,iBACRpB,OAAA,CAACT,KAAK;MAACiM,OAAO,EAAEhK,SAAU;MAACiK,WAAW;MAACC,OAAO,EAAEA,CAAA,KAAMrK,YAAY,CAAC,KAAK,CAAE;MAAA4J,QAAA,gBACxEjL,OAAA,CAACT,KAAK,CAACoM,OAAO;QAAAV,QAAA,GACXzJ,SAAS,KAAK,SAAS,IAAI,YAAY,EACvCA,SAAS,KAAK,QAAQ,IAAI,UAAU,EACpCA,SAAS,KAAK,SAAS,IAAI,aAAa,EACxCA,SAAS,KAAK,MAAM,IAAI,gBAAgB;MAAA;QAAA2J,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5B,CAAC,eAChBtL,OAAA;QAAAiL,QAAA,EAAI3J;MAAY;QAAA6J,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChB,CACR,eAEDtL,OAAA,CAACd,GAAG;MAACgM,SAAS,EAAC,MAAM;MAAAD,QAAA,gBACnBjL,OAAA,CAACb,GAAG;QAACyM,EAAE,EAAE,CAAE;QAAAX,QAAA,eACTjL,OAAA,CAACZ,IAAI;UAAC8L,SAAS,EAAC,iBAAiB;UAAAD,QAAA,gBAC/BjL,OAAA,CAACZ,IAAI,CAACyM,MAAM;YAACX,SAAS,EAAC,yEAAyE;YAAAD,QAAA,gBAC9FjL,OAAA;cAAIkL,SAAS,EAAC,MAAM;cAAAD,QAAA,EAAC;YAAc;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EACvC5J,aAAa,KAAK,WAAW,iBAC5B1B,OAAA,CAACN,KAAK;cAACoM,EAAE,EAAC,SAAS;cAAAb,QAAA,gBACjBjL,OAAA;gBAAGkL,SAAS,EAAC;cAAmB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,kBACvC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CACR;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACU,CAAC,eACdtL,OAAA,CAACZ,IAAI,CAAC2M,IAAI;YAAAd,QAAA,eACRjL,OAAA,CAACV,IAAI;cAAA2L,QAAA,gBACHjL,OAAA,CAACV,IAAI,CAAC0M,KAAK;gBAACd,SAAS,EAAC,MAAM;gBAAAD,QAAA,gBAC1BjL,OAAA,CAACV,IAAI,CAAC2M,KAAK;kBAAAhB,QAAA,EAAC;gBAAe;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACxCtL,OAAA,CAACV,IAAI,CAAC4M,OAAO;kBACXjF,IAAI,EAAC,MAAM;kBACXO,MAAM,EAAC,YAAY;kBACnB2E,QAAQ,EAAEvE,gBAAiB;kBAC3BwE,QAAQ,EAAEpL;gBAAY;kBAAAmK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB,CAAC,eACFtL,OAAA,CAACV,IAAI,CAAC+M,IAAI;kBAACnB,SAAS,EAAC,YAAY;kBAAAD,QAAA,EAAC;gBAElC;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,EAEZxK,YAAY,iBACXd,OAAA,CAAAE,SAAA;gBAAA+K,QAAA,gBACEjL,OAAA,CAACT,KAAK;kBAACiM,OAAO,EAAC,MAAM;kBAAAP,QAAA,gBACnBjL,OAAA;oBAAAiL,QAAA,EAAQ;kBAAc;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,KAAC,EAACxK,YAAY,CAAC0D,IAAI,eAACxE,OAAA;oBAAAmL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACxDtL,OAAA;oBAAAiL,QAAA,EAAQ;kBAAK;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,KAAC,EAAC,CAACxK,YAAY,CAACwH,IAAI,GAAG,IAAI,GAAG,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC,EAAC,KACvE;gBAAA;kBAAA4C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,EAEPxJ,gBAAgB,iBACf9B,OAAA,CAACT,KAAK;kBAACiM,OAAO,EAAC,WAAW;kBAAAP,QAAA,eACxBjL,OAAA;oBAAKkL,SAAS,EAAC,2BAA2B;oBAAAD,QAAA,gBACxCjL,OAAA;sBAAKkL,SAAS,EAAC,uCAAuC;sBAACoB,IAAI,EAAC;oBAAQ;sBAAAnB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,yBAE7E;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CACR,EAEA1J,YAAY,IAAI,CAACE,gBAAgB,iBAChC9B,OAAA,CAACT,KAAK;kBAACiM,OAAO,EAAC,SAAS;kBAAAP,QAAA,gBACtBjL,OAAA;oBAAAiL,QAAA,EAAI;kBAAuB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAChCtL,OAAA,CAACd,GAAG;oBAAA+L,QAAA,gBACFjL,OAAA,CAACb,GAAG;sBAACoN,EAAE,EAAE,CAAE;sBAAAtB,QAAA,gBACTjL,OAAA;wBAAAiL,QAAA,EAAQ;sBAAY;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,KAAC,EAAC,EAAAjL,qBAAA,GAAAuB,YAAY,CAACsG,cAAc,cAAA7H,qBAAA,uBAA3BA,qBAAA,CAA6BmM,WAAW,KAAI,CAAC,eAACxM,OAAA;wBAAAmL,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eAClFtL,OAAA;wBAAAiL,QAAA,EAAQ;sBAAe;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,KAAC,EAAC,EAAAhL,sBAAA,GAAAsB,YAAY,CAACsG,cAAc,cAAA5H,sBAAA,uBAA3BA,sBAAA,CAA6BmM,YAAY,KAAI,CAAC,eAACzM,OAAA;wBAAAmL,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACtFtL,OAAA;wBAAAiL,QAAA,EAAQ;sBAAY;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,KAAC,EAAC,EAAA/K,sBAAA,GAAAqB,YAAY,CAACsG,cAAc,cAAA3H,sBAAA,uBAA3BA,sBAAA,CAA6BmM,SAAS,KAAI,CAAC;oBAAA;sBAAAvB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvE,CAAC,eACNtL,OAAA,CAACb,GAAG;sBAACoN,EAAE,EAAE,CAAE;sBAAAtB,QAAA,gBACTjL,OAAA;wBAAAiL,QAAA,EAAQ;sBAAkB;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,KAAC,EAAC,EAAA9K,sBAAA,GAAAoB,YAAY,CAACsG,cAAc,cAAA1H,sBAAA,uBAA3BA,sBAAA,CAA6B2H,iBAAiB,KAAI,CAAC,eAACnI,OAAA;wBAAAmL,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eAC9FtL,OAAA;wBAAAiL,QAAA,EAAQ;sBAAU;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,KAAC,EAAC,CAAA7K,sBAAA,GAAAmB,YAAY,CAACsG,cAAc,cAAAzH,sBAAA,eAA3BA,sBAAA,CAA6BkM,oBAAoB,GAAG,OAAO,GAAG,MAAM,eAAC3M,OAAA;wBAAAmL,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACvGtL,OAAA;wBAAAiL,QAAA,EAAQ;sBAAU;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,KAAC,EAAC,EAAA5K,qBAAA,GAAAkB,YAAY,CAACgL,SAAS,cAAAlM,qBAAA,uBAAtBA,qBAAA,CAAwBmM,OAAO,KAAI,CAAC,EAAC,KACpE;oBAAA;sBAAA1B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,EAEL,EAAA3K,qBAAA,GAAAiB,YAAY,CAACkL,aAAa,cAAAnM,qBAAA,wBAAAC,sBAAA,GAA1BD,qBAAA,CAA4BuJ,MAAM,cAAAtJ,sBAAA,uBAAlCA,sBAAA,CAAoCoC,OAAO,kBAC1ChD,OAAA;oBAAKkL,SAAS,EAAC,MAAM;oBAAAD,QAAA,gBACnBjL,OAAA;sBAAAiL,QAAA,EAAQ;oBAAgB;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAAC,EAAAzK,sBAAA,GAAAe,YAAY,CAACkL,aAAa,CAAC5C,MAAM,CAAChG,IAAI,cAAArD,sBAAA,uBAAtCA,sBAAA,CAAwCkE,MAAM,KAAI,CAAC;kBAAA;oBAAAoG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnF,CACN;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CACR;cAAA,eACD,CACH,eAEDtL,OAAA;gBAAKkL,SAAS,EAAC,MAAM;gBAAAD,QAAA,gBACnBjL,OAAA,CAACX,MAAM;kBACL0N,OAAO,EAAE3F,iBAAkB;kBAC3BoE,OAAO,EAAC,SAAS;kBACjBY,QAAQ,EAAEtK,gBAAiB;kBAAAmJ,QAAA,EAC5B;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,EAERxK,YAAY,IAAI,CAACc,YAAY,iBAC5B5B,OAAA,CAACX,MAAM;kBACL0N,OAAO,EAAEjF,iBAAkB;kBAC3B0D,OAAO,EAAC,MAAM;kBACdN,SAAS,EAAC,MAAM;kBAChBkB,QAAQ,EAAEtK,gBAAiB;kBAAAmJ,QAAA,EAE1BnJ,gBAAgB,gBACf9B,OAAA,CAAAE,SAAA;oBAAA+K,QAAA,gBACEjL,OAAA;sBAAMkL,SAAS,EAAC,uCAAuC;sBAACoB,IAAI,EAAC;oBAAQ;sBAAAnB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,iBAE/E;kBAAA,eAAE,CAAC,GAEH;gBACD;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACK,CACT,EAEA1J,YAAY,iBACX5B,OAAA,CAACX,MAAM;kBACL0N,OAAO,EAAE3E,mBAAoB;kBAC7BoD,OAAO,EAAC,mBAAmB;kBAC3BN,SAAS,EAAC,MAAM;kBAChBkB,QAAQ,EAAEtK,gBAAiB;kBAAAmJ,QAAA,EAC5B;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CACT;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAGNtL,OAAA;gBAAKkL,SAAS,EAAC,MAAM;gBAAAD,QAAA,eACnBjL,OAAA;kBAAKkL,SAAS,EAAC,kBAAkB;kBAAAD,QAAA,gBAC/BjL,OAAA;oBAAAiL,QAAA,EAAQ;kBAAqB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,yCACtC,eAAAtL,OAAA;oBAAAmL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACNtL,OAAA;oBAAAiL,QAAA,EAAO;kBAAgF;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5F;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EAEL,IAAI,iBACHtL,OAAA,CAACV,IAAI,CAAC0M,KAAK;gBAACd,SAAS,EAAC,MAAM;gBAAAD,QAAA,gBAC1BjL,OAAA;kBAAKkL,SAAS,EAAC,wDAAwD;kBAAAD,QAAA,gBACrEjL,OAAA,CAACV,IAAI,CAAC2M,KAAK;oBAACf,SAAS,EAAC,MAAM;oBAAAD,QAAA,EAAC;kBAAyB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACnEtL,OAAA,CAACX,MAAM;oBACLmM,OAAO,EAAC,mBAAmB;oBAC3BlD,IAAI,EAAC,IAAI;oBACTyE,OAAO,EAAEzJ,eAAgB;oBACzB8I,QAAQ,EAAEpL,WAAW,IAAIU,aAAa,KAAK,WAAY;oBAAAuJ,QAAA,EACxD;kBAED;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,eACNtL,OAAA,CAACV,IAAI,CAAC0N,MAAM;kBACVpG,KAAK,EAAE3E,aAAc;kBACrBkK,QAAQ,EAAGxF,CAAC,IAAKzE,gBAAgB,CAACyE,CAAC,CAACE,MAAM,CAACD,KAAK,CAAE;kBAClDwF,QAAQ,EAAEpL,WAAY;kBAAAiK,QAAA,gBAEtBjL,OAAA;oBAAQ4G,KAAK,EAAC,EAAE;oBAAAqE,QAAA,EAAC;kBAAwB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,EACjD5I,gBAAgB,CAAC0B,GAAG,CAACC,MAAM,iBAC1BrE,OAAA;oBAAwB4G,KAAK,EAAEvC,MAAM,CAACC,EAAG;oBAAA2G,QAAA,GACtC5G,MAAM,CAACG,IAAI,EAAC,IAAE,EAACH,MAAM,CAACK,EAAE,EAAC,MAAI,EAACL,MAAM,CAACO,IAAI;kBAAA,GAD/BP,MAAM,CAACC,EAAE;oBAAA6G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEd,CACT,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACS,CAAC,eACdtL,OAAA,CAACV,IAAI,CAAC+M,IAAI;kBAACnB,SAAS,EAAC,YAAY;kBAAAD,QAAA,GAAC,0DAEhC,EAACvI,gBAAgB,CAACqC,MAAM,GAAG,CAAC,iBAC1B/E,OAAA;oBAAMkL,SAAS,EAAC,cAAc;oBAAAD,QAAA,GAAC,UAAG,EAACvI,gBAAgB,CAACqC,MAAM,EAAC,iBAAe;kBAAA;oBAAAoG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CACjF;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CACb,EAEArJ,aAAa,iBACZjC,OAAA,CAAAE,SAAA;gBAAA+K,QAAA,gBACEjL,OAAA,CAACV,IAAI,CAAC0M,KAAK;kBAACd,SAAS,EAAC,MAAM;kBAAAD,QAAA,gBAC1BjL,OAAA,CAACV,IAAI,CAAC2M,KAAK;oBAAAhB,QAAA,EAAC;kBAAuB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAChDtL,OAAA,CAACV,IAAI,CAAC2N,KAAK;oBACThG,IAAI,EAAC,UAAU;oBACfiG,KAAK,EAAC,2DAAsD;oBAC5DC,OAAO,EAAEhL,YAAY,CAACE,YAAa;oBACnC8J,QAAQ,EAAGxF,CAAC,IAAKvE,eAAe,CAACgL,IAAI,KAAK;sBACxC,GAAGA,IAAI;sBACP/K,YAAY,EAAEsE,CAAC,CAACE,MAAM,CAACsG;oBACzB,CAAC,CAAC,CAAE;oBACJf,QAAQ,EAAEpL;kBAAY;oBAAAmK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvB,CAAC,eACFtL,OAAA,CAACV,IAAI,CAAC2N,KAAK;oBACThG,IAAI,EAAC,UAAU;oBACfiG,KAAK,EAAC,wDAA8C;oBACpDC,OAAO,EAAEhL,YAAY,CAACG,WAAY;oBAClC6J,QAAQ,EAAGxF,CAAC,IAAKvE,eAAe,CAACgL,IAAI,KAAK;sBACxC,GAAGA,IAAI;sBACP9K,WAAW,EAAEqE,CAAC,CAACE,MAAM,CAACsG;oBACxB,CAAC,CAAC,CAAE;oBACJf,QAAQ,EAAEpL;kBAAY;oBAAAmK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvB,CAAC,eACFtL,OAAA,CAACV,IAAI,CAAC2N,KAAK;oBACThG,IAAI,EAAC,UAAU;oBACfiG,KAAK,EAAC,yDAA+C;oBACrDC,OAAO,EAAEhL,YAAY,CAACI,aAAc;oBACpC4J,QAAQ,EAAGxF,CAAC,IAAKvE,eAAe,CAACgL,IAAI,KAAK;sBACxC,GAAGA,IAAI;sBACP7K,aAAa,EAAEoE,CAAC,CAACE,MAAM,CAACsG;oBAC1B,CAAC,CAAC,CAAE;oBACJf,QAAQ,EAAEpL;kBAAY;oBAAAmK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACQ,CAAC,eAEbtL,OAAA,CAACV,IAAI,CAAC0M,KAAK;kBAACd,SAAS,EAAC,MAAM;kBAAAD,QAAA,gBAC1BjL,OAAA,CAACV,IAAI,CAAC2M,KAAK;oBAAAhB,QAAA,EAAC;kBAAyB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAClDtL,OAAA;oBAAAiL,QAAA,gBAEEjL,OAAA,CAACV,IAAI,CAAC4M,OAAO;sBACXjF,IAAI,EAAC,MAAM;sBACXoG,WAAW,EAAC,oDAAiD;sBAC7DzG,KAAK,EAAE3B,cAAe;sBACtBkH,QAAQ,EAAEzF,oBAAqB;sBAC/B0F,QAAQ,EAAEpL,WAAW,IAAIuE,iBAAkB;sBAC3C2F,SAAS,EAAC,MAAM;sBAChB5C,IAAI,EAAC;oBAAI;sBAAA6C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV,CAAC,eAGFtL,OAAA;sBAAK8K,KAAK,EAAE;wBAACwC,SAAS,EAAE,OAAO;wBAAEC,SAAS,EAAE,MAAM;wBAAEC,MAAM,EAAE,gBAAgB;wBAAEC,OAAO,EAAE,KAAK;wBAAEC,YAAY,EAAE;sBAAK,CAAE;sBAAAzC,QAAA,EAChH1F,iBAAiB,gBAChBvF,OAAA;wBAAKkL,SAAS,EAAC,wBAAwB;wBAAAD,QAAA,eACrCjL,OAAA;0BAAAiL,QAAA,EAAO;wBAAsB;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAClC,CAAC,GACJjG,kBAAkB,CAACN,MAAM,GAAG,CAAC,gBAC/B/E,OAAA,CAAAE,SAAA;wBAAA+K,QAAA,gBACEjL,OAAA;0BAAKkL,SAAS,EAAC,iBAAiB;0BAAAD,QAAA,eAC9BjL,OAAA;4BAAAiL,QAAA,GAAO,YACK,EAAC5F,kBAAkB,CAACN,MAAM,EAAC,MAAI,EAACI,aAAa,CAACJ,MAAM,EAAC,gBAC/D,EAACE,cAAc,IAAI,oBAAoBA,cAAc,IAAI;0BAAA;4BAAAkG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACpD;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACL,CAAC,EACLjG,kBAAkB,CAACsI,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,CAACvJ,GAAG,CAACwJ,QAAQ,iBAC5C5N,OAAA,CAACV,IAAI,CAAC2N,KAAK;0BAEThG,IAAI,EAAC,OAAO;0BACZzC,IAAI,EAAC,mBAAmB;0BACxB0I,KAAK,eACHlN,OAAA;4BAAAiL,QAAA,gBACEjL,OAAA;8BAAAiL,QAAA,EAAS2C,QAAQ,CAACpJ;4BAAI;8BAAA2G,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAS,CAAC,eAChCtL,OAAA;8BAAMkL,SAAS,EAAC,YAAY;8BAAAD,QAAA,GAAC,QAAM,EAAC2C,QAAQ,CAACtJ,EAAE,EAAC,GAAC;4BAAA;8BAAA6G,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAM,CAAC,eACxDtL,OAAA;8BAAK8K,KAAK,EAAE;gCAAC+C,QAAQ,EAAE,QAAQ;gCAAEC,KAAK,EAAE,MAAM;gCAAEC,SAAS,EAAE;8BAAK,CAAE;8BAAA9C,QAAA,GAC/D2C,QAAQ,CAAChI,WAAW,IAAI,CAAC,EAAC,eAC3B,EAACgI,QAAQ,CAAC/H,aAAa,iBACrB7F,OAAA;gCAAAiL,QAAA,GAAM,gBAAS,EAAC2C,QAAQ,CAAC/H,aAAa;8BAAA;gCAAAsF,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAO,CAC9C,EACAsC,QAAQ,CAAC9H,aAAa,iBACrB9F,OAAA;gCAAMkL,SAAS,EAAC,cAAc;gCAAAD,QAAA,EAAC;8BAAmB;gCAAAE,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAM,CACzD;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACE,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACH,CACN;0BACD6B,OAAO,EAAE3K,kBAAkB,CAACgE,QAAQ,CAACoH,QAAQ,CAACtJ,EAAE,CAAE;0BAClD6H,QAAQ,EAAGxF,CAAC,IAAK;4BACf,IAAIA,CAAC,CAACE,MAAM,CAACsG,OAAO,EAAE;8BACpB1K,qBAAqB,CAAC,CAACmL,QAAQ,CAACtJ,EAAE,CAAC,CAAC,CAAC,CAAC;4BACxC;0BACF,CAAE;0BACF8H,QAAQ,EAAEpL,WAAY;0BACtBkK,SAAS,EAAC;wBAAM,GAzBX0C,QAAQ,CAACtJ,EAAE;0BAAA6G,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OA0BjB,CACF,CAAC,EACDjG,kBAAkB,CAACN,MAAM,GAAG,GAAG,iBAC9B/E,OAAA;0BAAKkL,SAAS,EAAC,6BAA6B;0BAAAD,QAAA,eAC1CjL,OAAA;4BAAAiL,QAAA,EAAO;0BAAoE;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAChF,CACN;sBAAA,eACD,CAAC,gBAEHtL,OAAA;wBAAKkL,SAAS,EAAC,wBAAwB;wBAAAD,QAAA,eACrCjL,OAAA;0BAAAiL,QAAA,EACGhG,cAAc,GACb,mDAAmDA,cAAc,GAAG,GACpE;wBAA+B;0BAAAkG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAE5B;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACL;oBACN;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNtL,OAAA,CAACV,IAAI,CAAC+M,IAAI;oBAACnB,SAAS,EAAC,YAAY;oBAAAD,QAAA,EAAC;kBAElC;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC;cAAA,eACb,CACH,eAEDtL,OAAA,CAACV,IAAI,CAAC0M,KAAK;gBAACd,SAAS,EAAC,MAAM;gBAAAD,QAAA,gBAC1BjL,OAAA,CAACV,IAAI,CAAC2M,KAAK;kBAAAhB,QAAA,EAAC;gBAAkB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC3CtL,OAAA,CAACV,IAAI,CAAC2N,KAAK;kBACThG,IAAI,EAAC,UAAU;kBACfiG,KAAK,EAAC,yCAA+B;kBACrCc,cAAc;gBAAA;kBAAA7C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf,CAAC,eACFtL,OAAA,CAACV,IAAI,CAAC2N,KAAK;kBACThG,IAAI,EAAC,UAAU;kBACfiG,KAAK,EAAC,qCAA2B;kBACjCc,cAAc;gBAAA;kBAAA7C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf,CAAC,eACFtL,OAAA,CAACV,IAAI,CAAC2N,KAAK;kBACThG,IAAI,EAAC,UAAU;kBACfiG,KAAK,EAAC,qCAA2B;kBACjCc,cAAc;gBAAA;kBAAA7C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ,CAAC,EAEZtK,WAAW,iBACVhB,OAAA;gBAAKkL,SAAS,EAAC,MAAM;gBAAAD,QAAA,gBACnBjL,OAAA,CAACV,IAAI,CAAC2M,KAAK;kBAAAhB,QAAA,EAAC;gBAAe;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACxCtL,OAAA,CAACR,WAAW;kBACVyO,GAAG,EAAE/M,cAAe;kBACpBgM,KAAK,EAAE,GAAGhM,cAAc,GAAI;kBAC5BsK,OAAO,EAAEtK,cAAc,KAAK,GAAG,GAAG,SAAS,GAAG,SAAU;kBACxDgN,QAAQ,EAAEhN,cAAc,GAAG;gBAAI;kBAAAiK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CACN,eAEDtL,OAAA,CAACX,MAAM;gBACLmM,OAAO,EAAC,SAAS;gBACjBlD,IAAI,EAAC,IAAI;gBACTyE,OAAO,EAAEvE,YAAa;gBACtB4D,QAAQ,EAAE,CAACtL,YAAY,IAAI,CAACc,YAAY,IAAI,CAACK,aAAa,IAAIjB,WAAW,IAAIc,gBAAiB;gBAC9FoJ,SAAS,EAAC,OAAO;gBAAAD,QAAA,EAEhBjK,WAAW,gBACVhB,OAAA,CAAAE,SAAA;kBAAA+K,QAAA,gBACEjL,OAAA;oBAAMkL,SAAS,EAAC,uCAAuC;oBAACoB,IAAI,EAAC;kBAAQ;oBAAAnB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,kBAC/D,EAACpK,cAAc,EAAC,GAChC;gBAAA,eAAE,CAAC,GACD,CAACJ,YAAY,GACf,8BAA8B,GAC5B,CAACc,YAAY,GACf,+BAA+B,GAC7B,CAACK,aAAa,GAChB,2BAA2B,GAE3B;cACD;gBAAAkJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAENtL,OAAA,CAACb,GAAG;QAACyM,EAAE,EAAE,CAAE;QAAAX,QAAA,eACTjL,OAAA,CAACZ,IAAI;UAAC8L,SAAS,EAAC,iBAAiB;UAAAD,QAAA,gBAC/BjL,OAAA,CAACZ,IAAI,CAACyM,MAAM;YAACX,SAAS,EAAC,oBAAoB;YAAAD,QAAA,eACzCjL,OAAA;cAAIkL,SAAS,EAAC,MAAM;cAAAD,QAAA,EAAC;YAAwB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC,eACdtL,OAAA,CAACZ,IAAI,CAAC2M,IAAI;YAAAd,QAAA,gBACRjL,OAAA;cAAAiL,QAAA,EAAI;YAAyB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAClCtL,OAAA;cAAAiL,QAAA,gBACEjL,OAAA;gBAAAiL,QAAA,gBAAIjL,OAAA;kBAAAiL,QAAA,EAAQ;gBAAU;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,oCAAgC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACpEtL,OAAA;gBAAAiL,QAAA,gBAAIjL,OAAA;kBAAAiL,QAAA,EAAQ;gBAAe;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,qCAAiC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC1EtL,OAAA;gBAAAiL,QAAA,gBAAIjL,OAAA;kBAAAiL,QAAA,EAAQ;gBAAY;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,iCAA6B;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACnEtL,OAAA;gBAAAiL,QAAA,gBAAIjL,OAAA;kBAAAiL,QAAA,EAAQ;gBAAY;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,qCAAiC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrE,CAAC,eAELtL,OAAA;cAAAiL,QAAA,EAAI;YAAuB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAChCtL,OAAA;cAAAiL,QAAA,gBACEjL,OAAA;gBAAAiL,QAAA,gBAAIjL,OAAA;kBAAAiL,QAAA,EAAQ;gBAAoB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,8BAA0B;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACxEtL,OAAA;gBAAAiL,QAAA,gBAAIjL,OAAA;kBAAAiL,QAAA,EAAQ;gBAAwB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,+BAA2B;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC7EtL,OAAA;gBAAAiL,QAAA,gBAAIjL,OAAA;kBAAAiL,QAAA,EAAQ;gBAAoB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,+BAA2B;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACzEtL,OAAA;gBAAAiL,QAAA,gBAAIjL,OAAA;kBAAAiL,QAAA,EAAQ;gBAAuB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,uBAAmB;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClE,CAAC,eAELtL,OAAA;cAAAiL,QAAA,EAAI;YAAmB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5BtL,OAAA;cAAAiL,QAAA,gBACEjL,OAAA;gBAAAiL,QAAA,EAAI;cAA8B;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACvCtL,OAAA;gBAAAiL,QAAA,EAAI;cAAyC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAClDtL,OAAA;gBAAAiL,QAAA,EAAI;cAAqC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC9CtL,OAAA;gBAAAiL,QAAA,EAAI;cAAwC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENtL,OAAA,CAACd,GAAG;MAAA+L,QAAA,eACFjL,OAAA,CAACb,GAAG;QAAA8L,QAAA,eACFjL,OAAA,CAACZ,IAAI;UAAC8L,SAAS,EAAC,WAAW;UAAAD,QAAA,gBACzBjL,OAAA,CAACZ,IAAI,CAACyM,MAAM;YAACX,SAAS,EAAC,yBAAyB;YAAAD,QAAA,eAC9CjL,OAAA;cAAIkL,SAAS,EAAC,MAAM;cAAAD,QAAA,EAAC;YAA0B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzC,CAAC,eACdtL,OAAA,CAACZ,IAAI,CAAC2M,IAAI;YAAAd,QAAA,eACRjL,OAAA,CAACP,KAAK;cAAC0O,OAAO;cAACC,KAAK;cAAAnD,QAAA,gBAClBjL,OAAA;gBAAAiL,QAAA,eACEjL,OAAA;kBAAAiL,QAAA,gBACEjL,OAAA;oBAAAiL,QAAA,EAAI;kBAAO;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAChBtL,OAAA;oBAAAiL,QAAA,EAAI;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAClBtL,OAAA;oBAAAiL,QAAA,EAAI;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAClBtL,OAAA;oBAAAiL,QAAA,EAAI;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAClBtL,OAAA;oBAAAiL,QAAA,EAAI;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,eACRtL,OAAA;gBAAAiL,QAAA,gBACEjL,OAAA;kBAAAiL,QAAA,gBACEjL,OAAA;oBAAAiL,QAAA,eAAIjL,OAAA;sBAAAiL,QAAA,EAAQ;oBAAqB;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC/CtL,OAAA;oBAAAiL,QAAA,EAAI;kBAAgB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACzBtL,OAAA;oBAAAiL,QAAA,eAAIjL,OAAA;sBAAMkL,SAAS,EAAC,kBAAkB;sBAAAD,QAAA,EAAC;oBAAQ;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC3DtL,OAAA;oBAAAiL,QAAA,EAAI;kBAAW;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACpBtL,OAAA;oBAAAiL,QAAA,gBACEjL,OAAA,CAACX,MAAM;sBAACiJ,IAAI,EAAC,IAAI;sBAACkD,OAAO,EAAC,iBAAiB;sBAACN,SAAS,EAAC,MAAM;sBAAAD,QAAA,EAAC;oBAAE;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACxEtL,OAAA,CAACX,MAAM;sBAACiJ,IAAI,EAAC,IAAI;sBAACkD,OAAO,EAAC,gBAAgB;sBAAAP,QAAA,EAAC;oBAAC;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACLtL,OAAA;kBAAAiL,QAAA,gBACEjL,OAAA;oBAAAiL,QAAA,eAAIjL,OAAA;sBAAAiL,QAAA,EAAQ;oBAAiB;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC3CtL,OAAA;oBAAAiL,QAAA,EAAI;kBAAgB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACzBtL,OAAA;oBAAAiL,QAAA,eAAIjL,OAAA;sBAAMkL,SAAS,EAAC,kBAAkB;sBAAAD,QAAA,EAAC;oBAAY;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC/DtL,OAAA;oBAAAiL,QAAA,EAAI;kBAAY;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACrBtL,OAAA;oBAAAiL,QAAA,eACEjL,OAAA,CAACX,MAAM;sBAACiJ,IAAI,EAAC,IAAI;sBAACkD,OAAO,EAAC,cAAc;sBAAAP,QAAA,EAAC;oBAAE;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAClL,EAAA,CAx3BID,YAAY;AAAAkO,EAAA,GAAZlO,YAAY;AA03BlB,eAAeA,YAAY;AAAC,IAAAkO,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}