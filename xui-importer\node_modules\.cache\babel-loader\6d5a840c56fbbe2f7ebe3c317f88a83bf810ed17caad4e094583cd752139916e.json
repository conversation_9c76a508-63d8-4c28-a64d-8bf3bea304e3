{"ast": null, "code": "var _jsxFileName = \"F:\\\\WORKSPACE\\\\XUI IMPORTER\\\\xui-importer\\\\src\\\\components\\\\ImportVODM3U.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Row, Col, Card, Button, Form, Alert, ProgressBar, Table, Badge } from 'react-bootstrap';\nimport BackendStatus from './BackendStatus';\nimport { checkSystemHealth } from '../utils/seriesLogic';\nimport { api, databaseAPI } from '../services/apiService';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ImportVODM3U = () => {\n  _s();\n  var _fileAnalysis$basic_a, _fileAnalysis$basic_a2, _fileAnalysis$basic_a3, _fileAnalysis$basic_a4, _fileAnalysis$basic_a5, _fileAnalysis$file_in, _fileAnalysis$parse_r, _fileAnalysis$parse_r2, _fileAnalysis$parse_r3;\n  const [selectedFile, setSelectedFile] = useState(null);\n  const [isImporting, setIsImporting] = useState(false);\n  const [importProgress, setImportProgress] = useState(0);\n  const [showAlert, setShowAlert] = useState(false);\n  const [alertMessage, setAlertMessage] = useState('');\n  const [alertType, setAlertType] = useState('info');\n\n  // Estados para backend\n  const [backendStatus, setBackendStatus] = useState('checking');\n  const [fileAnalysis, setFileAnalysis] = useState(null);\n  const [isProcessingFile, setIsProcessingFile] = useState(false);\n\n  // Configuración fija para VOD/Movies\n  const contentType = 'movie';\n  const [streamsServer, setStreamsServer] = useState('');\n  const [sourceConfig, setSourceConfig] = useState({\n    directSource: true,\n    directProxy: false,\n    loadBalancing: false\n  });\n  const [selectedCategories, setSelectedCategories] = useState([]);\n\n  // Estados para datos dinámicos del backend\n  const [availableServers, setAvailableServers] = useState([]);\n  // const [existingCategories, setExistingCategories] = useState([]);\n\n  const checkBackendStatus = async () => {\n    try {\n      const health = await checkSystemHealth();\n      setBackendStatus(health.success ? 'connected' : 'error');\n      if (!health.success) {\n        displayAlert('warning', 'Backend no disponible. Funcionando en modo offline.');\n      }\n    } catch (error) {\n      setBackendStatus('error');\n      displayAlert('danger', 'No se puede conectar al backend');\n    }\n  };\n  const loadInitialData = async () => {\n    try {\n      console.log(`🔄 Cargando datos iniciales. Backend status: ${backendStatus}`);\n\n      // Cargar servidores y categorías desde backend si está disponible\n      if (backendStatus === 'connected') {\n        console.log('✅ Backend conectado, cargando datos reales...');\n        await loadRealServers();\n        await loadRealCategories();\n      } else {\n        console.log('⚠️ Backend no conectado, usando datos mock...');\n        // Fallback a mock data si no hay conexión\n        loadMockData();\n      }\n    } catch (error) {\n      console.error('Error cargando datos iniciales:', error);\n      if (window.debugLog) {\n        window.debugLog('error', `Error cargando datos iniciales: ${error.message}`);\n      }\n      // Fallback a mock data en caso de error\n      loadMockData();\n    }\n  };\n\n  // Cargar servidores reales desde la base de datos\n  const loadRealServers = async () => {\n    try {\n      console.log('🔄 Iniciando carga de servidores reales...');\n      const response = await fetch('http://localhost:5001/api/database/streaming-servers');\n      console.log('📡 Respuesta del servidor:', response.status, response.statusText);\n      const result = await response.json();\n      console.log('📊 Datos recibidos:', result);\n      if (result.success && result.data) {\n        const servers = result.data.map(server => ({\n          id: server.server_id,\n          name: server.server_name || `Server ${server.server_id}`,\n          ip: server.server_ip || 'Unknown IP',\n          load: `${server.total_streams || 0} streams`,\n          // Mostrar cantidad de streams como \"carga\"\n          total_streams: server.total_streams || 0,\n          status: server.server_status === 1 ? 'Active' : 'Inactive'\n        }));\n        console.log('🖥️ Servidores mapeados:', servers);\n        setAvailableServers(servers);\n        console.log('✅ Estado actualizado con', servers.length, 'servidores');\n        if (window.debugLog) {\n          window.debugLog('success', `✅ Cargados ${servers.length} servidores reales desde BD`);\n        }\n      } else {\n        console.error('❌ Respuesta no exitosa:', result);\n        throw new Error(result.error || 'No se pudieron cargar servidores');\n      }\n    } catch (error) {\n      console.error('❌ Error cargando servidores:', error);\n      if (window.debugLog) {\n        window.debugLog('error', `❌ Error cargando servidores: ${error.message}`);\n      }\n      throw error;\n    }\n  };\n\n  // Estados para manejo de categorías\n  const [categorySearch, setCategorySearch] = useState('');\n  const [allCategories, setAllCategories] = useState([]);\n  const [filteredCategories, setFilteredCategories] = useState([]);\n  const [categoriesLoading, setCategoriesLoading] = useState(false);\n\n  // Cargar TODAS las categorías desde la base de datos con nombres reales\n  const loadRealCategories = async () => {\n    try {\n      setCategoriesLoading(true);\n      console.log('🔄 Cargando TODAS las categorías con nombres reales...');\n      const result = await databaseAPI.getCategories();\n      if (result.success && result.categories) {\n        const categories = result.categories.map(cat => ({\n          id: cat.id,\n          name: cat.name,\n          usage_count: cat.usage_count,\n          category_type: cat.category_type,\n          has_real_name: cat.has_real_name,\n          category_id_raw: `[${cat.id}]` // Formato JSON array\n        }));\n        setAllCategories(categories);\n        setFilteredCategories(categories);\n        setExistingCategories(categories);\n        console.log('✅ Categorías cargadas:', categories.length);\n        if (window.debugLog) {\n          window.debugLog('success', `✅ Cargadas ${categories.length} categorías reales desde BD`);\n        }\n      } else {\n        throw new Error('No se pudieron cargar categorías');\n      }\n    } catch (error) {\n      console.error('Error cargando categorías:', error);\n      if (window.debugLog) {\n        window.debugLog('error', `❌ Error cargando categorías: ${error.message}`);\n      }\n      // Fallback a categorías mock\n      loadMockCategories();\n    } finally {\n      setCategoriesLoading(false);\n    }\n  };\n\n  // Filtrar categorías basándose en la búsqueda\n  const filterCategories = searchTerm => {\n    if (!searchTerm.trim()) {\n      setFilteredCategories(allCategories);\n      return;\n    }\n    const searchLower = searchTerm.toLowerCase();\n    const filtered = allCategories.filter(cat => cat.name.toLowerCase().includes(searchLower) || cat.id.toString().includes(searchTerm) || cat.category_type && cat.category_type.toLowerCase().includes(searchLower));\n    setFilteredCategories(filtered);\n  };\n\n  // Manejar cambio en búsqueda de categorías\n  const handleCategorySearch = e => {\n    const value = e.target.value;\n    setCategorySearch(value);\n    filterCategories(value);\n  };\n\n  // Cargar categorías mock como fallback\n  const loadMockCategories = () => {\n    const mockCategories = [{\n      id: 1964,\n      name: 'Películas Generales',\n      usage_count: 2279\n    }, {\n      id: 1763,\n      name: 'Acción',\n      usage_count: 2242\n    }, {\n      id: 1762,\n      name: 'Drama',\n      usage_count: 2142\n    }, {\n      id: 1767,\n      name: 'Comedia',\n      usage_count: 1333\n    }, {\n      id: 1855,\n      name: 'Terror/Horror',\n      usage_count: 1200\n    }];\n    setAllCategories(mockCategories);\n    setFilteredCategories(mockCategories);\n    setExistingCategories(mockCategories);\n    console.log('⚠️ Usando categorías mock:', mockCategories);\n  };\n\n  // Detectar tipo de categoría basado en el nombre\n  const detectCategoryType = categoryName => {\n    const name = categoryName.toLowerCase();\n    if (name.includes('movie') || name.includes('film') || name.includes('cinema')) {\n      return 'movie';\n    } else if (name.includes('series') || name.includes('show') || name.includes('drama')) {\n      return 'series';\n    } else if (name.includes('live') || name.includes('tv') || name.includes('channel') || name.includes('news') || name.includes('sport')) {\n      return 'live';\n    } else if (name.includes('radio') || name.includes('music') || name.includes('fm')) {\n      return 'radio';\n    }\n    return 'movie'; // Default a movie para VOD si no se puede detectar\n  };\n\n  // Datos mock como fallback\n  const loadMockData = () => {\n    setAvailableServers([{\n      id: 1,\n      name: 'Main Server US',\n      ip: '*************',\n      load: '45%'\n    }, {\n      id: 2,\n      name: 'EU Server',\n      ip: '*************',\n      load: '32%'\n    }, {\n      id: 3,\n      name: 'Asia Server',\n      ip: '*************',\n      load: '67%'\n    }, {\n      id: 4,\n      name: 'Backup Server',\n      ip: '*************',\n      load: '12%'\n    }]);\n    setExistingCategories([{\n      id: 1,\n      name: 'Action Movies',\n      type: 'movie'\n    }, {\n      id: 2,\n      name: 'Comedy Movies',\n      type: 'movie'\n    }, {\n      id: 3,\n      name: 'Drama Movies',\n      type: 'movie'\n    }, {\n      id: 4,\n      name: 'Horror Movies',\n      type: 'movie'\n    }, {\n      id: 5,\n      name: 'Sci-Fi Movies',\n      type: 'movie'\n    }, {\n      id: 6,\n      name: 'Documentary',\n      type: 'movie'\n    }, {\n      id: 7,\n      name: 'Animation',\n      type: 'movie'\n    }, {\n      id: 8,\n      name: 'Thriller',\n      type: 'movie'\n    }, {\n      id: 9,\n      name: 'Romance',\n      type: 'movie'\n    }]);\n    if (window.debugLog) {\n      window.debugLog('warning', '⚠️ Usando datos mock - backend no disponible');\n    }\n  };\n  const showAlertMessage = (message, type = 'info') => {\n    setAlertMessage(message);\n    setAlertType(type);\n    setShowAlert(true);\n    setTimeout(() => setShowAlert(false), 5000);\n  };\n\n  // Helper function to show alert with better formatting\n  const displayAlert = (type, message) => {\n    showAlertMessage(message, type);\n  };\n\n  // File handling functions\n  const handleBrowseFiles = () => {\n    const fileInput = document.createElement('input');\n    fileInput.type = 'file';\n    fileInput.accept = '.m3u,.m3u8';\n    fileInput.onchange = e => {\n      const file = e.target.files[0];\n      if (file) {\n        handleFileSelect({\n          target: {\n            files: [file]\n          }\n        });\n      }\n    };\n    fileInput.click();\n  };\n  const handleAnalyzeFile = async () => {\n    if (!selectedFile) return;\n    setIsProcessingFile(true);\n    setFileAnalysis(null);\n    try {\n      displayAlert('info', 'Analizando archivo M3U...');\n      const response = await api.m3uAPI.analyzeFile(selectedFile);\n      if (response.success) {\n        var _response$data$basic_;\n        setFileAnalysis(response.data);\n\n        // Análisis completado - configurado para VOD/Movies\n        displayAlert('success', `✅ Archivo analizado correctamente. Se detectaron ${((_response$data$basic_ = response.data.basic_analysis) === null || _response$data$basic_ === void 0 ? void 0 : _response$data$basic_.estimated_entries) || 0} entradas. Configurado para importar como películas/VOD.`);\n      } else {\n        throw new Error(response.error || 'Error analizando archivo');\n      }\n    } catch (error) {\n      console.error('Error analyzing file:', error);\n      displayAlert('danger', `❌ Error analizando archivo: ${error.message}`);\n    } finally {\n      setIsProcessingFile(false);\n    }\n  };\n  const handleClearAnalysis = () => {\n    setFileAnalysis(null);\n    setSelectedFile(null);\n    // Content type fijo para VOD\n    displayAlert('info', 'Análisis limpiado. Selecciona un nuevo archivo.');\n  };\n\n  // Verificar estado del backend al cargar\n  useEffect(() => {\n    checkBackendStatus();\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n\n  // Cargar datos cuando el backend status cambie\n  useEffect(() => {\n    if (backendStatus !== 'checking') {\n      loadInitialData();\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [backendStatus]);\n  const handleFileSelect = async event => {\n    const file = event.target.files[0];\n    setSelectedFile(file);\n    setFileAnalysis(null);\n    if (file) {\n      displayAlert('info', `Archivo seleccionado: ${file.name} (${(file.size / 1024 / 1024).toFixed(2)} MB)`);\n\n      // Solo mostrar información básica, el análisis se hace manualmente\n      if (window.debugLog) {\n        window.debugLog(`📁 File selected: ${file.name}`, 'info');\n        window.debugLog(`📊 File size: ${(file.size / 1024 / 1024).toFixed(2)} MB`, 'info');\n      }\n    }\n  };\n  const handleImport = async () => {\n    if (!selectedFile || !streamsServer) {\n      displayAlert('warning', '⚠️ Por favor completa todos los campos requeridos (archivo y servidor).');\n      return;\n    }\n    if (!fileAnalysis) {\n      displayAlert('warning', '⚠️ Por favor analiza el archivo antes de importar.');\n      return;\n    }\n    if (window.debugLog) {\n      window.debugLog(`📥 Starting VOD import of ${selectedFile.name}`, 'info');\n      window.debugLog(`📊 File size: ${(selectedFile.size / 1024 / 1024).toFixed(2)} MB`, 'info');\n      window.debugLog(`🎯 Content type: ${contentType}`, 'info');\n      window.debugLog(`🖥️ Target server: ${streamsServer}`, 'info');\n    }\n    setIsImporting(true);\n    setImportProgress(0);\n    try {\n      // Preparar configuración de importación para VOD\n      const importConfig = {\n        contentType,\n        streamsServer,\n        sourceConfig,\n        categories: selectedCategories,\n        tmdbEnabled: true,\n        autoAssignCategories: true\n      };\n      displayAlert('info', '🔍 Iniciando proceso de importación VOD...');\n      setImportProgress(10);\n\n      // Paso 1: Subir archivo\n      const formData = new FormData();\n      formData.append('file', selectedFile);\n      formData.append('config', JSON.stringify(importConfig));\n      if (window.debugLog) {\n        window.debugLog('📤 Uploading VOD file to backend...', 'info');\n      }\n\n      // Paso 1: Analizar archivo M3U\n      const analyzeResponse = await api.m3uAPI.analyzeFile(selectedFile);\n      setImportProgress(30);\n      if (!analyzeResponse.success) {\n        throw new Error(analyzeResponse.error || 'Error analyzing file');\n      }\n      displayAlert('info', '🎯 Archivo subido, procesando contenido VOD...');\n      // Paso 2: Leer contenido del archivo para parsear películas\n      const fileContent = await new Promise((resolve, reject) => {\n        const reader = new FileReader();\n        reader.onload = e => resolve(e.target.result);\n        reader.onerror = () => reject(new Error('Error reading file'));\n        reader.readAsText(selectedFile);\n      });\n      setImportProgress(40);\n\n      // Paso 3: Parsear contenido del M3U como películas/VOD\n      const parseResponse = await fetch('http://localhost:5001/api/import/parse-movies', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          m3uContent: fileContent\n        })\n      });\n      const parseResult = await parseResponse.json();\n      if (!parseResult.success) {\n        throw new Error(parseResult.error || 'Error parsing VOD content');\n      }\n      setImportProgress(60);\n\n      // Paso 4: Importar películas a la base de datos (tabla streams con type=2)\n      const importPayload = {\n        movies: parseResult.data.movies,\n        server_id: parseInt(streamsServer),\n        category_id: selectedCategories.length > 0 ? parseInt(selectedCategories[0]) : null,\n        tmdb_search: true // Usar integración TMDB existente\n      };\n      const importResponse = await fetch('http://localhost:5001/api/import/movies', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify(importPayload)\n      });\n      const importResult = await importResponse.json();\n\n      // Debug: Log de la respuesta completa\n      console.log('🔍 VOD Import Response:', importResult);\n      if (!importResult.success) {\n        throw new Error(importResult.error || 'Error importing VOD content');\n      }\n\n      // Paso 5: Finalizar importación\n      setImportProgress(100);\n\n      // Mostrar estadísticas de películas (acceso seguro)\n      const stats = importResult.data || importResult;\n      const successMessage = `✅ Importación VOD completada exitosamente!\\n📊 Estadísticas:\\n• ${stats.imported || 0} elementos importados\\n• ${stats.errors || 0} errores\\n• ${stats.movies_created || 0} películas creadas\\n• ${stats.metadata_enriched || 0} con metadata TMDB`;\n      displayAlert('success', successMessage);\n      if (window.debugLog) {\n        window.debugLog(`✅ VOD Import completed successfully: ${selectedFile.name}`, 'success');\n        window.debugLog(`📊 Stats: ${JSON.stringify(importResult)}`, 'info');\n      }\n\n      // Limpiar estado después de importación exitosa\n      setTimeout(() => {\n        setSelectedFile(null);\n        setFileAnalysis(null);\n        // Content type fijo para VOD\n        setStreamsServer('');\n        setSelectedCategories([]);\n      }, 3000);\n    } catch (error) {\n      console.error('VOD Import error:', error);\n      displayAlert('danger', `❌ Error durante la importación VOD: ${error.message}`);\n      if (window.debugLog) {\n        window.debugLog(`❌ VOD Import failed: ${error.message}`, 'error');\n      }\n    } finally {\n      setIsImporting(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      width: '100%',\n      maxWidth: 'none'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"d-flex justify-content-between align-items-center mb-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"text-danger\",\n        children: \"\\uD83C\\uDFAC Import VOD M3U Files\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 473,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(BackendStatus, {\n        status: backendStatus,\n        onRetry: checkBackendStatus\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 474,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 472,\n      columnNumber: 7\n    }, this), showAlert && /*#__PURE__*/_jsxDEV(Alert, {\n      variant: alertType,\n      dismissible: true,\n      onClose: () => setShowAlert(false),\n      children: [/*#__PURE__*/_jsxDEV(Alert.Heading, {\n        children: [alertType === 'success' && '✅ Success!', alertType === 'danger' && '❌ Error!', alertType === 'warning' && '⚠️ Warning!', alertType === 'info' && 'ℹ️ Information']\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 482,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: alertMessage\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 488,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 481,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      className: \"mb-4\",\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        lg: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"shadow-sm h-100\",\n          children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n            className: \"bg-danger text-white d-flex justify-content-between align-items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n              className: \"mb-0\",\n              children: \"\\uD83D\\uDCC2 File Upload\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 496,\n              columnNumber: 15\n            }, this), backendStatus === 'connected' && /*#__PURE__*/_jsxDEV(Badge, {\n              bg: \"success\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"bi bi-cloud-check\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 499,\n                columnNumber: 19\n              }, this), \" Backend Ready\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 498,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 495,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n            children: /*#__PURE__*/_jsxDEV(Form, {\n              children: [/*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Select M3U File\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 506,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  type: \"file\",\n                  accept: \".m3u,.m3u8\",\n                  onChange: handleFileSelect,\n                  disabled: isImporting\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 507,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Text, {\n                  className: \"text-muted\",\n                  children: \"Supported formats: .m3u, .m3u8 (Max size: 50MB)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 513,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 505,\n                columnNumber: 17\n              }, this), selectedFile && /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(Alert, {\n                  variant: \"info\",\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Selected File:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 521,\n                    columnNumber: 23\n                  }, this), \" \", selectedFile.name, /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 521,\n                    columnNumber: 74\n                  }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Size:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 522,\n                    columnNumber: 23\n                  }, this), \" \", (selectedFile.size / 1024 / 1024).toFixed(2), \" MB\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 520,\n                  columnNumber: 21\n                }, this), isProcessingFile && /*#__PURE__*/_jsxDEV(Alert, {\n                  variant: \"secondary\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"d-flex align-items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"spinner-border spinner-border-sm me-2\",\n                      role: \"status\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 528,\n                      columnNumber: 27\n                    }, this), \"Analizando archivo...\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 527,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 526,\n                  columnNumber: 23\n                }, this), fileAnalysis && !isProcessingFile && /*#__PURE__*/_jsxDEV(Alert, {\n                  variant: \"success\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                    children: \"\\uD83D\\uDCCA An\\xE1lisis del Archivo\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 536,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Row, {\n                    children: [/*#__PURE__*/_jsxDEV(Col, {\n                      md: 6,\n                      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: \"Total Lines:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 539,\n                        columnNumber: 29\n                      }, this), \" \", ((_fileAnalysis$basic_a = fileAnalysis.basic_analysis) === null || _fileAnalysis$basic_a === void 0 ? void 0 : _fileAnalysis$basic_a.total_lines) || 0, /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 539,\n                        columnNumber: 106\n                      }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: \"EXTINF Entries:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 540,\n                        columnNumber: 29\n                      }, this), \" \", ((_fileAnalysis$basic_a2 = fileAnalysis.basic_analysis) === null || _fileAnalysis$basic_a2 === void 0 ? void 0 : _fileAnalysis$basic_a2.extinf_lines) || 0, /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 540,\n                        columnNumber: 110\n                      }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: \"URL Entries:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 541,\n                        columnNumber: 29\n                      }, this), \" \", ((_fileAnalysis$basic_a3 = fileAnalysis.basic_analysis) === null || _fileAnalysis$basic_a3 === void 0 ? void 0 : _fileAnalysis$basic_a3.url_lines) || 0]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 538,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Col, {\n                      md: 6,\n                      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: \"Estimated Entries:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 544,\n                        columnNumber: 29\n                      }, this), \" \", ((_fileAnalysis$basic_a4 = fileAnalysis.basic_analysis) === null || _fileAnalysis$basic_a4 === void 0 ? void 0 : _fileAnalysis$basic_a4.estimated_entries) || 0, /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 544,\n                        columnNumber: 118\n                      }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: \"Valid M3U:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 545,\n                        columnNumber: 29\n                      }, this), \" \", (_fileAnalysis$basic_a5 = fileAnalysis.basic_analysis) !== null && _fileAnalysis$basic_a5 !== void 0 && _fileAnalysis$basic_a5.has_valid_m3u_header ? '✅ Yes' : '❌ No', /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 545,\n                        columnNumber: 127\n                      }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: \"File Size:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 546,\n                        columnNumber: 29\n                      }, this), \" \", ((_fileAnalysis$file_in = fileAnalysis.file_info) === null || _fileAnalysis$file_in === void 0 ? void 0 : _fileAnalysis$file_in.size_mb) || 0, \" MB\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 543,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 537,\n                    columnNumber: 25\n                  }, this), ((_fileAnalysis$parse_r = fileAnalysis.parse_results) === null || _fileAnalysis$parse_r === void 0 ? void 0 : (_fileAnalysis$parse_r2 = _fileAnalysis$parse_r.movies) === null || _fileAnalysis$parse_r2 === void 0 ? void 0 : _fileAnalysis$parse_r2.success) && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"mt-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Movies Detected:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 552,\n                      columnNumber: 29\n                    }, this), \" \", ((_fileAnalysis$parse_r3 = fileAnalysis.parse_results.movies.data) === null || _fileAnalysis$parse_r3 === void 0 ? void 0 : _fileAnalysis$parse_r3.length) || 0]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 551,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 535,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Button, {\n                  onClick: handleBrowseFiles,\n                  variant: \"danger\",\n                  disabled: isProcessingFile,\n                  children: \"Seleccionar Archivo M3U\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 561,\n                  columnNumber: 19\n                }, this), selectedFile && !fileAnalysis && /*#__PURE__*/_jsxDEV(Button, {\n                  onClick: handleAnalyzeFile,\n                  variant: \"info\",\n                  className: \"ms-2\",\n                  disabled: isProcessingFile,\n                  children: isProcessingFile ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"spinner-border spinner-border-sm me-2\",\n                      role: \"status\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 578,\n                      columnNumber: 27\n                    }, this), \"Analizando...\"]\n                  }, void 0, true) : 'Analizar Archivo'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 570,\n                  columnNumber: 21\n                }, this), fileAnalysis && /*#__PURE__*/_jsxDEV(Button, {\n                  onClick: handleClearAnalysis,\n                  variant: \"outline-secondary\",\n                  className: \"ms-2\",\n                  disabled: isProcessingFile,\n                  children: \"Limpiar An\\xE1lisis\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 588,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 560,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mb-3\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"alert alert-info\",\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"\\uD83C\\uDFAF Tipo de Contenido:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 602,\n                    columnNumber: 21\n                  }, this), \" \\uD83C\\uDFAC Pel\\xEDculas/VOD (fijo)\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 603,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                    children: \"Este importador est\\xE1 configurado espec\\xEDficamente para pel\\xEDculas y contenido VOD.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 604,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 601,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 600,\n                columnNumber: 17\n              }, this), true && /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex justify-content-between align-items-center mb-2\",\n                  children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                    className: \"mb-0\",\n                    children: \"\\uD83D\\uDDA5\\uFE0F Target Streams Server\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 611,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Button, {\n                    variant: \"outline-secondary\",\n                    size: \"sm\",\n                    onClick: loadRealServers,\n                    disabled: isImporting || backendStatus !== 'connected',\n                    children: \"\\uD83D\\uDD04 Refresh\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 612,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 610,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n                  value: streamsServer,\n                  onChange: e => setStreamsServer(e.target.value),\n                  disabled: isImporting,\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\",\n                    children: \"Select streams server...\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 626,\n                    columnNumber: 23\n                  }, this), availableServers.map(server => /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: server.id,\n                    children: [server.name, \" (\", server.ip, \") - \", server.load]\n                  }, server.id, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 628,\n                    columnNumber: 25\n                  }, this))]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 621,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Form.Text, {\n                  className: \"text-muted\",\n                  children: [\"Server where VOD content will be hosted and served from.\", availableServers.length > 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-success\",\n                    children: [\" \\u2705 \", availableServers.length, \" servers loaded\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 636,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 633,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 609,\n                columnNumber: 19\n              }, this), streamsServer && /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(Form.Group, {\n                  className: \"mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                    children: \"\\uD83D\\uDD17 Source Configuration\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 645,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Form.Check, {\n                    type: \"checkbox\",\n                    label: \"\\u2705 Direct Source (recommended for better performance)\",\n                    checked: sourceConfig.directSource,\n                    onChange: e => setSourceConfig(prev => ({\n                      ...prev,\n                      directSource: e.target.checked\n                    })),\n                    disabled: isImporting\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 646,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Form.Check, {\n                    type: \"checkbox\",\n                    label: \"\\uD83D\\uDD04 Direct Proxy (for geo-restricted content)\",\n                    checked: sourceConfig.directProxy,\n                    onChange: e => setSourceConfig(prev => ({\n                      ...prev,\n                      directProxy: e.target.checked\n                    })),\n                    disabled: isImporting\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 656,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Form.Check, {\n                    type: \"checkbox\",\n                    label: \"\\u2696\\uFE0F Load Balancing (distribute across servers)\",\n                    checked: sourceConfig.loadBalancing,\n                    onChange: e => setSourceConfig(prev => ({\n                      ...prev,\n                      loadBalancing: e.target.checked\n                    })),\n                    disabled: isImporting\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 666,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 644,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n                  className: \"mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                    children: \"\\uD83C\\uDFF7\\uFE0F Categories Assignment\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 679,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(Form.Control, {\n                      type: \"text\",\n                      placeholder: \"Buscar categor\\xEDas por nombre, ID o contenido...\",\n                      value: categorySearch,\n                      onChange: handleCategorySearch,\n                      disabled: isImporting || categoriesLoading,\n                      className: \"mb-2\",\n                      size: \"sm\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 682,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        maxHeight: '200px',\n                        overflowY: 'auto',\n                        border: '1px solid #ddd',\n                        padding: '8px',\n                        borderRadius: '4px'\n                      },\n                      children: categoriesLoading ? /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-center text-muted\",\n                        children: /*#__PURE__*/_jsxDEV(\"small\", {\n                          children: \"Cargando categor\\xEDas...\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 696,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 695,\n                        columnNumber: 29\n                      }, this) : filteredCategories.length > 0 ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"text-muted mb-2\",\n                          children: /*#__PURE__*/_jsxDEV(\"small\", {\n                            children: [\"Mostrando \", filteredCategories.length, \" de \", allCategories.length, \" categor\\xEDas\", categorySearch && ` (filtradas por \"${categorySearch}\")`]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 701,\n                            columnNumber: 33\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 700,\n                          columnNumber: 31\n                        }, this), filteredCategories.slice(0, 100).map(category => /*#__PURE__*/_jsxDEV(Form.Check, {\n                          type: \"radio\",\n                          name: \"categorySelection\",\n                          label: /*#__PURE__*/_jsxDEV(\"div\", {\n                            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                              children: category.name\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 713,\n                              columnNumber: 39\n                            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                              className: \"text-muted\",\n                              children: [\" (ID: \", category.id, \")\"]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 714,\n                              columnNumber: 39\n                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                              style: {\n                                fontSize: '0.85em',\n                                color: '#666',\n                                marginTop: '2px'\n                              },\n                              children: [category.usage_count || 0, \" pel\\xEDculas\", category.category_type && /*#__PURE__*/_jsxDEV(\"span\", {\n                                children: [\" \\u2022 Tipo: \", category.category_type]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 718,\n                                columnNumber: 43\n                              }, this), category.has_real_name && /*#__PURE__*/_jsxDEV(\"span\", {\n                                className: \"text-success\",\n                                children: \" \\u2022 \\u2713 Nombre oficial\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 721,\n                                columnNumber: 43\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 715,\n                              columnNumber: 39\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 712,\n                            columnNumber: 37\n                          }, this),\n                          checked: selectedCategories.includes(category.id),\n                          onChange: e => {\n                            if (e.target.checked) {\n                              setSelectedCategories([category.id]); // Solo una categoría para VOD\n                            }\n                          },\n                          disabled: isImporting,\n                          className: \"mb-2\"\n                        }, category.id, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 707,\n                          columnNumber: 33\n                        }, this)), filteredCategories.length > 100 && /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"text-muted text-center mt-2\",\n                          children: /*#__PURE__*/_jsxDEV(\"small\", {\n                            children: \"Mostrando primeras 100 categor\\xEDas. Use la b\\xFAsqueda para filtrar m\\xE1s.\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 738,\n                            columnNumber: 35\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 737,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true) : /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-muted text-center\",\n                        children: /*#__PURE__*/_jsxDEV(\"small\", {\n                          children: categorySearch ? `No se encontraron categorías que coincidan con \"${categorySearch}\"` : 'No hay categorías disponibles'\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 744,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 743,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 693,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 680,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Form.Text, {\n                    className: \"text-muted\",\n                    children: \"Select existing movie categories or new ones will be created automatically\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 754,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 678,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true), /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"\\u2699\\uFE0F Import Settings\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 762,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Check, {\n                  type: \"checkbox\",\n                  label: \"\\uD83D\\uDD04 Auto-rename with TMDB data\",\n                  defaultChecked: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 763,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Check, {\n                  type: \"checkbox\",\n                  label: \"\\uD83D\\uDCC2 Auto-assign categories\",\n                  defaultChecked: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 768,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Check, {\n                  type: \"checkbox\",\n                  label: \"\\uD83C\\uDFAC Process movie metadata\",\n                  defaultChecked: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 773,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 761,\n                columnNumber: 17\n              }, this), isImporting && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Import Progress\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 782,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(ProgressBar, {\n                  now: importProgress,\n                  label: `${importProgress}%`,\n                  variant: importProgress === 100 ? 'success' : 'danger',\n                  animated: importProgress < 100\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 783,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 781,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"success\",\n                size: \"lg\",\n                onClick: handleImport,\n                disabled: !selectedFile || !fileAnalysis || !streamsServer || isImporting || isProcessingFile,\n                className: \"w-100\",\n                children: isImporting ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"spinner-border spinner-border-sm me-2\",\n                    role: \"status\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 801,\n                    columnNumber: 23\n                  }, this), \"Importando... \", importProgress, \"%\"]\n                }, void 0, true) : !selectedFile ? '📁 Selecciona un archivo M3U' : !fileAnalysis ? '🔍 Analiza el archivo primero' : !streamsServer ? '⚙️ Selecciona un servidor' : '🚀 Iniciar Importación VOD'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 792,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 504,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 503,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 494,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 493,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        lg: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"shadow-sm h-100\",\n          children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n            className: \"bg-info text-white\",\n            children: /*#__PURE__*/_jsxDEV(\"h5\", {\n              className: \"mb-0\",\n              children: \"\\u2139\\uFE0F VOD Import Guidelines\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 822,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 821,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n            children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n              children: \"\\uD83D\\uDCCB Supported VOD Content:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 825,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"\\uD83C\\uDFAC Movies:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 827,\n                  columnNumber: 21\n                }, this), \" Feature films and documentaries\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 827,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"\\uD83C\\uDFAD Short Films:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 828,\n                  columnNumber: 21\n                }, this), \" Independent and festival content\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 828,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"\\uD83D\\uDCFA Specials:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 829,\n                  columnNumber: 21\n                }, this), \" TV movies and special events\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 829,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"\\uD83C\\uDFAA Stand-up:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 830,\n                  columnNumber: 21\n                }, this), \" Comedy specials and performances\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 830,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 826,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h6\", {\n              children: \"\\u2699\\uFE0F Processing Features:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 833,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"\\uD83C\\uDFAF TMDB Integration:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 835,\n                  columnNumber: 21\n                }, this), \" Auto-fetch movie metadata\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 835,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"\\uD83C\\uDFF7\\uFE0F Category Assignment:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 836,\n                  columnNumber: 21\n                }, this), \" Smart movie categorization\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 836,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"\\uD83D\\uDDBC\\uFE0F Poster Download:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 837,\n                  columnNumber: 21\n                }, this), \" High-quality movie artwork\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 837,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"\\uD83D\\uDCDD Description Parsing:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 838,\n                  columnNumber: 21\n                }, this), \" Extract movie info\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 838,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 834,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h6\", {\n              children: \"\\u26A1 Performance Tips:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 841,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Files under 10MB import faster\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 843,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Use UTF-8 encoding for special characters\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 844,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Clean duplicate entries before import\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 845,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Ensure stable internet for TMDB metadata\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 846,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 842,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 824,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 820,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 819,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 492,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      children: /*#__PURE__*/_jsxDEV(Col, {\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"shadow-sm\",\n          children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n            className: \"bg-secondary text-white\",\n            children: /*#__PURE__*/_jsxDEV(\"h5\", {\n              className: \"mb-0\",\n              children: \"\\uD83D\\uDCCA Recent VOD Import Queue\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 857,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 856,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n            children: /*#__PURE__*/_jsxDEV(Table, {\n              striped: true,\n              hover: true,\n              children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"\\uD83D\\uDCC4 File\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 863,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"\\uD83D\\uDCC5 Queued\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 864,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"\\uD83D\\uDCCA Status\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 865,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"\\uD83C\\uDFAF Target\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 866,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"\\u26A1 Actions\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 867,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 862,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 861,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                children: [/*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"movies_collection.m3u\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 872,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 872,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: \"2025-07-15 16:30\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 873,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"badge bg-warning\",\n                      children: \"\\u23F3 Queued\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 874,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 874,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: \"Main Server\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 875,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: [/*#__PURE__*/_jsxDEV(Button, {\n                      size: \"sm\",\n                      variant: \"outline-danger\",\n                      className: \"me-1\",\n                      children: \"\\u25B6\\uFE0F\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 877,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Button, {\n                      size: \"sm\",\n                      variant: \"outline-danger\",\n                      children: \"\\u274C\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 878,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 876,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 871,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"action_movies.m3u\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 882,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 882,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: \"2025-07-15 16:25\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 883,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"badge bg-success\",\n                      children: \"\\u2705 Processing\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 884,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 884,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: \"Cloud Server\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 885,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(Button, {\n                      size: \"sm\",\n                      variant: \"outline-info\",\n                      children: \"\\uD83D\\uDCCA\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 887,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 886,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 881,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 870,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 860,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 859,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 855,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 854,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 853,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 471,\n    columnNumber: 5\n  }, this);\n};\n_s(ImportVODM3U, \"G0f4ODwpXIZZYsKSP7ALqrFb0eg=\");\n_c = ImportVODM3U;\nexport default ImportVODM3U;\nvar _c;\n$RefreshReg$(_c, \"ImportVODM3U\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Row", "Col", "Card", "<PERSON><PERSON>", "Form", "<PERSON><PERSON>", "ProgressBar", "Table", "Badge", "BackendStatus", "checkSystemHealth", "api", "databaseAPI", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ImportVODM3U", "_s", "_fileAnalysis$basic_a", "_fileAnalysis$basic_a2", "_fileAnalysis$basic_a3", "_fileAnalysis$basic_a4", "_fileAnalysis$basic_a5", "_fileAnalysis$file_in", "_fileAnalysis$parse_r", "_fileAnalysis$parse_r2", "_fileAnalysis$parse_r3", "selectedFile", "setSelectedFile", "isImporting", "setIsImporting", "importProgress", "setImportProgress", "show<PERSON><PERSON><PERSON>", "setShowAlert", "alertMessage", "setAlertMessage", "alertType", "setAlertType", "backendStatus", "setBackendStatus", "fileAnalysis", "setFileAnalysis", "isProcessingFile", "setIsProcessingFile", "contentType", "streamsServer", "setStreamsServer", "sourceConfig", "setSourceConfig", "directSource", "directProxy", "loadBalancing", "selectedCategories", "setSelectedCategories", "availableServers", "setAvailableServers", "checkBackendStatus", "health", "success", "displayAlert", "error", "loadInitialData", "console", "log", "loadRealServers", "loadRealCategories", "loadMockData", "window", "debugLog", "message", "response", "fetch", "status", "statusText", "result", "json", "data", "servers", "map", "server", "id", "server_id", "name", "server_name", "ip", "server_ip", "load", "total_streams", "server_status", "length", "Error", "categorySearch", "setCategorySearch", "allCategories", "setAllCategories", "filteredCategories", "setFilteredCategories", "categoriesLoading", "setCategoriesLoading", "getCategories", "categories", "cat", "usage_count", "category_type", "has_real_name", "category_id_raw", "setExistingCategories", "loadMockCategories", "filterCategories", "searchTerm", "trim", "searchLower", "toLowerCase", "filtered", "filter", "includes", "toString", "handleCategorySearch", "e", "value", "target", "mockCategories", "detectCategoryType", "categoryName", "type", "showAlertMessage", "setTimeout", "handleBrowseFiles", "fileInput", "document", "createElement", "accept", "onchange", "file", "files", "handleFileSelect", "click", "handleAnalyzeFile", "m3uAPI", "analyzeFile", "_response$data$basic_", "basic_analysis", "estimated_entries", "handleClearAnalysis", "event", "size", "toFixed", "handleImport", "importConfig", "tmdbEnabled", "autoAssignCategories", "formData", "FormData", "append", "JSON", "stringify", "analyzeResponse", "fileContent", "Promise", "resolve", "reject", "reader", "FileReader", "onload", "onerror", "readAsText", "parseResponse", "method", "headers", "body", "m3uContent", "parseResult", "importPayload", "movies", "parseInt", "category_id", "tmdb_search", "importResponse", "importResult", "stats", "successMessage", "imported", "errors", "movies_created", "metadata_enriched", "style", "width", "max<PERSON><PERSON><PERSON>", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onRetry", "variant", "dismissible", "onClose", "Heading", "lg", "Header", "bg", "Body", "Group", "Label", "Control", "onChange", "disabled", "Text", "role", "md", "total_lines", "extinf_lines", "url_lines", "has_valid_m3u_header", "file_info", "size_mb", "parse_results", "onClick", "Select", "Check", "label", "checked", "prev", "placeholder", "maxHeight", "overflowY", "border", "padding", "borderRadius", "slice", "category", "fontSize", "color", "marginTop", "defaultChecked", "now", "animated", "striped", "hover", "_c", "$RefreshReg$"], "sources": ["F:/WORKSPACE/XUI IMPORTER/xui-importer/src/components/ImportVODM3U.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Row, Col, Card, Button, Form, Alert, ProgressBar, Table, Badge } from 'react-bootstrap';\nimport BackendStatus from './BackendStatus';\n\nimport {\n  checkSystemHealth\n} from '../utils/seriesLogic';\nimport { api, databaseAPI } from '../services/apiService';\n\nconst ImportVODM3U = () => {\n  const [selectedFile, setSelectedFile] = useState(null);\n  const [isImporting, setIsImporting] = useState(false);\n  const [importProgress, setImportProgress] = useState(0);\n  const [showAlert, setShowAlert] = useState(false);\n  const [alertMessage, setAlertMessage] = useState('');\n  const [alertType, setAlertType] = useState('info');\n  \n  // Estados para backend\n  const [backendStatus, setBackendStatus] = useState('checking');\n  const [fileAnalysis, setFileAnalysis] = useState(null);\n  const [isProcessingFile, setIsProcessingFile] = useState(false);\n  \n  // Configuración fija para VOD/Movies\n  const contentType = 'movie';\n  const [streamsServer, setStreamsServer] = useState('');\n  const [sourceConfig, setSourceConfig] = useState({\n    directSource: true,\n    directProxy: false,\n    loadBalancing: false\n  });\n  const [selectedCategories, setSelectedCategories] = useState([]);\n\n  // Estados para datos dinámicos del backend\n  const [availableServers, setAvailableServers] = useState([]);\n  // const [existingCategories, setExistingCategories] = useState([]);\n\n  const checkBackendStatus = async () => {\n    try {\n      const health = await checkSystemHealth();\n      setBackendStatus(health.success ? 'connected' : 'error');\n      \n      if (!health.success) {\n        displayAlert('warning', 'Backend no disponible. Funcionando en modo offline.');\n      }\n    } catch (error) {\n      setBackendStatus('error');\n      displayAlert('danger', 'No se puede conectar al backend');\n    }\n  };\n\n  const loadInitialData = async () => {\n    try {\n      console.log(`🔄 Cargando datos iniciales. Backend status: ${backendStatus}`);\n\n      // Cargar servidores y categorías desde backend si está disponible\n      if (backendStatus === 'connected') {\n        console.log('✅ Backend conectado, cargando datos reales...');\n        await loadRealServers();\n        await loadRealCategories();\n      } else {\n        console.log('⚠️ Backend no conectado, usando datos mock...');\n        // Fallback a mock data si no hay conexión\n        loadMockData();\n      }\n\n    } catch (error) {\n      console.error('Error cargando datos iniciales:', error);\n      if (window.debugLog) {\n        window.debugLog('error', `Error cargando datos iniciales: ${error.message}`);\n      }\n      // Fallback a mock data en caso de error\n      loadMockData();\n    }\n  };\n\n  // Cargar servidores reales desde la base de datos\n  const loadRealServers = async () => {\n    try {\n      console.log('🔄 Iniciando carga de servidores reales...');\n\n      const response = await fetch('http://localhost:5001/api/database/streaming-servers');\n      console.log('📡 Respuesta del servidor:', response.status, response.statusText);\n\n      const result = await response.json();\n      console.log('📊 Datos recibidos:', result);\n\n      if (result.success && result.data) {\n        const servers = result.data.map(server => ({\n          id: server.server_id,\n          name: server.server_name || `Server ${server.server_id}`,\n          ip: server.server_ip || 'Unknown IP',\n          load: `${server.total_streams || 0} streams`, // Mostrar cantidad de streams como \"carga\"\n          total_streams: server.total_streams || 0,\n          status: server.server_status === 1 ? 'Active' : 'Inactive'\n        }));\n\n        console.log('🖥️ Servidores mapeados:', servers);\n        setAvailableServers(servers);\n        console.log('✅ Estado actualizado con', servers.length, 'servidores');\n\n        if (window.debugLog) {\n          window.debugLog('success', `✅ Cargados ${servers.length} servidores reales desde BD`);\n        }\n      } else {\n        console.error('❌ Respuesta no exitosa:', result);\n        throw new Error(result.error || 'No se pudieron cargar servidores');\n      }\n    } catch (error) {\n      console.error('❌ Error cargando servidores:', error);\n      if (window.debugLog) {\n        window.debugLog('error', `❌ Error cargando servidores: ${error.message}`);\n      }\n      throw error;\n    }\n  };\n\n  // Estados para manejo de categorías\n  const [categorySearch, setCategorySearch] = useState('');\n  const [allCategories, setAllCategories] = useState([]);\n  const [filteredCategories, setFilteredCategories] = useState([]);\n  const [categoriesLoading, setCategoriesLoading] = useState(false);\n\n  // Cargar TODAS las categorías desde la base de datos con nombres reales\n  const loadRealCategories = async () => {\n    try {\n      setCategoriesLoading(true);\n      console.log('🔄 Cargando TODAS las categorías con nombres reales...');\n      const result = await databaseAPI.getCategories();\n\n      if (result.success && result.categories) {\n        const categories = result.categories.map(cat => ({\n          id: cat.id,\n          name: cat.name,\n          usage_count: cat.usage_count,\n          category_type: cat.category_type,\n          has_real_name: cat.has_real_name,\n          category_id_raw: `[${cat.id}]` // Formato JSON array\n        }));\n\n        setAllCategories(categories);\n        setFilteredCategories(categories);\n        setExistingCategories(categories);\n        console.log('✅ Categorías cargadas:', categories.length);\n\n        if (window.debugLog) {\n          window.debugLog('success', `✅ Cargadas ${categories.length} categorías reales desde BD`);\n        }\n      } else {\n        throw new Error('No se pudieron cargar categorías');\n      }\n    } catch (error) {\n      console.error('Error cargando categorías:', error);\n      if (window.debugLog) {\n        window.debugLog('error', `❌ Error cargando categorías: ${error.message}`);\n      }\n      // Fallback a categorías mock\n      loadMockCategories();\n    } finally {\n      setCategoriesLoading(false);\n    }\n  };\n\n  // Filtrar categorías basándose en la búsqueda\n  const filterCategories = (searchTerm) => {\n    if (!searchTerm.trim()) {\n      setFilteredCategories(allCategories);\n      return;\n    }\n\n    const searchLower = searchTerm.toLowerCase();\n    const filtered = allCategories.filter(cat =>\n      cat.name.toLowerCase().includes(searchLower) ||\n      cat.id.toString().includes(searchTerm) ||\n      (cat.category_type && cat.category_type.toLowerCase().includes(searchLower))\n    );\n\n    setFilteredCategories(filtered);\n  };\n\n  // Manejar cambio en búsqueda de categorías\n  const handleCategorySearch = (e) => {\n    const value = e.target.value;\n    setCategorySearch(value);\n    filterCategories(value);\n  };\n\n  // Cargar categorías mock como fallback\n  const loadMockCategories = () => {\n    const mockCategories = [\n      { id: 1964, name: 'Películas Generales', usage_count: 2279 },\n      { id: 1763, name: 'Acción', usage_count: 2242 },\n      { id: 1762, name: 'Drama', usage_count: 2142 },\n      { id: 1767, name: 'Comedia', usage_count: 1333 },\n      { id: 1855, name: 'Terror/Horror', usage_count: 1200 }\n    ];\n    setAllCategories(mockCategories);\n    setFilteredCategories(mockCategories);\n    setExistingCategories(mockCategories);\n    console.log('⚠️ Usando categorías mock:', mockCategories);\n  };\n\n  // Detectar tipo de categoría basado en el nombre\n  const detectCategoryType = (categoryName) => {\n    const name = categoryName.toLowerCase();\n\n    if (name.includes('movie') || name.includes('film') || name.includes('cinema')) {\n      return 'movie';\n    } else if (name.includes('series') || name.includes('show') || name.includes('drama')) {\n      return 'series';\n    } else if (name.includes('live') || name.includes('tv') || name.includes('channel') || name.includes('news') || name.includes('sport')) {\n      return 'live';\n    } else if (name.includes('radio') || name.includes('music') || name.includes('fm')) {\n      return 'radio';\n    }\n\n    return 'movie'; // Default a movie para VOD si no se puede detectar\n  };\n\n  // Datos mock como fallback\n  const loadMockData = () => {\n    setAvailableServers([\n      { id: 1, name: 'Main Server US', ip: '*************', load: '45%' },\n      { id: 2, name: 'EU Server', ip: '*************', load: '32%' },\n      { id: 3, name: 'Asia Server', ip: '*************', load: '67%' },\n      { id: 4, name: 'Backup Server', ip: '*************', load: '12%' }\n    ]);\n\n    setExistingCategories([\n      { id: 1, name: 'Action Movies', type: 'movie' },\n      { id: 2, name: 'Comedy Movies', type: 'movie' },\n      { id: 3, name: 'Drama Movies', type: 'movie' },\n      { id: 4, name: 'Horror Movies', type: 'movie' },\n      { id: 5, name: 'Sci-Fi Movies', type: 'movie' },\n      { id: 6, name: 'Documentary', type: 'movie' },\n      { id: 7, name: 'Animation', type: 'movie' },\n      { id: 8, name: 'Thriller', type: 'movie' },\n      { id: 9, name: 'Romance', type: 'movie' }\n    ]);\n\n    if (window.debugLog) {\n      window.debugLog('warning', '⚠️ Usando datos mock - backend no disponible');\n    }\n  };\n\n  const showAlertMessage = (message, type = 'info') => {\n    setAlertMessage(message);\n    setAlertType(type);\n    setShowAlert(true);\n    setTimeout(() => setShowAlert(false), 5000);\n  };\n\n  // Helper function to show alert with better formatting\n  const displayAlert = (type, message) => {\n    showAlertMessage(message, type);\n  };\n\n  // File handling functions\n  const handleBrowseFiles = () => {\n    const fileInput = document.createElement('input');\n    fileInput.type = 'file';\n    fileInput.accept = '.m3u,.m3u8';\n    fileInput.onchange = (e) => {\n      const file = e.target.files[0];\n      if (file) {\n        handleFileSelect({ target: { files: [file] } });\n      }\n    };\n    fileInput.click();\n  };\n\n  const handleAnalyzeFile = async () => {\n    if (!selectedFile) return;\n    \n    setIsProcessingFile(true);\n    setFileAnalysis(null);\n    \n    try {\n      displayAlert('info', 'Analizando archivo M3U...');\n      \n      const response = await api.m3uAPI.analyzeFile(selectedFile);\n      \n      if (response.success) {\n        setFileAnalysis(response.data);\n\n        // Análisis completado - configurado para VOD/Movies\n        displayAlert('success', `✅ Archivo analizado correctamente. Se detectaron ${response.data.basic_analysis?.estimated_entries || 0} entradas. Configurado para importar como películas/VOD.`);\n      } else {\n        throw new Error(response.error || 'Error analizando archivo');\n      }\n    } catch (error) {\n      console.error('Error analyzing file:', error);\n      displayAlert('danger', `❌ Error analizando archivo: ${error.message}`);\n    } finally {\n      setIsProcessingFile(false);\n    }\n  };\n\n  const handleClearAnalysis = () => {\n    setFileAnalysis(null);\n    setSelectedFile(null);\n    // Content type fijo para VOD\n    displayAlert('info', 'Análisis limpiado. Selecciona un nuevo archivo.');\n  };\n\n  // Verificar estado del backend al cargar\n  useEffect(() => {\n    checkBackendStatus();\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n\n  // Cargar datos cuando el backend status cambie\n  useEffect(() => {\n    if (backendStatus !== 'checking') {\n      loadInitialData();\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [backendStatus]);\n\n  const handleFileSelect = async (event) => {\n    const file = event.target.files[0];\n    setSelectedFile(file);\n    setFileAnalysis(null);\n\n    if (file) {\n      displayAlert('info', `Archivo seleccionado: ${file.name} (${(file.size / 1024 / 1024).toFixed(2)} MB)`);\n\n      // Solo mostrar información básica, el análisis se hace manualmente\n      if (window.debugLog) {\n        window.debugLog(`📁 File selected: ${file.name}`, 'info');\n        window.debugLog(`📊 File size: ${(file.size / 1024 / 1024).toFixed(2)} MB`, 'info');\n      }\n    }\n  };\n\n  const handleImport = async () => {\n    if (!selectedFile || !streamsServer) {\n      displayAlert('warning', '⚠️ Por favor completa todos los campos requeridos (archivo y servidor).');\n      return;\n    }\n\n    if (!fileAnalysis) {\n      displayAlert('warning', '⚠️ Por favor analiza el archivo antes de importar.');\n      return;\n    }\n\n    if (window.debugLog) {\n      window.debugLog(`📥 Starting VOD import of ${selectedFile.name}`, 'info');\n      window.debugLog(`📊 File size: ${(selectedFile.size / 1024 / 1024).toFixed(2)} MB`, 'info');\n      window.debugLog(`🎯 Content type: ${contentType}`, 'info');\n      window.debugLog(`🖥️ Target server: ${streamsServer}`, 'info');\n    }\n\n    setIsImporting(true);\n    setImportProgress(0);\n\n    try {\n      // Preparar configuración de importación para VOD\n      const importConfig = {\n        contentType,\n        streamsServer,\n        sourceConfig,\n        categories: selectedCategories,\n        tmdbEnabled: true,\n        autoAssignCategories: true\n      };\n\n      displayAlert('info', '🔍 Iniciando proceso de importación VOD...');\n      setImportProgress(10);\n\n      // Paso 1: Subir archivo\n      const formData = new FormData();\n      formData.append('file', selectedFile);\n      formData.append('config', JSON.stringify(importConfig));\n\n      if (window.debugLog) {\n        window.debugLog('📤 Uploading VOD file to backend...', 'info');\n      }\n\n      // Paso 1: Analizar archivo M3U\n      const analyzeResponse = await api.m3uAPI.analyzeFile(selectedFile);\n      setImportProgress(30);\n\n      if (!analyzeResponse.success) {\n        throw new Error(analyzeResponse.error || 'Error analyzing file');\n      }\n\n      displayAlert('info', '🎯 Archivo subido, procesando contenido VOD...');\n      // Paso 2: Leer contenido del archivo para parsear películas\n      const fileContent = await new Promise((resolve, reject) => {\n        const reader = new FileReader();\n        reader.onload = (e) => resolve(e.target.result);\n        reader.onerror = () => reject(new Error('Error reading file'));\n        reader.readAsText(selectedFile);\n      });\n\n      setImportProgress(40);\n\n      // Paso 3: Parsear contenido del M3U como películas/VOD\n      const parseResponse = await fetch('http://localhost:5001/api/import/parse-movies', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({ m3uContent: fileContent })\n      });\n\n      const parseResult = await parseResponse.json();\n      if (!parseResult.success) {\n        throw new Error(parseResult.error || 'Error parsing VOD content');\n      }\n\n      setImportProgress(60);\n\n      // Paso 4: Importar películas a la base de datos (tabla streams con type=2)\n      const importPayload = {\n        movies: parseResult.data.movies,\n        server_id: parseInt(streamsServer),\n        category_id: selectedCategories.length > 0 ? parseInt(selectedCategories[0]) : null,\n        tmdb_search: true // Usar integración TMDB existente\n      };\n\n      const importResponse = await fetch('http://localhost:5001/api/import/movies', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify(importPayload)\n      });\n\n      const importResult = await importResponse.json();\n\n      // Debug: Log de la respuesta completa\n      console.log('🔍 VOD Import Response:', importResult);\n\n      if (!importResult.success) {\n        throw new Error(importResult.error || 'Error importing VOD content');\n      }\n\n      // Paso 5: Finalizar importación\n      setImportProgress(100);\n\n      // Mostrar estadísticas de películas (acceso seguro)\n      const stats = importResult.data || importResult;\n      const successMessage = `✅ Importación VOD completada exitosamente!\\n📊 Estadísticas:\\n• ${stats.imported || 0} elementos importados\\n• ${stats.errors || 0} errores\\n• ${stats.movies_created || 0} películas creadas\\n• ${stats.metadata_enriched || 0} con metadata TMDB`;\n\n      displayAlert('success', successMessage);\n\n      if (window.debugLog) {\n        window.debugLog(`✅ VOD Import completed successfully: ${selectedFile.name}`, 'success');\n        window.debugLog(`📊 Stats: ${JSON.stringify(importResult)}`, 'info');\n      }\n\n      // Limpiar estado después de importación exitosa\n      setTimeout(() => {\n        setSelectedFile(null);\n        setFileAnalysis(null);\n        // Content type fijo para VOD\n        setStreamsServer('');\n        setSelectedCategories([]);\n      }, 3000);\n\n    } catch (error) {\n      console.error('VOD Import error:', error);\n      displayAlert('danger', `❌ Error durante la importación VOD: ${error.message}`);\n\n      if (window.debugLog) {\n        window.debugLog(`❌ VOD Import failed: ${error.message}`, 'error');\n      }\n    } finally {\n      setIsImporting(false);\n    }\n  };\n\n  return (\n    <div style={{ width: '100%', maxWidth: 'none' }}>\n      <div className=\"d-flex justify-content-between align-items-center mb-4\">\n        <h1 className=\"text-danger\">🎬 Import VOD M3U Files</h1>\n        <BackendStatus\n          status={backendStatus}\n          onRetry={checkBackendStatus}\n        />\n      </div>\n\n      {showAlert && (\n        <Alert variant={alertType} dismissible onClose={() => setShowAlert(false)}>\n          <Alert.Heading>\n            {alertType === 'success' && '✅ Success!'}\n            {alertType === 'danger' && '❌ Error!'}\n            {alertType === 'warning' && '⚠️ Warning!'}\n            {alertType === 'info' && 'ℹ️ Information'}\n          </Alert.Heading>\n          <p>{alertMessage}</p>\n        </Alert>\n      )}\n\n      <Row className=\"mb-4\">\n        <Col lg={6}>\n          <Card className=\"shadow-sm h-100\">\n            <Card.Header className=\"bg-danger text-white d-flex justify-content-between align-items-center\">\n              <h5 className=\"mb-0\">📂 File Upload</h5>\n              {backendStatus === 'connected' && (\n                <Badge bg=\"success\">\n                  <i className=\"bi bi-cloud-check\"></i> Backend Ready\n                </Badge>\n              )}\n            </Card.Header>\n            <Card.Body>\n              <Form>\n                <Form.Group className=\"mb-3\">\n                  <Form.Label>Select M3U File</Form.Label>\n                  <Form.Control\n                    type=\"file\"\n                    accept=\".m3u,.m3u8\"\n                    onChange={handleFileSelect}\n                    disabled={isImporting}\n                  />\n                  <Form.Text className=\"text-muted\">\n                    Supported formats: .m3u, .m3u8 (Max size: 50MB)\n                  </Form.Text>\n                </Form.Group>\n\n                {selectedFile && (\n                  <>\n                    <Alert variant=\"info\">\n                      <strong>Selected File:</strong> {selectedFile.name}<br/>\n                      <strong>Size:</strong> {(selectedFile.size / 1024 / 1024).toFixed(2)} MB\n                    </Alert>\n\n                    {isProcessingFile && (\n                      <Alert variant=\"secondary\">\n                        <div className=\"d-flex align-items-center\">\n                          <div className=\"spinner-border spinner-border-sm me-2\" role=\"status\"></div>\n                          Analizando archivo...\n                        </div>\n                      </Alert>\n                    )}\n\n                    {fileAnalysis && !isProcessingFile && (\n                      <Alert variant=\"success\">\n                        <h6>📊 Análisis del Archivo</h6>\n                        <Row>\n                          <Col md={6}>\n                            <strong>Total Lines:</strong> {fileAnalysis.basic_analysis?.total_lines || 0}<br/>\n                            <strong>EXTINF Entries:</strong> {fileAnalysis.basic_analysis?.extinf_lines || 0}<br/>\n                            <strong>URL Entries:</strong> {fileAnalysis.basic_analysis?.url_lines || 0}\n                          </Col>\n                          <Col md={6}>\n                            <strong>Estimated Entries:</strong> {fileAnalysis.basic_analysis?.estimated_entries || 0}<br/>\n                            <strong>Valid M3U:</strong> {fileAnalysis.basic_analysis?.has_valid_m3u_header ? '✅ Yes' : '❌ No'}<br/>\n                            <strong>File Size:</strong> {fileAnalysis.file_info?.size_mb || 0} MB\n                          </Col>\n                        </Row>\n\n                        {fileAnalysis.parse_results?.movies?.success && (\n                          <div className=\"mt-2\">\n                            <strong>Movies Detected:</strong> {fileAnalysis.parse_results.movies.data?.length || 0}\n                          </div>\n                        )}\n                      </Alert>\n                    )}\n                  </>\n                )}\n\n                <div className=\"mb-3\">\n                  <Button\n                    onClick={handleBrowseFiles}\n                    variant=\"danger\"\n                    disabled={isProcessingFile}\n                  >\n                    Seleccionar Archivo M3U\n                  </Button>\n\n                  {selectedFile && !fileAnalysis && (\n                    <Button\n                      onClick={handleAnalyzeFile}\n                      variant=\"info\"\n                      className=\"ms-2\"\n                      disabled={isProcessingFile}\n                    >\n                      {isProcessingFile ? (\n                        <>\n                          <span className=\"spinner-border spinner-border-sm me-2\" role=\"status\"></span>\n                          Analizando...\n                        </>\n                      ) : (\n                        'Analizar Archivo'\n                      )}\n                    </Button>\n                  )}\n\n                  {fileAnalysis && (\n                    <Button\n                      onClick={handleClearAnalysis}\n                      variant=\"outline-secondary\"\n                      className=\"ms-2\"\n                      disabled={isProcessingFile}\n                    >\n                      Limpiar Análisis\n                    </Button>\n                  )}\n                </div>\n\n                {/* Content type fijo para VOD */}\n                <div className=\"mb-3\">\n                  <div className=\"alert alert-info\">\n                    <strong>🎯 Tipo de Contenido:</strong> 🎬 Películas/VOD (fijo)\n                    <br />\n                    <small>Este importador está configurado específicamente para películas y contenido VOD.</small>\n                  </div>\n                </div>\n\n                {true && (\n                  <Form.Group className=\"mb-3\">\n                    <div className=\"d-flex justify-content-between align-items-center mb-2\">\n                      <Form.Label className=\"mb-0\">🖥️ Target Streams Server</Form.Label>\n                      <Button\n                        variant=\"outline-secondary\"\n                        size=\"sm\"\n                        onClick={loadRealServers}\n                        disabled={isImporting || backendStatus !== 'connected'}\n                      >\n                        🔄 Refresh\n                      </Button>\n                    </div>\n                    <Form.Select\n                      value={streamsServer}\n                      onChange={(e) => setStreamsServer(e.target.value)}\n                      disabled={isImporting}\n                    >\n                      <option value=\"\">Select streams server...</option>\n                      {availableServers.map(server => (\n                        <option key={server.id} value={server.id}>\n                          {server.name} ({server.ip}) - {server.load}\n                        </option>\n                      ))}\n                    </Form.Select>\n                    <Form.Text className=\"text-muted\">\n                      Server where VOD content will be hosted and served from.\n                      {availableServers.length > 0 && (\n                        <span className=\"text-success\"> ✅ {availableServers.length} servers loaded</span>\n                      )}\n                    </Form.Text>\n                  </Form.Group>\n                )}\n\n                {streamsServer && (\n                  <>\n                    <Form.Group className=\"mb-3\">\n                      <Form.Label>🔗 Source Configuration</Form.Label>\n                      <Form.Check\n                        type=\"checkbox\"\n                        label=\"✅ Direct Source (recommended for better performance)\"\n                        checked={sourceConfig.directSource}\n                        onChange={(e) => setSourceConfig(prev => ({\n                          ...prev,\n                          directSource: e.target.checked\n                        }))}\n                        disabled={isImporting}\n                      />\n                      <Form.Check\n                        type=\"checkbox\"\n                        label=\"🔄 Direct Proxy (for geo-restricted content)\"\n                        checked={sourceConfig.directProxy}\n                        onChange={(e) => setSourceConfig(prev => ({\n                          ...prev,\n                          directProxy: e.target.checked\n                        }))}\n                        disabled={isImporting}\n                      />\n                      <Form.Check\n                        type=\"checkbox\"\n                        label=\"⚖️ Load Balancing (distribute across servers)\"\n                        checked={sourceConfig.loadBalancing}\n                        onChange={(e) => setSourceConfig(prev => ({\n                          ...prev,\n                          loadBalancing: e.target.checked\n                        }))}\n                        disabled={isImporting}\n                      />\n                    </Form.Group>\n\n                    <Form.Group className=\"mb-3\">\n                      <Form.Label>🏷️ Categories Assignment</Form.Label>\n                      <div>\n                        {/* Buscador de categorías */}\n                        <Form.Control\n                          type=\"text\"\n                          placeholder=\"Buscar categorías por nombre, ID o contenido...\"\n                          value={categorySearch}\n                          onChange={handleCategorySearch}\n                          disabled={isImporting || categoriesLoading}\n                          className=\"mb-2\"\n                          size=\"sm\"\n                        />\n\n                        {/* Lista de categorías */}\n                        <div style={{maxHeight: '200px', overflowY: 'auto', border: '1px solid #ddd', padding: '8px', borderRadius: '4px'}}>\n                          {categoriesLoading ? (\n                            <div className=\"text-center text-muted\">\n                              <small>Cargando categorías...</small>\n                            </div>\n                          ) : filteredCategories.length > 0 ? (\n                            <>\n                              <div className=\"text-muted mb-2\">\n                                <small>\n                                  Mostrando {filteredCategories.length} de {allCategories.length} categorías\n                                  {categorySearch && ` (filtradas por \"${categorySearch}\")`}\n                                </small>\n                              </div>\n                              {filteredCategories.slice(0, 100).map(category => (\n                                <Form.Check\n                                  key={category.id}\n                                  type=\"radio\"\n                                  name=\"categorySelection\"\n                                  label={\n                                    <div>\n                                      <strong>{category.name}</strong>\n                                      <span className=\"text-muted\"> (ID: {category.id})</span>\n                                      <div style={{fontSize: '0.85em', color: '#666', marginTop: '2px'}}>\n                                        {category.usage_count || 0} películas\n                                        {category.category_type && (\n                                          <span> • Tipo: {category.category_type}</span>\n                                        )}\n                                        {category.has_real_name && (\n                                          <span className=\"text-success\"> • ✓ Nombre oficial</span>\n                                        )}\n                                      </div>\n                                    </div>\n                                  }\n                                  checked={selectedCategories.includes(category.id)}\n                                  onChange={(e) => {\n                                    if (e.target.checked) {\n                                      setSelectedCategories([category.id]); // Solo una categoría para VOD\n                                    }\n                                  }}\n                                  disabled={isImporting}\n                                  className=\"mb-2\"\n                                />\n                              ))}\n                              {filteredCategories.length > 100 && (\n                                <div className=\"text-muted text-center mt-2\">\n                                  <small>Mostrando primeras 100 categorías. Use la búsqueda para filtrar más.</small>\n                                </div>\n                              )}\n                            </>\n                          ) : (\n                            <div className=\"text-muted text-center\">\n                              <small>\n                                {categorySearch ?\n                                  `No se encontraron categorías que coincidan con \"${categorySearch}\"` :\n                                  'No hay categorías disponibles'\n                                }\n                              </small>\n                            </div>\n                          )}\n                        </div>\n                      </div>\n                      <Form.Text className=\"text-muted\">\n                        Select existing movie categories or new ones will be created automatically\n                      </Form.Text>\n                    </Form.Group>\n                  </>\n                )}\n\n                <Form.Group className=\"mb-3\">\n                  <Form.Label>⚙️ Import Settings</Form.Label>\n                  <Form.Check\n                    type=\"checkbox\"\n                    label=\"🔄 Auto-rename with TMDB data\"\n                    defaultChecked\n                  />\n                  <Form.Check\n                    type=\"checkbox\"\n                    label=\"📂 Auto-assign categories\"\n                    defaultChecked\n                  />\n                  <Form.Check\n                    type=\"checkbox\"\n                    label=\"🎬 Process movie metadata\"\n                    defaultChecked\n                  />\n                </Form.Group>\n\n                {isImporting && (\n                  <div className=\"mb-3\">\n                    <Form.Label>Import Progress</Form.Label>\n                    <ProgressBar\n                      now={importProgress}\n                      label={`${importProgress}%`}\n                      variant={importProgress === 100 ? 'success' : 'danger'}\n                      animated={importProgress < 100}\n                    />\n                  </div>\n                )}\n\n                <Button\n                  variant=\"success\"\n                  size=\"lg\"\n                  onClick={handleImport}\n                  disabled={!selectedFile || !fileAnalysis || !streamsServer || isImporting || isProcessingFile}\n                  className=\"w-100\"\n                >\n                  {isImporting ? (\n                    <>\n                      <span className=\"spinner-border spinner-border-sm me-2\" role=\"status\"></span>\n                      Importando... {importProgress}%\n                    </>\n                  ) : !selectedFile ? (\n                    '📁 Selecciona un archivo M3U'\n                  ) : !fileAnalysis ? (\n                    '🔍 Analiza el archivo primero'\n                  ) : !streamsServer ? (\n                    '⚙️ Selecciona un servidor'\n                  ) : (\n                    '🚀 Iniciar Importación VOD'\n                  )}\n                </Button>\n              </Form>\n            </Card.Body>\n          </Card>\n        </Col>\n\n        <Col lg={6}>\n          <Card className=\"shadow-sm h-100\">\n            <Card.Header className=\"bg-info text-white\">\n              <h5 className=\"mb-0\">ℹ️ VOD Import Guidelines</h5>\n            </Card.Header>\n            <Card.Body>\n              <h6>📋 Supported VOD Content:</h6>\n              <ul>\n                <li><strong>🎬 Movies:</strong> Feature films and documentaries</li>\n                <li><strong>🎭 Short Films:</strong> Independent and festival content</li>\n                <li><strong>📺 Specials:</strong> TV movies and special events</li>\n                <li><strong>🎪 Stand-up:</strong> Comedy specials and performances</li>\n              </ul>\n\n              <h6>⚙️ Processing Features:</h6>\n              <ul>\n                <li><strong>🎯 TMDB Integration:</strong> Auto-fetch movie metadata</li>\n                <li><strong>🏷️ Category Assignment:</strong> Smart movie categorization</li>\n                <li><strong>🖼️ Poster Download:</strong> High-quality movie artwork</li>\n                <li><strong>📝 Description Parsing:</strong> Extract movie info</li>\n              </ul>\n\n              <h6>⚡ Performance Tips:</h6>\n              <ul>\n                <li>Files under 10MB import faster</li>\n                <li>Use UTF-8 encoding for special characters</li>\n                <li>Clean duplicate entries before import</li>\n                <li>Ensure stable internet for TMDB metadata</li>\n              </ul>\n            </Card.Body>\n          </Card>\n        </Col>\n      </Row>\n\n      <Row>\n        <Col>\n          <Card className=\"shadow-sm\">\n            <Card.Header className=\"bg-secondary text-white\">\n              <h5 className=\"mb-0\">📊 Recent VOD Import Queue</h5>\n            </Card.Header>\n            <Card.Body>\n              <Table striped hover>\n                <thead>\n                  <tr>\n                    <th>📄 File</th>\n                    <th>📅 Queued</th>\n                    <th>📊 Status</th>\n                    <th>🎯 Target</th>\n                    <th>⚡ Actions</th>\n                  </tr>\n                </thead>\n                <tbody>\n                  <tr>\n                    <td><strong>movies_collection.m3u</strong></td>\n                    <td>2025-07-15 16:30</td>\n                    <td><span className=\"badge bg-warning\">⏳ Queued</span></td>\n                    <td>Main Server</td>\n                    <td>\n                      <Button size=\"sm\" variant=\"outline-danger\" className=\"me-1\">▶️</Button>\n                      <Button size=\"sm\" variant=\"outline-danger\">❌</Button>\n                    </td>\n                  </tr>\n                  <tr>\n                    <td><strong>action_movies.m3u</strong></td>\n                    <td>2025-07-15 16:25</td>\n                    <td><span className=\"badge bg-success\">✅ Processing</span></td>\n                    <td>Cloud Server</td>\n                    <td>\n                      <Button size=\"sm\" variant=\"outline-info\">📊</Button>\n                    </td>\n                  </tr>\n                </tbody>\n              </Table>\n            </Card.Body>\n          </Card>\n        </Col>\n      </Row>\n    </div>\n  );\n};\n\nexport default ImportVODM3U;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAEC,MAAM,EAAEC,IAAI,EAAEC,KAAK,EAAEC,WAAW,EAAEC,KAAK,EAAEC,KAAK,QAAQ,iBAAiB;AAChG,OAAOC,aAAa,MAAM,iBAAiB;AAE3C,SACEC,iBAAiB,QACZ,sBAAsB;AAC7B,SAASC,GAAG,EAAEC,WAAW,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE1D,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA;EACzB,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAG/B,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACgC,WAAW,EAAEC,cAAc,CAAC,GAAGjC,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACkC,cAAc,EAAEC,iBAAiB,CAAC,GAAGnC,QAAQ,CAAC,CAAC,CAAC;EACvD,MAAM,CAACoC,SAAS,EAAEC,YAAY,CAAC,GAAGrC,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACsC,YAAY,EAAEC,eAAe,CAAC,GAAGvC,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACwC,SAAS,EAAEC,YAAY,CAAC,GAAGzC,QAAQ,CAAC,MAAM,CAAC;;EAElD;EACA,MAAM,CAAC0C,aAAa,EAAEC,gBAAgB,CAAC,GAAG3C,QAAQ,CAAC,UAAU,CAAC;EAC9D,MAAM,CAAC4C,YAAY,EAAEC,eAAe,CAAC,GAAG7C,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAAC8C,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG/C,QAAQ,CAAC,KAAK,CAAC;;EAE/D;EACA,MAAMgD,WAAW,GAAG,OAAO;EAC3B,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGlD,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACmD,YAAY,EAAEC,eAAe,CAAC,GAAGpD,QAAQ,CAAC;IAC/CqD,YAAY,EAAE,IAAI;IAClBC,WAAW,EAAE,KAAK;IAClBC,aAAa,EAAE;EACjB,CAAC,CAAC;EACF,MAAM,CAACC,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGzD,QAAQ,CAAC,EAAE,CAAC;;EAEhE;EACA,MAAM,CAAC0D,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG3D,QAAQ,CAAC,EAAE,CAAC;EAC5D;;EAEA,MAAM4D,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI;MACF,MAAMC,MAAM,GAAG,MAAMjD,iBAAiB,CAAC,CAAC;MACxC+B,gBAAgB,CAACkB,MAAM,CAACC,OAAO,GAAG,WAAW,GAAG,OAAO,CAAC;MAExD,IAAI,CAACD,MAAM,CAACC,OAAO,EAAE;QACnBC,YAAY,CAAC,SAAS,EAAE,qDAAqD,CAAC;MAChF;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdrB,gBAAgB,CAAC,OAAO,CAAC;MACzBoB,YAAY,CAAC,QAAQ,EAAE,iCAAiC,CAAC;IAC3D;EACF,CAAC;EAED,MAAME,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACFC,OAAO,CAACC,GAAG,CAAC,gDAAgDzB,aAAa,EAAE,CAAC;;MAE5E;MACA,IAAIA,aAAa,KAAK,WAAW,EAAE;QACjCwB,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;QAC5D,MAAMC,eAAe,CAAC,CAAC;QACvB,MAAMC,kBAAkB,CAAC,CAAC;MAC5B,CAAC,MAAM;QACLH,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;QAC5D;QACAG,YAAY,CAAC,CAAC;MAChB;IAEF,CAAC,CAAC,OAAON,KAAK,EAAE;MACdE,OAAO,CAACF,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvD,IAAIO,MAAM,CAACC,QAAQ,EAAE;QACnBD,MAAM,CAACC,QAAQ,CAAC,OAAO,EAAE,mCAAmCR,KAAK,CAACS,OAAO,EAAE,CAAC;MAC9E;MACA;MACAH,YAAY,CAAC,CAAC;IAChB;EACF,CAAC;;EAED;EACA,MAAMF,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACFF,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAC;MAEzD,MAAMO,QAAQ,GAAG,MAAMC,KAAK,CAAC,sDAAsD,CAAC;MACpFT,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEO,QAAQ,CAACE,MAAM,EAAEF,QAAQ,CAACG,UAAU,CAAC;MAE/E,MAAMC,MAAM,GAAG,MAAMJ,QAAQ,CAACK,IAAI,CAAC,CAAC;MACpCb,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEW,MAAM,CAAC;MAE1C,IAAIA,MAAM,CAAChB,OAAO,IAAIgB,MAAM,CAACE,IAAI,EAAE;QACjC,MAAMC,OAAO,GAAGH,MAAM,CAACE,IAAI,CAACE,GAAG,CAACC,MAAM,KAAK;UACzCC,EAAE,EAAED,MAAM,CAACE,SAAS;UACpBC,IAAI,EAAEH,MAAM,CAACI,WAAW,IAAI,UAAUJ,MAAM,CAACE,SAAS,EAAE;UACxDG,EAAE,EAAEL,MAAM,CAACM,SAAS,IAAI,YAAY;UACpCC,IAAI,EAAE,GAAGP,MAAM,CAACQ,aAAa,IAAI,CAAC,UAAU;UAAE;UAC9CA,aAAa,EAAER,MAAM,CAACQ,aAAa,IAAI,CAAC;UACxCf,MAAM,EAAEO,MAAM,CAACS,aAAa,KAAK,CAAC,GAAG,QAAQ,GAAG;QAClD,CAAC,CAAC,CAAC;QAEH1B,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEc,OAAO,CAAC;QAChDtB,mBAAmB,CAACsB,OAAO,CAAC;QAC5Bf,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEc,OAAO,CAACY,MAAM,EAAE,YAAY,CAAC;QAErE,IAAItB,MAAM,CAACC,QAAQ,EAAE;UACnBD,MAAM,CAACC,QAAQ,CAAC,SAAS,EAAE,cAAcS,OAAO,CAACY,MAAM,6BAA6B,CAAC;QACvF;MACF,CAAC,MAAM;QACL3B,OAAO,CAACF,KAAK,CAAC,yBAAyB,EAAEc,MAAM,CAAC;QAChD,MAAM,IAAIgB,KAAK,CAAChB,MAAM,CAACd,KAAK,IAAI,kCAAkC,CAAC;MACrE;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdE,OAAO,CAACF,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD,IAAIO,MAAM,CAACC,QAAQ,EAAE;QACnBD,MAAM,CAACC,QAAQ,CAAC,OAAO,EAAE,gCAAgCR,KAAK,CAACS,OAAO,EAAE,CAAC;MAC3E;MACA,MAAMT,KAAK;IACb;EACF,CAAC;;EAED;EACA,MAAM,CAAC+B,cAAc,EAAEC,iBAAiB,CAAC,GAAGhG,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACiG,aAAa,EAAEC,gBAAgB,CAAC,GAAGlG,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACmG,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGpG,QAAQ,CAAC,EAAE,CAAC;EAChE,MAAM,CAACqG,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGtG,QAAQ,CAAC,KAAK,CAAC;;EAEjE;EACA,MAAMqE,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI;MACFiC,oBAAoB,CAAC,IAAI,CAAC;MAC1BpC,OAAO,CAACC,GAAG,CAAC,wDAAwD,CAAC;MACrE,MAAMW,MAAM,GAAG,MAAMhE,WAAW,CAACyF,aAAa,CAAC,CAAC;MAEhD,IAAIzB,MAAM,CAAChB,OAAO,IAAIgB,MAAM,CAAC0B,UAAU,EAAE;QACvC,MAAMA,UAAU,GAAG1B,MAAM,CAAC0B,UAAU,CAACtB,GAAG,CAACuB,GAAG,KAAK;UAC/CrB,EAAE,EAAEqB,GAAG,CAACrB,EAAE;UACVE,IAAI,EAAEmB,GAAG,CAACnB,IAAI;UACdoB,WAAW,EAAED,GAAG,CAACC,WAAW;UAC5BC,aAAa,EAAEF,GAAG,CAACE,aAAa;UAChCC,aAAa,EAAEH,GAAG,CAACG,aAAa;UAChCC,eAAe,EAAE,IAAIJ,GAAG,CAACrB,EAAE,GAAG,CAAC;QACjC,CAAC,CAAC,CAAC;QAEHc,gBAAgB,CAACM,UAAU,CAAC;QAC5BJ,qBAAqB,CAACI,UAAU,CAAC;QACjCM,qBAAqB,CAACN,UAAU,CAAC;QACjCtC,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEqC,UAAU,CAACX,MAAM,CAAC;QAExD,IAAItB,MAAM,CAACC,QAAQ,EAAE;UACnBD,MAAM,CAACC,QAAQ,CAAC,SAAS,EAAE,cAAcgC,UAAU,CAACX,MAAM,6BAA6B,CAAC;QAC1F;MACF,CAAC,MAAM;QACL,MAAM,IAAIC,KAAK,CAAC,kCAAkC,CAAC;MACrD;IACF,CAAC,CAAC,OAAO9B,KAAK,EAAE;MACdE,OAAO,CAACF,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClD,IAAIO,MAAM,CAACC,QAAQ,EAAE;QACnBD,MAAM,CAACC,QAAQ,CAAC,OAAO,EAAE,gCAAgCR,KAAK,CAACS,OAAO,EAAE,CAAC;MAC3E;MACA;MACAsC,kBAAkB,CAAC,CAAC;IACtB,CAAC,SAAS;MACRT,oBAAoB,CAAC,KAAK,CAAC;IAC7B;EACF,CAAC;;EAED;EACA,MAAMU,gBAAgB,GAAIC,UAAU,IAAK;IACvC,IAAI,CAACA,UAAU,CAACC,IAAI,CAAC,CAAC,EAAE;MACtBd,qBAAqB,CAACH,aAAa,CAAC;MACpC;IACF;IAEA,MAAMkB,WAAW,GAAGF,UAAU,CAACG,WAAW,CAAC,CAAC;IAC5C,MAAMC,QAAQ,GAAGpB,aAAa,CAACqB,MAAM,CAACb,GAAG,IACvCA,GAAG,CAACnB,IAAI,CAAC8B,WAAW,CAAC,CAAC,CAACG,QAAQ,CAACJ,WAAW,CAAC,IAC5CV,GAAG,CAACrB,EAAE,CAACoC,QAAQ,CAAC,CAAC,CAACD,QAAQ,CAACN,UAAU,CAAC,IACrCR,GAAG,CAACE,aAAa,IAAIF,GAAG,CAACE,aAAa,CAACS,WAAW,CAAC,CAAC,CAACG,QAAQ,CAACJ,WAAW,CAC5E,CAAC;IAEDf,qBAAqB,CAACiB,QAAQ,CAAC;EACjC,CAAC;;EAED;EACA,MAAMI,oBAAoB,GAAIC,CAAC,IAAK;IAClC,MAAMC,KAAK,GAAGD,CAAC,CAACE,MAAM,CAACD,KAAK;IAC5B3B,iBAAiB,CAAC2B,KAAK,CAAC;IACxBX,gBAAgB,CAACW,KAAK,CAAC;EACzB,CAAC;;EAED;EACA,MAAMZ,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,MAAMc,cAAc,GAAG,CACrB;MAAEzC,EAAE,EAAE,IAAI;MAAEE,IAAI,EAAE,qBAAqB;MAAEoB,WAAW,EAAE;IAAK,CAAC,EAC5D;MAAEtB,EAAE,EAAE,IAAI;MAAEE,IAAI,EAAE,QAAQ;MAAEoB,WAAW,EAAE;IAAK,CAAC,EAC/C;MAAEtB,EAAE,EAAE,IAAI;MAAEE,IAAI,EAAE,OAAO;MAAEoB,WAAW,EAAE;IAAK,CAAC,EAC9C;MAAEtB,EAAE,EAAE,IAAI;MAAEE,IAAI,EAAE,SAAS;MAAEoB,WAAW,EAAE;IAAK,CAAC,EAChD;MAAEtB,EAAE,EAAE,IAAI;MAAEE,IAAI,EAAE,eAAe;MAAEoB,WAAW,EAAE;IAAK,CAAC,CACvD;IACDR,gBAAgB,CAAC2B,cAAc,CAAC;IAChCzB,qBAAqB,CAACyB,cAAc,CAAC;IACrCf,qBAAqB,CAACe,cAAc,CAAC;IACrC3D,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE0D,cAAc,CAAC;EAC3D,CAAC;;EAED;EACA,MAAMC,kBAAkB,GAAIC,YAAY,IAAK;IAC3C,MAAMzC,IAAI,GAAGyC,YAAY,CAACX,WAAW,CAAC,CAAC;IAEvC,IAAI9B,IAAI,CAACiC,QAAQ,CAAC,OAAO,CAAC,IAAIjC,IAAI,CAACiC,QAAQ,CAAC,MAAM,CAAC,IAAIjC,IAAI,CAACiC,QAAQ,CAAC,QAAQ,CAAC,EAAE;MAC9E,OAAO,OAAO;IAChB,CAAC,MAAM,IAAIjC,IAAI,CAACiC,QAAQ,CAAC,QAAQ,CAAC,IAAIjC,IAAI,CAACiC,QAAQ,CAAC,MAAM,CAAC,IAAIjC,IAAI,CAACiC,QAAQ,CAAC,OAAO,CAAC,EAAE;MACrF,OAAO,QAAQ;IACjB,CAAC,MAAM,IAAIjC,IAAI,CAACiC,QAAQ,CAAC,MAAM,CAAC,IAAIjC,IAAI,CAACiC,QAAQ,CAAC,IAAI,CAAC,IAAIjC,IAAI,CAACiC,QAAQ,CAAC,SAAS,CAAC,IAAIjC,IAAI,CAACiC,QAAQ,CAAC,MAAM,CAAC,IAAIjC,IAAI,CAACiC,QAAQ,CAAC,OAAO,CAAC,EAAE;MACtI,OAAO,MAAM;IACf,CAAC,MAAM,IAAIjC,IAAI,CAACiC,QAAQ,CAAC,OAAO,CAAC,IAAIjC,IAAI,CAACiC,QAAQ,CAAC,OAAO,CAAC,IAAIjC,IAAI,CAACiC,QAAQ,CAAC,IAAI,CAAC,EAAE;MAClF,OAAO,OAAO;IAChB;IAEA,OAAO,OAAO,CAAC,CAAC;EAClB,CAAC;;EAED;EACA,MAAMjD,YAAY,GAAGA,CAAA,KAAM;IACzBX,mBAAmB,CAAC,CAClB;MAAEyB,EAAE,EAAE,CAAC;MAAEE,IAAI,EAAE,gBAAgB;MAAEE,EAAE,EAAE,eAAe;MAAEE,IAAI,EAAE;IAAM,CAAC,EACnE;MAAEN,EAAE,EAAE,CAAC;MAAEE,IAAI,EAAE,WAAW;MAAEE,EAAE,EAAE,eAAe;MAAEE,IAAI,EAAE;IAAM,CAAC,EAC9D;MAAEN,EAAE,EAAE,CAAC;MAAEE,IAAI,EAAE,aAAa;MAAEE,EAAE,EAAE,eAAe;MAAEE,IAAI,EAAE;IAAM,CAAC,EAChE;MAAEN,EAAE,EAAE,CAAC;MAAEE,IAAI,EAAE,eAAe;MAAEE,EAAE,EAAE,eAAe;MAAEE,IAAI,EAAE;IAAM,CAAC,CACnE,CAAC;IAEFoB,qBAAqB,CAAC,CACpB;MAAE1B,EAAE,EAAE,CAAC;MAAEE,IAAI,EAAE,eAAe;MAAE0C,IAAI,EAAE;IAAQ,CAAC,EAC/C;MAAE5C,EAAE,EAAE,CAAC;MAAEE,IAAI,EAAE,eAAe;MAAE0C,IAAI,EAAE;IAAQ,CAAC,EAC/C;MAAE5C,EAAE,EAAE,CAAC;MAAEE,IAAI,EAAE,cAAc;MAAE0C,IAAI,EAAE;IAAQ,CAAC,EAC9C;MAAE5C,EAAE,EAAE,CAAC;MAAEE,IAAI,EAAE,eAAe;MAAE0C,IAAI,EAAE;IAAQ,CAAC,EAC/C;MAAE5C,EAAE,EAAE,CAAC;MAAEE,IAAI,EAAE,eAAe;MAAE0C,IAAI,EAAE;IAAQ,CAAC,EAC/C;MAAE5C,EAAE,EAAE,CAAC;MAAEE,IAAI,EAAE,aAAa;MAAE0C,IAAI,EAAE;IAAQ,CAAC,EAC7C;MAAE5C,EAAE,EAAE,CAAC;MAAEE,IAAI,EAAE,WAAW;MAAE0C,IAAI,EAAE;IAAQ,CAAC,EAC3C;MAAE5C,EAAE,EAAE,CAAC;MAAEE,IAAI,EAAE,UAAU;MAAE0C,IAAI,EAAE;IAAQ,CAAC,EAC1C;MAAE5C,EAAE,EAAE,CAAC;MAAEE,IAAI,EAAE,SAAS;MAAE0C,IAAI,EAAE;IAAQ,CAAC,CAC1C,CAAC;IAEF,IAAIzD,MAAM,CAACC,QAAQ,EAAE;MACnBD,MAAM,CAACC,QAAQ,CAAC,SAAS,EAAE,8CAA8C,CAAC;IAC5E;EACF,CAAC;EAED,MAAMyD,gBAAgB,GAAGA,CAACxD,OAAO,EAAEuD,IAAI,GAAG,MAAM,KAAK;IACnDzF,eAAe,CAACkC,OAAO,CAAC;IACxBhC,YAAY,CAACuF,IAAI,CAAC;IAClB3F,YAAY,CAAC,IAAI,CAAC;IAClB6F,UAAU,CAAC,MAAM7F,YAAY,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC;EAC7C,CAAC;;EAED;EACA,MAAM0B,YAAY,GAAGA,CAACiE,IAAI,EAAEvD,OAAO,KAAK;IACtCwD,gBAAgB,CAACxD,OAAO,EAAEuD,IAAI,CAAC;EACjC,CAAC;;EAED;EACA,MAAMG,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,MAAMC,SAAS,GAAGC,QAAQ,CAACC,aAAa,CAAC,OAAO,CAAC;IACjDF,SAAS,CAACJ,IAAI,GAAG,MAAM;IACvBI,SAAS,CAACG,MAAM,GAAG,YAAY;IAC/BH,SAAS,CAACI,QAAQ,GAAId,CAAC,IAAK;MAC1B,MAAMe,IAAI,GAAGf,CAAC,CAACE,MAAM,CAACc,KAAK,CAAC,CAAC,CAAC;MAC9B,IAAID,IAAI,EAAE;QACRE,gBAAgB,CAAC;UAAEf,MAAM,EAAE;YAAEc,KAAK,EAAE,CAACD,IAAI;UAAE;QAAE,CAAC,CAAC;MACjD;IACF,CAAC;IACDL,SAAS,CAACQ,KAAK,CAAC,CAAC;EACnB,CAAC;EAED,MAAMC,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI,CAAC/G,YAAY,EAAE;IAEnBiB,mBAAmB,CAAC,IAAI,CAAC;IACzBF,eAAe,CAAC,IAAI,CAAC;IAErB,IAAI;MACFkB,YAAY,CAAC,MAAM,EAAE,2BAA2B,CAAC;MAEjD,MAAMW,QAAQ,GAAG,MAAM7D,GAAG,CAACiI,MAAM,CAACC,WAAW,CAACjH,YAAY,CAAC;MAE3D,IAAI4C,QAAQ,CAACZ,OAAO,EAAE;QAAA,IAAAkF,qBAAA;QACpBnG,eAAe,CAAC6B,QAAQ,CAACM,IAAI,CAAC;;QAE9B;QACAjB,YAAY,CAAC,SAAS,EAAE,oDAAoD,EAAAiF,qBAAA,GAAAtE,QAAQ,CAACM,IAAI,CAACiE,cAAc,cAAAD,qBAAA,uBAA5BA,qBAAA,CAA8BE,iBAAiB,KAAI,CAAC,0DAA0D,CAAC;MAC7L,CAAC,MAAM;QACL,MAAM,IAAIpD,KAAK,CAACpB,QAAQ,CAACV,KAAK,IAAI,0BAA0B,CAAC;MAC/D;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdE,OAAO,CAACF,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7CD,YAAY,CAAC,QAAQ,EAAE,+BAA+BC,KAAK,CAACS,OAAO,EAAE,CAAC;IACxE,CAAC,SAAS;MACR1B,mBAAmB,CAAC,KAAK,CAAC;IAC5B;EACF,CAAC;EAED,MAAMoG,mBAAmB,GAAGA,CAAA,KAAM;IAChCtG,eAAe,CAAC,IAAI,CAAC;IACrBd,eAAe,CAAC,IAAI,CAAC;IACrB;IACAgC,YAAY,CAAC,MAAM,EAAE,iDAAiD,CAAC;EACzE,CAAC;;EAED;EACA9D,SAAS,CAAC,MAAM;IACd2D,kBAAkB,CAAC,CAAC;IACpB;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA3D,SAAS,CAAC,MAAM;IACd,IAAIyC,aAAa,KAAK,UAAU,EAAE;MAChCuB,eAAe,CAAC,CAAC;IACnB;IACA;EACF,CAAC,EAAE,CAACvB,aAAa,CAAC,CAAC;EAEnB,MAAMiG,gBAAgB,GAAG,MAAOS,KAAK,IAAK;IACxC,MAAMX,IAAI,GAAGW,KAAK,CAACxB,MAAM,CAACc,KAAK,CAAC,CAAC,CAAC;IAClC3G,eAAe,CAAC0G,IAAI,CAAC;IACrB5F,eAAe,CAAC,IAAI,CAAC;IAErB,IAAI4F,IAAI,EAAE;MACR1E,YAAY,CAAC,MAAM,EAAE,yBAAyB0E,IAAI,CAACnD,IAAI,KAAK,CAACmD,IAAI,CAACY,IAAI,GAAG,IAAI,GAAG,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC;;MAEvG;MACA,IAAI/E,MAAM,CAACC,QAAQ,EAAE;QACnBD,MAAM,CAACC,QAAQ,CAAC,qBAAqBiE,IAAI,CAACnD,IAAI,EAAE,EAAE,MAAM,CAAC;QACzDf,MAAM,CAACC,QAAQ,CAAC,iBAAiB,CAACiE,IAAI,CAACY,IAAI,GAAG,IAAI,GAAG,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC,KAAK,EAAE,MAAM,CAAC;MACrF;IACF;EACF,CAAC;EAED,MAAMC,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI,CAACzH,YAAY,IAAI,CAACmB,aAAa,EAAE;MACnCc,YAAY,CAAC,SAAS,EAAE,yEAAyE,CAAC;MAClG;IACF;IAEA,IAAI,CAACnB,YAAY,EAAE;MACjBmB,YAAY,CAAC,SAAS,EAAE,oDAAoD,CAAC;MAC7E;IACF;IAEA,IAAIQ,MAAM,CAACC,QAAQ,EAAE;MACnBD,MAAM,CAACC,QAAQ,CAAC,6BAA6B1C,YAAY,CAACwD,IAAI,EAAE,EAAE,MAAM,CAAC;MACzEf,MAAM,CAACC,QAAQ,CAAC,iBAAiB,CAAC1C,YAAY,CAACuH,IAAI,GAAG,IAAI,GAAG,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC,KAAK,EAAE,MAAM,CAAC;MAC3F/E,MAAM,CAACC,QAAQ,CAAC,oBAAoBxB,WAAW,EAAE,EAAE,MAAM,CAAC;MAC1DuB,MAAM,CAACC,QAAQ,CAAC,sBAAsBvB,aAAa,EAAE,EAAE,MAAM,CAAC;IAChE;IAEAhB,cAAc,CAAC,IAAI,CAAC;IACpBE,iBAAiB,CAAC,CAAC,CAAC;IAEpB,IAAI;MACF;MACA,MAAMqH,YAAY,GAAG;QACnBxG,WAAW;QACXC,aAAa;QACbE,YAAY;QACZqD,UAAU,EAAEhD,kBAAkB;QAC9BiG,WAAW,EAAE,IAAI;QACjBC,oBAAoB,EAAE;MACxB,CAAC;MAED3F,YAAY,CAAC,MAAM,EAAE,4CAA4C,CAAC;MAClE5B,iBAAiB,CAAC,EAAE,CAAC;;MAErB;MACA,MAAMwH,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;MAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAE/H,YAAY,CAAC;MACrC6H,QAAQ,CAACE,MAAM,CAAC,QAAQ,EAAEC,IAAI,CAACC,SAAS,CAACP,YAAY,CAAC,CAAC;MAEvD,IAAIjF,MAAM,CAACC,QAAQ,EAAE;QACnBD,MAAM,CAACC,QAAQ,CAAC,qCAAqC,EAAE,MAAM,CAAC;MAChE;;MAEA;MACA,MAAMwF,eAAe,GAAG,MAAMnJ,GAAG,CAACiI,MAAM,CAACC,WAAW,CAACjH,YAAY,CAAC;MAClEK,iBAAiB,CAAC,EAAE,CAAC;MAErB,IAAI,CAAC6H,eAAe,CAAClG,OAAO,EAAE;QAC5B,MAAM,IAAIgC,KAAK,CAACkE,eAAe,CAAChG,KAAK,IAAI,sBAAsB,CAAC;MAClE;MAEAD,YAAY,CAAC,MAAM,EAAE,gDAAgD,CAAC;MACtE;MACA,MAAMkG,WAAW,GAAG,MAAM,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;QACzD,MAAMC,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;QAC/BD,MAAM,CAACE,MAAM,GAAI7C,CAAC,IAAKyC,OAAO,CAACzC,CAAC,CAACE,MAAM,CAAC9C,MAAM,CAAC;QAC/CuF,MAAM,CAACG,OAAO,GAAG,MAAMJ,MAAM,CAAC,IAAItE,KAAK,CAAC,oBAAoB,CAAC,CAAC;QAC9DuE,MAAM,CAACI,UAAU,CAAC3I,YAAY,CAAC;MACjC,CAAC,CAAC;MAEFK,iBAAiB,CAAC,EAAE,CAAC;;MAErB;MACA,MAAMuI,aAAa,GAAG,MAAM/F,KAAK,CAAC,+CAA+C,EAAE;QACjFgG,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UAAE,cAAc,EAAE;QAAmB,CAAC;QAC/CC,IAAI,EAAEf,IAAI,CAACC,SAAS,CAAC;UAAEe,UAAU,EAAEb;QAAY,CAAC;MAClD,CAAC,CAAC;MAEF,MAAMc,WAAW,GAAG,MAAML,aAAa,CAAC3F,IAAI,CAAC,CAAC;MAC9C,IAAI,CAACgG,WAAW,CAACjH,OAAO,EAAE;QACxB,MAAM,IAAIgC,KAAK,CAACiF,WAAW,CAAC/G,KAAK,IAAI,2BAA2B,CAAC;MACnE;MAEA7B,iBAAiB,CAAC,EAAE,CAAC;;MAErB;MACA,MAAM6I,aAAa,GAAG;QACpBC,MAAM,EAAEF,WAAW,CAAC/F,IAAI,CAACiG,MAAM;QAC/B5F,SAAS,EAAE6F,QAAQ,CAACjI,aAAa,CAAC;QAClCkI,WAAW,EAAE3H,kBAAkB,CAACqC,MAAM,GAAG,CAAC,GAAGqF,QAAQ,CAAC1H,kBAAkB,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI;QACnF4H,WAAW,EAAE,IAAI,CAAC;MACpB,CAAC;MAED,MAAMC,cAAc,GAAG,MAAM1G,KAAK,CAAC,yCAAyC,EAAE;QAC5EgG,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UAAE,cAAc,EAAE;QAAmB,CAAC;QAC/CC,IAAI,EAAEf,IAAI,CAACC,SAAS,CAACiB,aAAa;MACpC,CAAC,CAAC;MAEF,MAAMM,YAAY,GAAG,MAAMD,cAAc,CAACtG,IAAI,CAAC,CAAC;;MAEhD;MACAb,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEmH,YAAY,CAAC;MAEpD,IAAI,CAACA,YAAY,CAACxH,OAAO,EAAE;QACzB,MAAM,IAAIgC,KAAK,CAACwF,YAAY,CAACtH,KAAK,IAAI,6BAA6B,CAAC;MACtE;;MAEA;MACA7B,iBAAiB,CAAC,GAAG,CAAC;;MAEtB;MACA,MAAMoJ,KAAK,GAAGD,YAAY,CAACtG,IAAI,IAAIsG,YAAY;MAC/C,MAAME,cAAc,GAAG,mEAAmED,KAAK,CAACE,QAAQ,IAAI,CAAC,4BAA4BF,KAAK,CAACG,MAAM,IAAI,CAAC,eAAeH,KAAK,CAACI,cAAc,IAAI,CAAC,yBAAyBJ,KAAK,CAACK,iBAAiB,IAAI,CAAC,oBAAoB;MAE3Q7H,YAAY,CAAC,SAAS,EAAEyH,cAAc,CAAC;MAEvC,IAAIjH,MAAM,CAACC,QAAQ,EAAE;QACnBD,MAAM,CAACC,QAAQ,CAAC,wCAAwC1C,YAAY,CAACwD,IAAI,EAAE,EAAE,SAAS,CAAC;QACvFf,MAAM,CAACC,QAAQ,CAAC,aAAasF,IAAI,CAACC,SAAS,CAACuB,YAAY,CAAC,EAAE,EAAE,MAAM,CAAC;MACtE;;MAEA;MACApD,UAAU,CAAC,MAAM;QACfnG,eAAe,CAAC,IAAI,CAAC;QACrBc,eAAe,CAAC,IAAI,CAAC;QACrB;QACAK,gBAAgB,CAAC,EAAE,CAAC;QACpBO,qBAAqB,CAAC,EAAE,CAAC;MAC3B,CAAC,EAAE,IAAI,CAAC;IAEV,CAAC,CAAC,OAAOO,KAAK,EAAE;MACdE,OAAO,CAACF,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC;MACzCD,YAAY,CAAC,QAAQ,EAAE,uCAAuCC,KAAK,CAACS,OAAO,EAAE,CAAC;MAE9E,IAAIF,MAAM,CAACC,QAAQ,EAAE;QACnBD,MAAM,CAACC,QAAQ,CAAC,wBAAwBR,KAAK,CAACS,OAAO,EAAE,EAAE,OAAO,CAAC;MACnE;IACF,CAAC,SAAS;MACRxC,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;EAED,oBACEjB,OAAA;IAAK6K,KAAK,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,QAAQ,EAAE;IAAO,CAAE;IAAAC,QAAA,gBAC9ChL,OAAA;MAAKiL,SAAS,EAAC,wDAAwD;MAAAD,QAAA,gBACrEhL,OAAA;QAAIiL,SAAS,EAAC,aAAa;QAAAD,QAAA,EAAC;MAAuB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACxDrL,OAAA,CAACL,aAAa;QACZiE,MAAM,EAAElC,aAAc;QACtB4J,OAAO,EAAE1I;MAAmB;QAAAsI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7B,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,EAELjK,SAAS,iBACRpB,OAAA,CAACT,KAAK;MAACgM,OAAO,EAAE/J,SAAU;MAACgK,WAAW;MAACC,OAAO,EAAEA,CAAA,KAAMpK,YAAY,CAAC,KAAK,CAAE;MAAA2J,QAAA,gBACxEhL,OAAA,CAACT,KAAK,CAACmM,OAAO;QAAAV,QAAA,GACXxJ,SAAS,KAAK,SAAS,IAAI,YAAY,EACvCA,SAAS,KAAK,QAAQ,IAAI,UAAU,EACpCA,SAAS,KAAK,SAAS,IAAI,aAAa,EACxCA,SAAS,KAAK,MAAM,IAAI,gBAAgB;MAAA;QAAA0J,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5B,CAAC,eAChBrL,OAAA;QAAAgL,QAAA,EAAI1J;MAAY;QAAA4J,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChB,CACR,eAEDrL,OAAA,CAACd,GAAG;MAAC+L,SAAS,EAAC,MAAM;MAAAD,QAAA,gBACnBhL,OAAA,CAACb,GAAG;QAACwM,EAAE,EAAE,CAAE;QAAAX,QAAA,eACThL,OAAA,CAACZ,IAAI;UAAC6L,SAAS,EAAC,iBAAiB;UAAAD,QAAA,gBAC/BhL,OAAA,CAACZ,IAAI,CAACwM,MAAM;YAACX,SAAS,EAAC,wEAAwE;YAAAD,QAAA,gBAC7FhL,OAAA;cAAIiL,SAAS,EAAC,MAAM;cAAAD,QAAA,EAAC;YAAc;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EACvC3J,aAAa,KAAK,WAAW,iBAC5B1B,OAAA,CAACN,KAAK;cAACmM,EAAE,EAAC,SAAS;cAAAb,QAAA,gBACjBhL,OAAA;gBAAGiL,SAAS,EAAC;cAAmB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,kBACvC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CACR;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACU,CAAC,eACdrL,OAAA,CAACZ,IAAI,CAAC0M,IAAI;YAAAd,QAAA,eACRhL,OAAA,CAACV,IAAI;cAAA0L,QAAA,gBACHhL,OAAA,CAACV,IAAI,CAACyM,KAAK;gBAACd,SAAS,EAAC,MAAM;gBAAAD,QAAA,gBAC1BhL,OAAA,CAACV,IAAI,CAAC0M,KAAK;kBAAAhB,QAAA,EAAC;gBAAe;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACxCrL,OAAA,CAACV,IAAI,CAAC2M,OAAO;kBACXjF,IAAI,EAAC,MAAM;kBACXO,MAAM,EAAC,YAAY;kBACnB2E,QAAQ,EAAEvE,gBAAiB;kBAC3BwE,QAAQ,EAAEnL;gBAAY;kBAAAkK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB,CAAC,eACFrL,OAAA,CAACV,IAAI,CAAC8M,IAAI;kBAACnB,SAAS,EAAC,YAAY;kBAAAD,QAAA,EAAC;gBAElC;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,EAEZvK,YAAY,iBACXd,OAAA,CAAAE,SAAA;gBAAA8K,QAAA,gBACEhL,OAAA,CAACT,KAAK;kBAACgM,OAAO,EAAC,MAAM;kBAAAP,QAAA,gBACnBhL,OAAA;oBAAAgL,QAAA,EAAQ;kBAAc;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,KAAC,EAACvK,YAAY,CAACwD,IAAI,eAACtE,OAAA;oBAAAkL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACxDrL,OAAA;oBAAAgL,QAAA,EAAQ;kBAAK;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,KAAC,EAAC,CAACvK,YAAY,CAACuH,IAAI,GAAG,IAAI,GAAG,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC,EAAC,KACvE;gBAAA;kBAAA4C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,EAEPvJ,gBAAgB,iBACf9B,OAAA,CAACT,KAAK;kBAACgM,OAAO,EAAC,WAAW;kBAAAP,QAAA,eACxBhL,OAAA;oBAAKiL,SAAS,EAAC,2BAA2B;oBAAAD,QAAA,gBACxChL,OAAA;sBAAKiL,SAAS,EAAC,uCAAuC;sBAACoB,IAAI,EAAC;oBAAQ;sBAAAnB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,yBAE7E;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CACR,EAEAzJ,YAAY,IAAI,CAACE,gBAAgB,iBAChC9B,OAAA,CAACT,KAAK;kBAACgM,OAAO,EAAC,SAAS;kBAAAP,QAAA,gBACtBhL,OAAA;oBAAAgL,QAAA,EAAI;kBAAuB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAChCrL,OAAA,CAACd,GAAG;oBAAA8L,QAAA,gBACFhL,OAAA,CAACb,GAAG;sBAACmN,EAAE,EAAE,CAAE;sBAAAtB,QAAA,gBACThL,OAAA;wBAAAgL,QAAA,EAAQ;sBAAY;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,KAAC,EAAC,EAAAhL,qBAAA,GAAAuB,YAAY,CAACqG,cAAc,cAAA5H,qBAAA,uBAA3BA,qBAAA,CAA6BkM,WAAW,KAAI,CAAC,eAACvM,OAAA;wBAAAkL,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eAClFrL,OAAA;wBAAAgL,QAAA,EAAQ;sBAAe;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,KAAC,EAAC,EAAA/K,sBAAA,GAAAsB,YAAY,CAACqG,cAAc,cAAA3H,sBAAA,uBAA3BA,sBAAA,CAA6BkM,YAAY,KAAI,CAAC,eAACxM,OAAA;wBAAAkL,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACtFrL,OAAA;wBAAAgL,QAAA,EAAQ;sBAAY;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,KAAC,EAAC,EAAA9K,sBAAA,GAAAqB,YAAY,CAACqG,cAAc,cAAA1H,sBAAA,uBAA3BA,sBAAA,CAA6BkM,SAAS,KAAI,CAAC;oBAAA;sBAAAvB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvE,CAAC,eACNrL,OAAA,CAACb,GAAG;sBAACmN,EAAE,EAAE,CAAE;sBAAAtB,QAAA,gBACThL,OAAA;wBAAAgL,QAAA,EAAQ;sBAAkB;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,KAAC,EAAC,EAAA7K,sBAAA,GAAAoB,YAAY,CAACqG,cAAc,cAAAzH,sBAAA,uBAA3BA,sBAAA,CAA6B0H,iBAAiB,KAAI,CAAC,eAAClI,OAAA;wBAAAkL,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eAC9FrL,OAAA;wBAAAgL,QAAA,EAAQ;sBAAU;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,KAAC,EAAC,CAAA5K,sBAAA,GAAAmB,YAAY,CAACqG,cAAc,cAAAxH,sBAAA,eAA3BA,sBAAA,CAA6BiM,oBAAoB,GAAG,OAAO,GAAG,MAAM,eAAC1M,OAAA;wBAAAkL,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACvGrL,OAAA;wBAAAgL,QAAA,EAAQ;sBAAU;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,KAAC,EAAC,EAAA3K,qBAAA,GAAAkB,YAAY,CAAC+K,SAAS,cAAAjM,qBAAA,uBAAtBA,qBAAA,CAAwBkM,OAAO,KAAI,CAAC,EAAC,KACpE;oBAAA;sBAAA1B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,EAEL,EAAA1K,qBAAA,GAAAiB,YAAY,CAACiL,aAAa,cAAAlM,qBAAA,wBAAAC,sBAAA,GAA1BD,qBAAA,CAA4BsJ,MAAM,cAAArJ,sBAAA,uBAAlCA,sBAAA,CAAoCkC,OAAO,kBAC1C9C,OAAA;oBAAKiL,SAAS,EAAC,MAAM;oBAAAD,QAAA,gBACnBhL,OAAA;sBAAAgL,QAAA,EAAQ;oBAAgB;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAAC,EAAAxK,sBAAA,GAAAe,YAAY,CAACiL,aAAa,CAAC5C,MAAM,CAACjG,IAAI,cAAAnD,sBAAA,uBAAtCA,sBAAA,CAAwCgE,MAAM,KAAI,CAAC;kBAAA;oBAAAqG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnF,CACN;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CACR;cAAA,eACD,CACH,eAEDrL,OAAA;gBAAKiL,SAAS,EAAC,MAAM;gBAAAD,QAAA,gBACnBhL,OAAA,CAACX,MAAM;kBACLyN,OAAO,EAAE3F,iBAAkB;kBAC3BoE,OAAO,EAAC,QAAQ;kBAChBY,QAAQ,EAAErK,gBAAiB;kBAAAkJ,QAAA,EAC5B;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,EAERvK,YAAY,IAAI,CAACc,YAAY,iBAC5B5B,OAAA,CAACX,MAAM;kBACLyN,OAAO,EAAEjF,iBAAkB;kBAC3B0D,OAAO,EAAC,MAAM;kBACdN,SAAS,EAAC,MAAM;kBAChBkB,QAAQ,EAAErK,gBAAiB;kBAAAkJ,QAAA,EAE1BlJ,gBAAgB,gBACf9B,OAAA,CAAAE,SAAA;oBAAA8K,QAAA,gBACEhL,OAAA;sBAAMiL,SAAS,EAAC,uCAAuC;sBAACoB,IAAI,EAAC;oBAAQ;sBAAAnB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,iBAE/E;kBAAA,eAAE,CAAC,GAEH;gBACD;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACK,CACT,EAEAzJ,YAAY,iBACX5B,OAAA,CAACX,MAAM;kBACLyN,OAAO,EAAE3E,mBAAoB;kBAC7BoD,OAAO,EAAC,mBAAmB;kBAC3BN,SAAS,EAAC,MAAM;kBAChBkB,QAAQ,EAAErK,gBAAiB;kBAAAkJ,QAAA,EAC5B;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CACT;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAGNrL,OAAA;gBAAKiL,SAAS,EAAC,MAAM;gBAAAD,QAAA,eACnBhL,OAAA;kBAAKiL,SAAS,EAAC,kBAAkB;kBAAAD,QAAA,gBAC/BhL,OAAA;oBAAAgL,QAAA,EAAQ;kBAAqB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,yCACtC,eAAArL,OAAA;oBAAAkL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACNrL,OAAA;oBAAAgL,QAAA,EAAO;kBAAgF;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5F;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EAEL,IAAI,iBACHrL,OAAA,CAACV,IAAI,CAACyM,KAAK;gBAACd,SAAS,EAAC,MAAM;gBAAAD,QAAA,gBAC1BhL,OAAA;kBAAKiL,SAAS,EAAC,wDAAwD;kBAAAD,QAAA,gBACrEhL,OAAA,CAACV,IAAI,CAAC0M,KAAK;oBAACf,SAAS,EAAC,MAAM;oBAAAD,QAAA,EAAC;kBAAyB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACnErL,OAAA,CAACX,MAAM;oBACLkM,OAAO,EAAC,mBAAmB;oBAC3BlD,IAAI,EAAC,IAAI;oBACTyE,OAAO,EAAE1J,eAAgB;oBACzB+I,QAAQ,EAAEnL,WAAW,IAAIU,aAAa,KAAK,WAAY;oBAAAsJ,QAAA,EACxD;kBAED;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,eACNrL,OAAA,CAACV,IAAI,CAACyN,MAAM;kBACVpG,KAAK,EAAE1E,aAAc;kBACrBiK,QAAQ,EAAGxF,CAAC,IAAKxE,gBAAgB,CAACwE,CAAC,CAACE,MAAM,CAACD,KAAK,CAAE;kBAClDwF,QAAQ,EAAEnL,WAAY;kBAAAgK,QAAA,gBAEtBhL,OAAA;oBAAQ2G,KAAK,EAAC,EAAE;oBAAAqE,QAAA,EAAC;kBAAwB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,EACjD3I,gBAAgB,CAACwB,GAAG,CAACC,MAAM,iBAC1BnE,OAAA;oBAAwB2G,KAAK,EAAExC,MAAM,CAACC,EAAG;oBAAA4G,QAAA,GACtC7G,MAAM,CAACG,IAAI,EAAC,IAAE,EAACH,MAAM,CAACK,EAAE,EAAC,MAAI,EAACL,MAAM,CAACO,IAAI;kBAAA,GAD/BP,MAAM,CAACC,EAAE;oBAAA8G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEd,CACT,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACS,CAAC,eACdrL,OAAA,CAACV,IAAI,CAAC8M,IAAI;kBAACnB,SAAS,EAAC,YAAY;kBAAAD,QAAA,GAAC,0DAEhC,EAACtI,gBAAgB,CAACmC,MAAM,GAAG,CAAC,iBAC1B7E,OAAA;oBAAMiL,SAAS,EAAC,cAAc;oBAAAD,QAAA,GAAC,UAAG,EAACtI,gBAAgB,CAACmC,MAAM,EAAC,iBAAe;kBAAA;oBAAAqG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CACjF;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CACb,EAEApJ,aAAa,iBACZjC,OAAA,CAAAE,SAAA;gBAAA8K,QAAA,gBACEhL,OAAA,CAACV,IAAI,CAACyM,KAAK;kBAACd,SAAS,EAAC,MAAM;kBAAAD,QAAA,gBAC1BhL,OAAA,CAACV,IAAI,CAAC0M,KAAK;oBAAAhB,QAAA,EAAC;kBAAuB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAChDrL,OAAA,CAACV,IAAI,CAAC0N,KAAK;oBACThG,IAAI,EAAC,UAAU;oBACfiG,KAAK,EAAC,2DAAsD;oBAC5DC,OAAO,EAAE/K,YAAY,CAACE,YAAa;oBACnC6J,QAAQ,EAAGxF,CAAC,IAAKtE,eAAe,CAAC+K,IAAI,KAAK;sBACxC,GAAGA,IAAI;sBACP9K,YAAY,EAAEqE,CAAC,CAACE,MAAM,CAACsG;oBACzB,CAAC,CAAC,CAAE;oBACJf,QAAQ,EAAEnL;kBAAY;oBAAAkK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvB,CAAC,eACFrL,OAAA,CAACV,IAAI,CAAC0N,KAAK;oBACThG,IAAI,EAAC,UAAU;oBACfiG,KAAK,EAAC,wDAA8C;oBACpDC,OAAO,EAAE/K,YAAY,CAACG,WAAY;oBAClC4J,QAAQ,EAAGxF,CAAC,IAAKtE,eAAe,CAAC+K,IAAI,KAAK;sBACxC,GAAGA,IAAI;sBACP7K,WAAW,EAAEoE,CAAC,CAACE,MAAM,CAACsG;oBACxB,CAAC,CAAC,CAAE;oBACJf,QAAQ,EAAEnL;kBAAY;oBAAAkK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvB,CAAC,eACFrL,OAAA,CAACV,IAAI,CAAC0N,KAAK;oBACThG,IAAI,EAAC,UAAU;oBACfiG,KAAK,EAAC,yDAA+C;oBACrDC,OAAO,EAAE/K,YAAY,CAACI,aAAc;oBACpC2J,QAAQ,EAAGxF,CAAC,IAAKtE,eAAe,CAAC+K,IAAI,KAAK;sBACxC,GAAGA,IAAI;sBACP5K,aAAa,EAAEmE,CAAC,CAACE,MAAM,CAACsG;oBAC1B,CAAC,CAAC,CAAE;oBACJf,QAAQ,EAAEnL;kBAAY;oBAAAkK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACQ,CAAC,eAEbrL,OAAA,CAACV,IAAI,CAACyM,KAAK;kBAACd,SAAS,EAAC,MAAM;kBAAAD,QAAA,gBAC1BhL,OAAA,CAACV,IAAI,CAAC0M,KAAK;oBAAAhB,QAAA,EAAC;kBAAyB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAClDrL,OAAA;oBAAAgL,QAAA,gBAEEhL,OAAA,CAACV,IAAI,CAAC2M,OAAO;sBACXjF,IAAI,EAAC,MAAM;sBACXoG,WAAW,EAAC,oDAAiD;sBAC7DzG,KAAK,EAAE5B,cAAe;sBACtBmH,QAAQ,EAAEzF,oBAAqB;sBAC/B0F,QAAQ,EAAEnL,WAAW,IAAIqE,iBAAkB;sBAC3C4F,SAAS,EAAC,MAAM;sBAChB5C,IAAI,EAAC;oBAAI;sBAAA6C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV,CAAC,eAGFrL,OAAA;sBAAK6K,KAAK,EAAE;wBAACwC,SAAS,EAAE,OAAO;wBAAEC,SAAS,EAAE,MAAM;wBAAEC,MAAM,EAAE,gBAAgB;wBAAEC,OAAO,EAAE,KAAK;wBAAEC,YAAY,EAAE;sBAAK,CAAE;sBAAAzC,QAAA,EAChH3F,iBAAiB,gBAChBrF,OAAA;wBAAKiL,SAAS,EAAC,wBAAwB;wBAAAD,QAAA,eACrChL,OAAA;0BAAAgL,QAAA,EAAO;wBAAsB;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAClC,CAAC,GACJlG,kBAAkB,CAACN,MAAM,GAAG,CAAC,gBAC/B7E,OAAA,CAAAE,SAAA;wBAAA8K,QAAA,gBACEhL,OAAA;0BAAKiL,SAAS,EAAC,iBAAiB;0BAAAD,QAAA,eAC9BhL,OAAA;4BAAAgL,QAAA,GAAO,YACK,EAAC7F,kBAAkB,CAACN,MAAM,EAAC,MAAI,EAACI,aAAa,CAACJ,MAAM,EAAC,gBAC/D,EAACE,cAAc,IAAI,oBAAoBA,cAAc,IAAI;0BAAA;4BAAAmG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACpD;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACL,CAAC,EACLlG,kBAAkB,CAACuI,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,CAACxJ,GAAG,CAACyJ,QAAQ,iBAC5C3N,OAAA,CAACV,IAAI,CAAC0N,KAAK;0BAEThG,IAAI,EAAC,OAAO;0BACZ1C,IAAI,EAAC,mBAAmB;0BACxB2I,KAAK,eACHjN,OAAA;4BAAAgL,QAAA,gBACEhL,OAAA;8BAAAgL,QAAA,EAAS2C,QAAQ,CAACrJ;4BAAI;8BAAA4G,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAS,CAAC,eAChCrL,OAAA;8BAAMiL,SAAS,EAAC,YAAY;8BAAAD,QAAA,GAAC,QAAM,EAAC2C,QAAQ,CAACvJ,EAAE,EAAC,GAAC;4BAAA;8BAAA8G,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAM,CAAC,eACxDrL,OAAA;8BAAK6K,KAAK,EAAE;gCAAC+C,QAAQ,EAAE,QAAQ;gCAAEC,KAAK,EAAE,MAAM;gCAAEC,SAAS,EAAE;8BAAK,CAAE;8BAAA9C,QAAA,GAC/D2C,QAAQ,CAACjI,WAAW,IAAI,CAAC,EAAC,eAC3B,EAACiI,QAAQ,CAAChI,aAAa,iBACrB3F,OAAA;gCAAAgL,QAAA,GAAM,gBAAS,EAAC2C,QAAQ,CAAChI,aAAa;8BAAA;gCAAAuF,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAO,CAC9C,EACAsC,QAAQ,CAAC/H,aAAa,iBACrB5F,OAAA;gCAAMiL,SAAS,EAAC,cAAc;gCAAAD,QAAA,EAAC;8BAAmB;gCAAAE,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAM,CACzD;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACE,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACH,CACN;0BACD6B,OAAO,EAAE1K,kBAAkB,CAAC+D,QAAQ,CAACoH,QAAQ,CAACvJ,EAAE,CAAE;0BAClD8H,QAAQ,EAAGxF,CAAC,IAAK;4BACf,IAAIA,CAAC,CAACE,MAAM,CAACsG,OAAO,EAAE;8BACpBzK,qBAAqB,CAAC,CAACkL,QAAQ,CAACvJ,EAAE,CAAC,CAAC,CAAC,CAAC;4BACxC;0BACF,CAAE;0BACF+H,QAAQ,EAAEnL,WAAY;0BACtBiK,SAAS,EAAC;wBAAM,GAzBX0C,QAAQ,CAACvJ,EAAE;0BAAA8G,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OA0BjB,CACF,CAAC,EACDlG,kBAAkB,CAACN,MAAM,GAAG,GAAG,iBAC9B7E,OAAA;0BAAKiL,SAAS,EAAC,6BAA6B;0BAAAD,QAAA,eAC1ChL,OAAA;4BAAAgL,QAAA,EAAO;0BAAoE;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAChF,CACN;sBAAA,eACD,CAAC,gBAEHrL,OAAA;wBAAKiL,SAAS,EAAC,wBAAwB;wBAAAD,QAAA,eACrChL,OAAA;0BAAAgL,QAAA,EACGjG,cAAc,GACb,mDAAmDA,cAAc,GAAG,GACpE;wBAA+B;0BAAAmG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAE5B;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACL;oBACN;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNrL,OAAA,CAACV,IAAI,CAAC8M,IAAI;oBAACnB,SAAS,EAAC,YAAY;oBAAAD,QAAA,EAAC;kBAElC;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC;cAAA,eACb,CACH,eAEDrL,OAAA,CAACV,IAAI,CAACyM,KAAK;gBAACd,SAAS,EAAC,MAAM;gBAAAD,QAAA,gBAC1BhL,OAAA,CAACV,IAAI,CAAC0M,KAAK;kBAAAhB,QAAA,EAAC;gBAAkB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC3CrL,OAAA,CAACV,IAAI,CAAC0N,KAAK;kBACThG,IAAI,EAAC,UAAU;kBACfiG,KAAK,EAAC,yCAA+B;kBACrCc,cAAc;gBAAA;kBAAA7C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf,CAAC,eACFrL,OAAA,CAACV,IAAI,CAAC0N,KAAK;kBACThG,IAAI,EAAC,UAAU;kBACfiG,KAAK,EAAC,qCAA2B;kBACjCc,cAAc;gBAAA;kBAAA7C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf,CAAC,eACFrL,OAAA,CAACV,IAAI,CAAC0N,KAAK;kBACThG,IAAI,EAAC,UAAU;kBACfiG,KAAK,EAAC,qCAA2B;kBACjCc,cAAc;gBAAA;kBAAA7C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ,CAAC,EAEZrK,WAAW,iBACVhB,OAAA;gBAAKiL,SAAS,EAAC,MAAM;gBAAAD,QAAA,gBACnBhL,OAAA,CAACV,IAAI,CAAC0M,KAAK;kBAAAhB,QAAA,EAAC;gBAAe;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACxCrL,OAAA,CAACR,WAAW;kBACVwO,GAAG,EAAE9M,cAAe;kBACpB+L,KAAK,EAAE,GAAG/L,cAAc,GAAI;kBAC5BqK,OAAO,EAAErK,cAAc,KAAK,GAAG,GAAG,SAAS,GAAG,QAAS;kBACvD+M,QAAQ,EAAE/M,cAAc,GAAG;gBAAI;kBAAAgK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CACN,eAEDrL,OAAA,CAACX,MAAM;gBACLkM,OAAO,EAAC,SAAS;gBACjBlD,IAAI,EAAC,IAAI;gBACTyE,OAAO,EAAEvE,YAAa;gBACtB4D,QAAQ,EAAE,CAACrL,YAAY,IAAI,CAACc,YAAY,IAAI,CAACK,aAAa,IAAIjB,WAAW,IAAIc,gBAAiB;gBAC9FmJ,SAAS,EAAC,OAAO;gBAAAD,QAAA,EAEhBhK,WAAW,gBACVhB,OAAA,CAAAE,SAAA;kBAAA8K,QAAA,gBACEhL,OAAA;oBAAMiL,SAAS,EAAC,uCAAuC;oBAACoB,IAAI,EAAC;kBAAQ;oBAAAnB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,kBAC/D,EAACnK,cAAc,EAAC,GAChC;gBAAA,eAAE,CAAC,GACD,CAACJ,YAAY,GACf,8BAA8B,GAC5B,CAACc,YAAY,GACf,+BAA+B,GAC7B,CAACK,aAAa,GAChB,2BAA2B,GAE3B;cACD;gBAAAiJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAENrL,OAAA,CAACb,GAAG;QAACwM,EAAE,EAAE,CAAE;QAAAX,QAAA,eACThL,OAAA,CAACZ,IAAI;UAAC6L,SAAS,EAAC,iBAAiB;UAAAD,QAAA,gBAC/BhL,OAAA,CAACZ,IAAI,CAACwM,MAAM;YAACX,SAAS,EAAC,oBAAoB;YAAAD,QAAA,eACzChL,OAAA;cAAIiL,SAAS,EAAC,MAAM;cAAAD,QAAA,EAAC;YAAwB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC,eACdrL,OAAA,CAACZ,IAAI,CAAC0M,IAAI;YAAAd,QAAA,gBACRhL,OAAA;cAAAgL,QAAA,EAAI;YAAyB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAClCrL,OAAA;cAAAgL,QAAA,gBACEhL,OAAA;gBAAAgL,QAAA,gBAAIhL,OAAA;kBAAAgL,QAAA,EAAQ;gBAAU;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,oCAAgC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACpErL,OAAA;gBAAAgL,QAAA,gBAAIhL,OAAA;kBAAAgL,QAAA,EAAQ;gBAAe;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,qCAAiC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC1ErL,OAAA;gBAAAgL,QAAA,gBAAIhL,OAAA;kBAAAgL,QAAA,EAAQ;gBAAY;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,iCAA6B;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACnErL,OAAA;gBAAAgL,QAAA,gBAAIhL,OAAA;kBAAAgL,QAAA,EAAQ;gBAAY;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,qCAAiC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrE,CAAC,eAELrL,OAAA;cAAAgL,QAAA,EAAI;YAAuB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAChCrL,OAAA;cAAAgL,QAAA,gBACEhL,OAAA;gBAAAgL,QAAA,gBAAIhL,OAAA;kBAAAgL,QAAA,EAAQ;gBAAoB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,8BAA0B;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACxErL,OAAA;gBAAAgL,QAAA,gBAAIhL,OAAA;kBAAAgL,QAAA,EAAQ;gBAAwB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,+BAA2B;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC7ErL,OAAA;gBAAAgL,QAAA,gBAAIhL,OAAA;kBAAAgL,QAAA,EAAQ;gBAAoB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,+BAA2B;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACzErL,OAAA;gBAAAgL,QAAA,gBAAIhL,OAAA;kBAAAgL,QAAA,EAAQ;gBAAuB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,uBAAmB;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClE,CAAC,eAELrL,OAAA;cAAAgL,QAAA,EAAI;YAAmB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5BrL,OAAA;cAAAgL,QAAA,gBACEhL,OAAA;gBAAAgL,QAAA,EAAI;cAA8B;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACvCrL,OAAA;gBAAAgL,QAAA,EAAI;cAAyC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAClDrL,OAAA;gBAAAgL,QAAA,EAAI;cAAqC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC9CrL,OAAA;gBAAAgL,QAAA,EAAI;cAAwC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENrL,OAAA,CAACd,GAAG;MAAA8L,QAAA,eACFhL,OAAA,CAACb,GAAG;QAAA6L,QAAA,eACFhL,OAAA,CAACZ,IAAI;UAAC6L,SAAS,EAAC,WAAW;UAAAD,QAAA,gBACzBhL,OAAA,CAACZ,IAAI,CAACwM,MAAM;YAACX,SAAS,EAAC,yBAAyB;YAAAD,QAAA,eAC9ChL,OAAA;cAAIiL,SAAS,EAAC,MAAM;cAAAD,QAAA,EAAC;YAA0B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzC,CAAC,eACdrL,OAAA,CAACZ,IAAI,CAAC0M,IAAI;YAAAd,QAAA,eACRhL,OAAA,CAACP,KAAK;cAACyO,OAAO;cAACC,KAAK;cAAAnD,QAAA,gBAClBhL,OAAA;gBAAAgL,QAAA,eACEhL,OAAA;kBAAAgL,QAAA,gBACEhL,OAAA;oBAAAgL,QAAA,EAAI;kBAAO;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAChBrL,OAAA;oBAAAgL,QAAA,EAAI;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAClBrL,OAAA;oBAAAgL,QAAA,EAAI;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAClBrL,OAAA;oBAAAgL,QAAA,EAAI;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAClBrL,OAAA;oBAAAgL,QAAA,EAAI;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,eACRrL,OAAA;gBAAAgL,QAAA,gBACEhL,OAAA;kBAAAgL,QAAA,gBACEhL,OAAA;oBAAAgL,QAAA,eAAIhL,OAAA;sBAAAgL,QAAA,EAAQ;oBAAqB;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC/CrL,OAAA;oBAAAgL,QAAA,EAAI;kBAAgB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACzBrL,OAAA;oBAAAgL,QAAA,eAAIhL,OAAA;sBAAMiL,SAAS,EAAC,kBAAkB;sBAAAD,QAAA,EAAC;oBAAQ;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC3DrL,OAAA;oBAAAgL,QAAA,EAAI;kBAAW;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACpBrL,OAAA;oBAAAgL,QAAA,gBACEhL,OAAA,CAACX,MAAM;sBAACgJ,IAAI,EAAC,IAAI;sBAACkD,OAAO,EAAC,gBAAgB;sBAACN,SAAS,EAAC,MAAM;sBAAAD,QAAA,EAAC;oBAAE;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACvErL,OAAA,CAACX,MAAM;sBAACgJ,IAAI,EAAC,IAAI;sBAACkD,OAAO,EAAC,gBAAgB;sBAAAP,QAAA,EAAC;oBAAC;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACLrL,OAAA;kBAAAgL,QAAA,gBACEhL,OAAA;oBAAAgL,QAAA,eAAIhL,OAAA;sBAAAgL,QAAA,EAAQ;oBAAiB;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC3CrL,OAAA;oBAAAgL,QAAA,EAAI;kBAAgB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACzBrL,OAAA;oBAAAgL,QAAA,eAAIhL,OAAA;sBAAMiL,SAAS,EAAC,kBAAkB;sBAAAD,QAAA,EAAC;oBAAY;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC/DrL,OAAA;oBAAAgL,QAAA,EAAI;kBAAY;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACrBrL,OAAA;oBAAAgL,QAAA,eACEhL,OAAA,CAACX,MAAM;sBAACgJ,IAAI,EAAC,IAAI;sBAACkD,OAAO,EAAC,cAAc;sBAAAP,QAAA,EAAC;oBAAE;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACjL,EAAA,CAx3BID,YAAY;AAAAiO,EAAA,GAAZjO,YAAY;AA03BlB,eAAeA,YAAY;AAAC,IAAAiO,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}