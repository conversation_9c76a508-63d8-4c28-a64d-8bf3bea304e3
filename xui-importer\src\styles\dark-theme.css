/* Tema Oscuro para XUI Importer - Forzado */

/* Variables de colores - Mejorado contraste */
:root {
  --bg-primary: #1a1a1a;
  --bg-secondary: #2d2d2d;
  --bg-tertiary: #3a3a3a;
  --text-primary: #ffffff;
  --text-secondary: #e0e0e0;
  --text-muted: #b0b0b0;
  --border-color: #444444;
  --accent-red: #dc3545;
  --accent-red-hover: #c82333;
  --accent-red-light: rgba(220, 53, 69, 0.1);
  --success-color: #28a745;
  --warning-color: #ffc107;
  --info-color: #17a2b8;
}

/* Fondo principal */
html {
  background-color: var(--bg-primary) !important;
  color: var(--text-primary) !important;
}

body {
  background-color: var(--bg-primary) !important;
  color: var(--text-primary) !important;
}

#root {
  background-color: var(--bg-primary) !important;
  color: var(--text-primary) !important;
  min-height: 100vh !important;
}

/* Contenedores principales */
.container, .container-fluid {
  background-color: var(--bg-primary) !important;
}

/* Cards y paneles */
.card {
  background-color: var(--bg-secondary) !important;
  border: 1px solid var(--border-color) !important;
  color: var(--text-primary) !important;
}

.card-header {
  background-color: var(--bg-tertiary) !important;
  border-bottom: 1px solid var(--border-color) !important;
  color: var(--text-primary) !important;
}

.card-body {
  background-color: var(--bg-secondary) !important;
}

/* Formularios */
.form-control {
  background-color: var(--bg-tertiary) !important;
  border: 1px solid var(--border-color) !important;
  color: var(--text-primary) !important;
}

.form-control:focus {
  background-color: var(--bg-tertiary) !important;
  border-color: var(--accent-red) !important;
  box-shadow: 0 0 0 0.2rem var(--accent-red-light) !important;
  color: var(--text-primary) !important;
}

.form-control::placeholder {
  color: var(--text-muted) !important;
}

.form-label {
  color: var(--text-primary) !important;
  font-weight: 500;
}

.form-text {
  color: var(--text-secondary) !important;
}

/* Botones */
.btn-primary {
  background-color: var(--accent-red) !important;
  border-color: var(--accent-red) !important;
  color: white !important;
}

.btn-primary:hover {
  background-color: var(--accent-red-hover) !important;
  border-color: var(--accent-red-hover) !important;
}

.btn-primary:focus {
  box-shadow: 0 0 0 0.2rem var(--accent-red-light) !important;
}

.btn-outline-primary {
  color: var(--accent-red) !important;
  border-color: var(--accent-red) !important;
}

.btn-outline-primary:hover {
  background-color: var(--accent-red) !important;
  border-color: var(--accent-red) !important;
  color: white !important;
}

.btn-secondary {
  background-color: var(--bg-tertiary) !important;
  border-color: var(--border-color) !important;
  color: var(--text-primary) !important;
}

.btn-secondary:hover {
  background-color: var(--border-color) !important;
  border-color: var(--border-color) !important;
}

/* Checkboxes y radios */
.form-check-input {
  background-color: var(--bg-tertiary) !important;
  border: 1px solid var(--border-color) !important;
}

.form-check-input:checked {
  background-color: var(--accent-red) !important;
  border-color: var(--accent-red) !important;
}

.form-check-input:focus {
  border-color: var(--accent-red) !important;
  box-shadow: 0 0 0 0.25rem var(--accent-red-light) !important;
}

.form-check-label {
  color: var(--text-primary) !important;
}

/* Alertas */
.alert {
  border: 1px solid var(--border-color) !important;
}

.alert-success {
  background-color: rgba(40, 167, 69, 0.1) !important;
  border-color: var(--success-color) !important;
  color: var(--success-color) !important;
}

.alert-danger {
  background-color: var(--accent-red-light) !important;
  border-color: var(--accent-red) !important;
  color: var(--accent-red) !important;
}

.alert-warning {
  background-color: rgba(255, 193, 7, 0.1) !important;
  border-color: var(--warning-color) !important;
  color: var(--warning-color) !important;
}

.alert-info {
  background-color: rgba(23, 162, 184, 0.1) !important;
  border-color: var(--info-color) !important;
  color: var(--info-color) !important;
}

/* Texto */
.text-muted {
  color: var(--text-muted) !important;
}

.text-secondary {
  color: var(--text-secondary) !important;
}

.text-success {
  color: var(--success-color) !important;
}

.text-danger {
  color: var(--accent-red) !important;
}

.text-warning {
  color: var(--warning-color) !important;
}

.text-info {
  color: var(--info-color) !important;
}

/* Bordes y divisores */
.border {
  border-color: var(--border-color) !important;
}

hr {
  border-color: var(--border-color) !important;
}

/* Scrollbars personalizados */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--text-muted);
}

/* Contenedores con scroll */
div[style*="overflow"] {
  scrollbar-width: thin;
  scrollbar-color: var(--border-color) var(--bg-secondary);
}

/* Badges */
.badge {
  color: white !important;
}

.badge-primary {
  background-color: var(--accent-red) !important;
}

.badge-secondary {
  background-color: var(--bg-tertiary) !important;
}

.badge-success {
  background-color: var(--success-color) !important;
}

/* Tablas */
.table {
  color: var(--text-primary) !important;
}

.table-dark {
  background-color: var(--bg-secondary) !important;
}

.table-dark th,
.table-dark td {
  border-color: var(--border-color) !important;
}

/* Modales */
.modal-content {
  background-color: var(--bg-secondary) !important;
  border: 1px solid var(--border-color) !important;
}

.modal-header {
  border-bottom: 1px solid var(--border-color) !important;
}

.modal-footer {
  border-top: 1px solid var(--border-color) !important;
}

/* Navegación */
.nav-tabs {
  border-bottom: 1px solid var(--border-color) !important;
}

.nav-tabs .nav-link {
  color: var(--text-secondary) !important;
  border: 1px solid transparent !important;
}

.nav-tabs .nav-link:hover {
  border-color: var(--border-color) !important;
  color: var(--text-primary) !important;
}

.nav-tabs .nav-link.active {
  background-color: var(--bg-secondary) !important;
  border-color: var(--border-color) var(--border-color) var(--bg-secondary) !important;
  color: var(--accent-red) !important;
}

/* Dropdown */
.dropdown-menu {
  background-color: var(--bg-secondary) !important;
  border: 1px solid var(--border-color) !important;
}

.dropdown-item {
  color: var(--text-primary) !important;
}

.dropdown-item:hover {
  background-color: var(--bg-tertiary) !important;
  color: var(--text-primary) !important;
}

/* Progress bars */
.progress {
  background-color: var(--bg-tertiary) !important;
}

.progress-bar {
  background-color: var(--accent-red) !important;
}

/* Spinner */
.spinner-border {
  color: var(--accent-red) !important;
}

/* Links */
a {
  color: var(--accent-red) !important;
}

a:hover {
  color: var(--accent-red-hover) !important;
}

/* Tablas específicas */
.table-striped > tbody > tr:nth-of-type(odd) > td {
  background-color: rgba(255, 255, 255, 0.05) !important;
}

.table-hover > tbody > tr:hover > td {
  background-color: rgba(220, 53, 69, 0.1) !important;
}

/* Mejoras para elementos específicos */
.shadow-sm {
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.5) !important;
}

.shadow {
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.5) !important;
}

/* Mejoras para el contenido principal */
.main-content {
  background-color: var(--bg-primary) !important;
}

/* Estilos para elementos de formulario específicos */
.form-select {
  background-color: var(--bg-tertiary) !important;
  border: 1px solid var(--border-color) !important;
  color: var(--text-primary) !important;
}

.form-select:focus {
  background-color: var(--bg-tertiary) !important;
  border-color: var(--accent-red) !important;
  box-shadow: 0 0 0 0.2rem var(--accent-red-light) !important;
  color: var(--text-primary) !important;
}

/* Mejoras para badges */
.badge {
  border: 1px solid transparent;
}

.bg-success {
  background-color: var(--success-color) !important;
}

.bg-warning {
  background-color: var(--warning-color) !important;
  color: #000 !important;
}

.bg-info {
  background-color: var(--info-color) !important;
}

.bg-danger {
  background-color: var(--accent-red) !important;
}

/* Mejorar contraste sin hacer todo transparente */
/* Solo elementos contenedores serán transparentes */
.container, .container-fluid, .row, .col, .col-* {
  background-color: transparent !important;
}

/* Elementos de texto mantienen color pero no fondo forzado */
h1, h2, h3, h4, h5, h6, p, span, label {
  color: var(--text-primary) !important;
}

/* Mejorar contraste de texto secundario */
.text-secondary, .text-muted, small {
  color: var(--text-secondary) !important;
}

/* Esta regla ya está arriba, eliminando duplicado */

/* Mejorar contraste en elementos específicos */
.card-text, .card-body p, .card-body span {
  color: var(--text-secondary) !important;
}

/* Títulos más brillantes */
h1, h2, h3, h4, h5, h6 {
  color: var(--text-primary) !important;
  font-weight: 600 !important;
}

/* Labels más visibles */
.form-label, label {
  color: var(--text-primary) !important;
  font-weight: 500 !important;
}

/* Texto de ayuda más visible */
.form-text, .text-help {
  color: var(--text-secondary) !important;
}

/* Corregir fondos blancos específicos de Bootstrap */
.bg-white {
  background-color: var(--bg-secondary) !important;
}

.bg-light {
  background-color: var(--bg-secondary) !important;
}

/* Asegurar que elementos importantes mantengan sus fondos */
.card {
  background-color: var(--bg-secondary) !important;
}

.form-control {
  background-color: var(--bg-tertiary) !important;
}

.btn {
  /* Los botones mantienen sus fondos específicos */
}

.alert {
  /* Las alertas mantienen sus fondos específicos */
}

.badge {
  /* Los badges mantienen sus fondos específicos */
}

.modal-content {
  background-color: var(--bg-secondary) !important;
}

/* Mejorar contraste en tablas */
.table td, .table th {
  color: var(--text-primary) !important;
  border-color: var(--border-color) !important;
}

/* Eliminar fondos blancos en elementos de lista */
.list-group-item {
  background-color: var(--bg-secondary) !important;
  color: var(--text-primary) !important;
  border-color: var(--border-color) !important;
}

/* Mejorar contraste en elementos de navegación */
.nav-link {
  color: var(--text-secondary) !important;
}

.nav-link:hover, .nav-link.active {
  color: var(--accent-red) !important;
}

/* Eliminar fondos blancos específicos de Bootstrap */
.bg-transparent {
  background-color: transparent !important;
}

/* Mejorar contraste en elementos específicos del dashboard */
.card-title {
  color: var(--text-primary) !important;
  font-weight: 600 !important;
}

.card-subtitle {
  color: var(--text-secondary) !important;
}

/* Eliminar fondos blancos en elementos de formulario */
.input-group-text {
  background-color: var(--bg-tertiary) !important;
  color: var(--text-primary) !important;
  border-color: var(--border-color) !important;
}

/* Mejorar contraste en elementos de estado */
.badge {
  font-weight: 500 !important;
}

/* Eliminar fondos blancos en elementos de contenido */
.content-wrapper, .main-content {
  background-color: var(--bg-primary) !important;
}

/* Forzar fondo oscuro en elementos específicos */
body, html, #root, .App {
  background-color: var(--bg-primary) !important;
  color: var(--text-primary) !important;
}

/* Eliminar cualquier fondo blanco residual */
[style*="background-color: white"],
[style*="background-color: #fff"],
[style*="background-color: #ffffff"] {
  background-color: var(--bg-secondary) !important;
}

/* Mejorar contraste en elementos de texto específicos */
strong, b {
  color: var(--text-primary) !important;
  font-weight: 600 !important;
}

/* Eliminar fondos blancos en elementos de Bootstrap específicos */
.modal-backdrop {
  background-color: rgba(0, 0, 0, 0.8) !important;
}

/* Mejorar visibilidad de iconos */
.bi, .fa, .fas, .far, .fab {
  color: inherit !important;
}

/* Eliminar fondos blancos en elementos de grid */
.d-flex, .flex-column, .flex-row {
  background-color: transparent !important;
}

/* Mejorar contraste en elementos de información */
.info-text, .help-text {
  color: var(--text-secondary) !important;
}

/* Regla movida arriba para evitar duplicados */

/* Forzar colores específicos en elementos problemáticos */
.text-dark {
  color: var(--text-primary) !important;
}

.text-light {
  color: var(--text-secondary) !important;
}

/* Mejorar contraste en elementos específicos */
.mb-0, .mb-1, .mb-2, .mb-3, .mb-4, .mb-5 {
  color: inherit !important;
}

/* Eliminar fondos blancos en elementos de spacing */
.p-0, .p-1, .p-2, .p-3, .p-4, .p-5,
.m-0, .m-1, .m-2, .m-3, .m-4, .m-5 {
  background-color: transparent !important;
}

/* Mejorar contraste en elementos de texto específicos */
.fw-bold, .fw-normal, .fw-light {
  color: var(--text-primary) !important;
}

/* Eliminar fondos blancos en elementos de utilidad */
.border, .border-top, .border-bottom, .border-start, .border-end {
  border-color: var(--border-color) !important;
}

/* Forzar tema oscuro en elementos específicos de la aplicación */
.sidebar, .main-content, .content-wrapper {
  background-color: var(--bg-primary) !important;
}

/* Mejorar contraste en elementos de estado específicos */
.text-success {
  color: #4ade80 !important;
}

.text-warning {
  color: #fbbf24 !important;
}

.text-info {
  color: #38bdf8 !important;
}

.text-danger {
  color: var(--accent-red) !important;
}

/* Solo elementos inline de texto sin fondo específico */
em, i, strong, b {
  color: inherit !important;
}

/* Mejorar visibilidad de elementos small */
small {
  color: var(--text-secondary) !important;
  font-weight: 500 !important;
}

/* Mejorar contraste en cards específicas */
.card small {
  color: rgba(255, 255, 255, 0.9) !important;
  font-weight: 500 !important;
}

/* Forzar colores más brillantes en elementos de estado */
.text-success {
  color: #4ade80 !important;
  font-weight: 600 !important;
}

.text-warning {
  color: #fbbf24 !important;
  font-weight: 600 !important;
}

.text-info {
  color: #38bdf8 !important;
  font-weight: 600 !important;
}

.text-danger {
  color: #ef4444 !important;
  font-weight: 600 !important;
}

/* Mejorar contraste en badges */
.badge {
  font-weight: 600 !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

/* Eliminar cualquier opacidad no deseada */
.opacity-75, .opacity-50, .opacity-25 {
  opacity: 1 !important;
}
