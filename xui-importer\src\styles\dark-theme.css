/* Tema Oscuro para XUI Importer - Forzado */

/* Variables de colores */
:root {
  --bg-primary: #1a1a1a;
  --bg-secondary: #2d2d2d;
  --bg-tertiary: #3a3a3a;
  --text-primary: #ffffff;
  --text-secondary: #b0b0b0;
  --text-muted: #888888;
  --border-color: #444444;
  --accent-red: #dc3545;
  --accent-red-hover: #c82333;
  --accent-red-light: rgba(220, 53, 69, 0.1);
  --success-color: #28a745;
  --warning-color: #ffc107;
  --info-color: #17a2b8;
}

/* Fondo principal */
html {
  background-color: var(--bg-primary) !important;
  color: var(--text-primary) !important;
}

body {
  background-color: var(--bg-primary) !important;
  color: var(--text-primary) !important;
}

#root {
  background-color: var(--bg-primary) !important;
  color: var(--text-primary) !important;
  min-height: 100vh !important;
}

/* Contenedores principales */
.container, .container-fluid {
  background-color: var(--bg-primary) !important;
}

/* Cards y paneles */
.card {
  background-color: var(--bg-secondary) !important;
  border: 1px solid var(--border-color) !important;
  color: var(--text-primary) !important;
}

.card-header {
  background-color: var(--bg-tertiary) !important;
  border-bottom: 1px solid var(--border-color) !important;
  color: var(--text-primary) !important;
}

.card-body {
  background-color: var(--bg-secondary) !important;
}

/* Formularios */
.form-control {
  background-color: var(--bg-tertiary) !important;
  border: 1px solid var(--border-color) !important;
  color: var(--text-primary) !important;
}

.form-control:focus {
  background-color: var(--bg-tertiary) !important;
  border-color: var(--accent-red) !important;
  box-shadow: 0 0 0 0.2rem var(--accent-red-light) !important;
  color: var(--text-primary) !important;
}

.form-control::placeholder {
  color: var(--text-muted) !important;
}

.form-label {
  color: var(--text-primary) !important;
  font-weight: 500;
}

.form-text {
  color: var(--text-secondary) !important;
}

/* Botones */
.btn-primary {
  background-color: var(--accent-red) !important;
  border-color: var(--accent-red) !important;
  color: white !important;
}

.btn-primary:hover {
  background-color: var(--accent-red-hover) !important;
  border-color: var(--accent-red-hover) !important;
}

.btn-primary:focus {
  box-shadow: 0 0 0 0.2rem var(--accent-red-light) !important;
}

.btn-outline-primary {
  color: var(--accent-red) !important;
  border-color: var(--accent-red) !important;
}

.btn-outline-primary:hover {
  background-color: var(--accent-red) !important;
  border-color: var(--accent-red) !important;
  color: white !important;
}

.btn-secondary {
  background-color: var(--bg-tertiary) !important;
  border-color: var(--border-color) !important;
  color: var(--text-primary) !important;
}

.btn-secondary:hover {
  background-color: var(--border-color) !important;
  border-color: var(--border-color) !important;
}

/* Checkboxes y radios */
.form-check-input {
  background-color: var(--bg-tertiary) !important;
  border: 1px solid var(--border-color) !important;
}

.form-check-input:checked {
  background-color: var(--accent-red) !important;
  border-color: var(--accent-red) !important;
}

.form-check-input:focus {
  border-color: var(--accent-red) !important;
  box-shadow: 0 0 0 0.25rem var(--accent-red-light) !important;
}

.form-check-label {
  color: var(--text-primary) !important;
}

/* Alertas */
.alert {
  border: 1px solid var(--border-color) !important;
}

.alert-success {
  background-color: rgba(40, 167, 69, 0.1) !important;
  border-color: var(--success-color) !important;
  color: var(--success-color) !important;
}

.alert-danger {
  background-color: var(--accent-red-light) !important;
  border-color: var(--accent-red) !important;
  color: var(--accent-red) !important;
}

.alert-warning {
  background-color: rgba(255, 193, 7, 0.1) !important;
  border-color: var(--warning-color) !important;
  color: var(--warning-color) !important;
}

.alert-info {
  background-color: rgba(23, 162, 184, 0.1) !important;
  border-color: var(--info-color) !important;
  color: var(--info-color) !important;
}

/* Texto */
.text-muted {
  color: var(--text-muted) !important;
}

.text-secondary {
  color: var(--text-secondary) !important;
}

.text-success {
  color: var(--success-color) !important;
}

.text-danger {
  color: var(--accent-red) !important;
}

.text-warning {
  color: var(--warning-color) !important;
}

.text-info {
  color: var(--info-color) !important;
}

/* Bordes y divisores */
.border {
  border-color: var(--border-color) !important;
}

hr {
  border-color: var(--border-color) !important;
}

/* Scrollbars personalizados */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--text-muted);
}

/* Contenedores con scroll */
div[style*="overflow"] {
  scrollbar-width: thin;
  scrollbar-color: var(--border-color) var(--bg-secondary);
}

/* Badges */
.badge {
  color: white !important;
}

.badge-primary {
  background-color: var(--accent-red) !important;
}

.badge-secondary {
  background-color: var(--bg-tertiary) !important;
}

.badge-success {
  background-color: var(--success-color) !important;
}

/* Tablas */
.table {
  color: var(--text-primary) !important;
}

.table-dark {
  background-color: var(--bg-secondary) !important;
}

.table-dark th,
.table-dark td {
  border-color: var(--border-color) !important;
}

/* Modales */
.modal-content {
  background-color: var(--bg-secondary) !important;
  border: 1px solid var(--border-color) !important;
}

.modal-header {
  border-bottom: 1px solid var(--border-color) !important;
}

.modal-footer {
  border-top: 1px solid var(--border-color) !important;
}

/* Navegación */
.nav-tabs {
  border-bottom: 1px solid var(--border-color) !important;
}

.nav-tabs .nav-link {
  color: var(--text-secondary) !important;
  border: 1px solid transparent !important;
}

.nav-tabs .nav-link:hover {
  border-color: var(--border-color) !important;
  color: var(--text-primary) !important;
}

.nav-tabs .nav-link.active {
  background-color: var(--bg-secondary) !important;
  border-color: var(--border-color) var(--border-color) var(--bg-secondary) !important;
  color: var(--accent-red) !important;
}

/* Dropdown */
.dropdown-menu {
  background-color: var(--bg-secondary) !important;
  border: 1px solid var(--border-color) !important;
}

.dropdown-item {
  color: var(--text-primary) !important;
}

.dropdown-item:hover {
  background-color: var(--bg-tertiary) !important;
  color: var(--text-primary) !important;
}

/* Progress bars */
.progress {
  background-color: var(--bg-tertiary) !important;
}

.progress-bar {
  background-color: var(--accent-red) !important;
}

/* Spinner */
.spinner-border {
  color: var(--accent-red) !important;
}

/* Links */
a {
  color: var(--accent-red) !important;
}

a:hover {
  color: var(--accent-red-hover) !important;
}

/* Tablas específicas */
.table-striped > tbody > tr:nth-of-type(odd) > td {
  background-color: rgba(255, 255, 255, 0.05) !important;
}

.table-hover > tbody > tr:hover > td {
  background-color: rgba(220, 53, 69, 0.1) !important;
}

/* Mejoras para elementos específicos */
.shadow-sm {
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.5) !important;
}

.shadow {
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.5) !important;
}

/* Mejoras para el contenido principal */
.main-content {
  background-color: var(--bg-primary) !important;
}

/* Estilos para elementos de formulario específicos */
.form-select {
  background-color: var(--bg-tertiary) !important;
  border: 1px solid var(--border-color) !important;
  color: var(--text-primary) !important;
}

.form-select:focus {
  background-color: var(--bg-tertiary) !important;
  border-color: var(--accent-red) !important;
  box-shadow: 0 0 0 0.2rem var(--accent-red-light) !important;
  color: var(--text-primary) !important;
}

/* Mejoras para badges */
.badge {
  border: 1px solid transparent;
}

.bg-success {
  background-color: var(--success-color) !important;
}

.bg-warning {
  background-color: var(--warning-color) !important;
  color: #000 !important;
}

.bg-info {
  background-color: var(--info-color) !important;
}

.bg-danger {
  background-color: var(--accent-red) !important;
}
