{"ast": null, "code": "var _jsxFileName = \"F:\\\\WORKSPACE\\\\XUI IMPORTER\\\\xui-importer\\\\src\\\\components\\\\Connections.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Row, Col, Card, Button, Form, Alert, Table, Modal, Badge, Spinner } from 'react-bootstrap';\nimport { useApp } from '../context/AppContext';\nimport DataReadingProgress from './DataReadingProgress';\nimport DataReadingModal from './DataReadingModal';\nimport DatabaseOptimization from './DatabaseOptimization';\nimport ConnectivityDiagnostic from './ConnectivityDiagnostic';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Connections = () => {\n  _s();\n  var _state$dataReading2, _state$dataReading3, _state$dataReading4;\n  const {\n    state,\n    connectToDatabase,\n    disconnectFromDatabase,\n    setError\n  } = useApp();\n  const [showModal, setShowModal] = useState(false);\n  const [showDataReadingModal, setShowDataReadingModal] = useState(false);\n  const [testingConnection, setTestingConnection] = useState(false);\n  const [connectionStatus, setConnectionStatus] = useState(null);\n  const [connections, setConnections] = useState([]);\n  const [newConnection, setNewConnection] = useState({\n    name: '',\n    host: '',\n    port: '3306',\n    database: 'xui',\n    // Usar 'xui' como base de datos por defecto\n    username: '',\n    password: ''\n  });\n\n  // Cargar conexiones guardadas al montar componente\n  useEffect(() => {\n    loadSavedConnections();\n  }, []);\n\n  // Actualizar estado cuando cambie la conexión global\n  useEffect(() => {\n    updateConnectionStatus();\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [state.databaseConnection]);\n\n  // Mostrar modal de progreso cuando se inicie la lectura de datos\n  useEffect(() => {\n    var _state$dataReading;\n    if ((_state$dataReading = state.dataReading) !== null && _state$dataReading !== void 0 && _state$dataReading.isReading && !showDataReadingModal) {\n      setShowDataReadingModal(true);\n    }\n  }, [(_state$dataReading2 = state.dataReading) === null || _state$dataReading2 === void 0 ? void 0 : _state$dataReading2.isReading, showDataReadingModal]);\n  const loadSavedConnections = () => {\n    try {\n      const savedConnections = localStorage.getItem('xuiConnections');\n      if (savedConnections) {\n        const parsed = JSON.parse(savedConnections);\n        setConnections(parsed);\n      } else {\n        // Mock data inicial si no hay conexiones guardadas\n        setConnections([{\n          id: 1,\n          name: \"Local XUI Server\",\n          host: \"localhost\",\n          port: \"3306\",\n          database: \"xtream_codes\",\n          username: \"root\",\n          status: \"disconnected\",\n          lastTest: null,\n          version: null,\n          isActive: false\n        }]);\n      }\n    } catch (error) {\n      console.error('Error cargando conexiones:', error);\n      setConnections([]);\n    }\n  };\n  const saveConnections = connectionsData => {\n    localStorage.setItem('xuiConnections', JSON.stringify(connectionsData));\n  };\n  const clearAllConnections = () => {\n    console.log('🧹 Limpiando todas las conexiones guardadas...');\n    localStorage.removeItem('xuiConnections');\n    setConnections([]);\n    console.log('✅ Conexiones limpiadas');\n  };\n  const updateConnectionStatus = () => {\n    if (state.databaseConnection.isConnected) {\n      setConnections(prev => prev.map(conn => {\n        var _state$databaseConnec;\n        return {\n          ...conn,\n          status: conn.host === state.databaseConnection.host && conn.database === state.databaseConnection.database ? 'connected' : 'disconnected',\n          isActive: conn.host === state.databaseConnection.host && conn.database === state.databaseConnection.database,\n          lastTest: conn.host === state.databaseConnection.host && conn.database === state.databaseConnection.database ? new Date().toLocaleString() : conn.lastTest,\n          version: conn.host === state.databaseConnection.host && conn.database === state.databaseConnection.database ? (_state$databaseConnec = state.databaseConnection.serverInfo) === null || _state$databaseConnec === void 0 ? void 0 : _state$databaseConnec.version : conn.version\n        };\n      }));\n    } else {\n      setConnections(prev => prev.map(conn => ({\n        ...conn,\n        status: 'disconnected',\n        isActive: false\n      })));\n    }\n  };\n  const handleInputChange = (field, value) => {\n    setNewConnection(prev => ({\n      ...prev,\n      [field]: value\n    }));\n  };\n  const testConnection = async () => {\n    setTestingConnection(true);\n    setConnectionStatus(null);\n    try {\n      if (window.debugLog) {\n        window.debugLog(`🔍 Testing connection to ${newConnection.host}`, 'info');\n      }\n      const result = await connectToDatabase(newConnection);\n      if (result.success) {\n        var _state$databaseConnec2;\n        setConnectionStatus({\n          type: 'success',\n          message: '✅ Conexión exitosa! Base de datos conectada.'\n        });\n\n        // Guardar la conexión si es exitosa\n        const connectionToSave = {\n          id: `conn_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,\n          name: newConnection.name || `${newConnection.host}:${newConnection.port}`,\n          host: newConnection.host,\n          port: newConnection.port,\n          database: newConnection.database,\n          username: newConnection.username,\n          status: 'connected',\n          lastTest: new Date().toLocaleString(),\n          version: ((_state$databaseConnec2 = state.databaseConnection.serverInfo) === null || _state$databaseConnec2 === void 0 ? void 0 : _state$databaseConnec2.version) || 'Unknown',\n          isActive: true\n        };\n\n        // Actualizar lista de conexiones\n        const updatedConnections = [...connections.filter(c => !(c.host === newConnection.host && c.database === newConnection.database)), connectionToSave];\n        setConnections(updatedConnections);\n        saveConnections(updatedConnections);\n\n        // Limpiar formulario\n        setNewConnection({\n          name: '',\n          host: '',\n          port: '3306',\n          database: 'xtream_codes',\n          username: '',\n          password: ''\n        });\n\n        // Cerrar modal después de un momento\n        setTimeout(() => {\n          setShowModal(false);\n        }, 2000);\n      } else {\n        setConnectionStatus({\n          type: 'danger',\n          message: `❌ Error de conexión: ${result.error}`\n        });\n      }\n    } catch (error) {\n      console.error('Error testing connection:', error);\n      setConnectionStatus({\n        type: 'danger',\n        message: `❌ Error: ${error.message}`\n      });\n    } finally {\n      setTestingConnection(false);\n    }\n  };\n  const connectToSavedConnection = async connection => {\n    // Prompt for password since we don't store passwords for security\n    const password = prompt(`Ingrese la contraseña para ${connection.name} (${connection.username}@${connection.host}):`);\n    if (!password) {\n      setError('Se requiere contraseña para conectar');\n      return;\n    }\n    setTestingConnection(true);\n    try {\n      const connectionData = {\n        host: connection.host,\n        port: connection.port,\n        database: connection.database,\n        username: connection.username,\n        password: password\n      };\n      const result = await connectToDatabase(connectionData);\n      if (!result.success) {\n        setError(`Error conectando a ${connection.name}: ${result.error}`);\n      } else {\n        setError(null); // Clear any previous errors\n      }\n    } catch (error) {\n      setError(`Error: ${error.message}`);\n    } finally {\n      setTestingConnection(false);\n    }\n  };\n  const handleDisconnect = async () => {\n    try {\n      await disconnectFromDatabase();\n      updateConnectionStatus();\n    } catch (error) {\n      setError(`Error desconectando: ${error.message}`);\n    }\n  };\n  const deleteConnection = connectionId => {\n    const updatedConnections = connections.filter(c => c.id !== connectionId);\n    setConnections(updatedConnections);\n    saveConnections(updatedConnections);\n  };\n  const handleCloseModal = () => {\n    setShowModal(false);\n    setNewConnection({\n      name: '',\n      host: '',\n      port: '3306',\n      database: 'xtream_codes',\n      username: '',\n      password: ''\n    });\n    setConnectionStatus(null);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      width: '100%',\n      maxWidth: 'none'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"d-flex justify-content-between align-items-center mb-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"text-danger\",\n        children: \"\\uD83D\\uDD17 XUI Server Connections\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 259,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [state.databaseConnection.isConnected && /*#__PURE__*/_jsxDEV(Badge, {\n          bg: \"success\",\n          className: \"me-2\",\n          children: [\"\\u2705 Connected to \", state.databaseConnection.host]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 262,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"danger\",\n          onClick: clearAllConnections,\n          className: \"me-2\",\n          size: \"sm\",\n          children: \"\\uD83E\\uDDF9 Clear All\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 266,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"success\",\n          onClick: () => setShowModal(true),\n          children: \"\\u2795 Add New Connection\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 269,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 260,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 258,\n      columnNumber: 7\n    }, this), state.error && /*#__PURE__*/_jsxDEV(Alert, {\n      variant: \"danger\",\n      dismissible: true,\n      onClose: () => setError(null),\n      children: state.error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 276,\n      columnNumber: 9\n    }, this), (((_state$dataReading3 = state.dataReading) === null || _state$dataReading3 === void 0 ? void 0 : _state$dataReading3.isReading) || ((_state$dataReading4 = state.dataReading) === null || _state$dataReading4 === void 0 ? void 0 : _state$dataReading4.result)) && /*#__PURE__*/_jsxDEV(Row, {\n      className: \"mb-4\",\n      children: /*#__PURE__*/_jsxDEV(Col, {\n        children: /*#__PURE__*/_jsxDEV(DataReadingProgress, {\n          showCard: true,\n          showControls: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 285,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 284,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 283,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      className: \"mb-4\",\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        lg: 8,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"shadow-sm\",\n          children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n            className: \"bg-danger text-white d-flex justify-content-between align-items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n              className: \"mb-0\",\n              children: \"\\uD83D\\uDDA5\\uFE0F Active XUI Servers\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 297,\n              columnNumber: 15\n            }, this), state.isLoading && /*#__PURE__*/_jsxDEV(Spinner, {\n              animation: \"border\",\n              size: \"sm\",\n              variant: \"light\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 299,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 296,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n            children: /*#__PURE__*/_jsxDEV(Table, {\n              striped: true,\n              hover: true,\n              children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"\\uD83D\\uDCDB Server Name\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 306,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"\\uD83C\\uDF10 Host:Port\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 307,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"\\uD83D\\uDDC4\\uFE0F Database\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 308,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"\\uD83D\\uDCCA Status\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 309,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"\\uD83D\\uDD27 Version\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 310,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"\\u26A1 Actions\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 311,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 305,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 304,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                children: [connections.map(conn => /*#__PURE__*/_jsxDEV(\"tr\", {\n                  className: conn.isActive ? 'table-success' : '',\n                  children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: conn.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 318,\n                      columnNumber: 25\n                    }, this), conn.isActive && /*#__PURE__*/_jsxDEV(Badge, {\n                      bg: \"danger\",\n                      className: \"ms-2\",\n                      children: \"Active\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 319,\n                      columnNumber: 43\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 317,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: [conn.host, \":\", conn.port]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 321,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: conn.database\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 322,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(Badge, {\n                      bg: conn.status === 'connected' ? 'success' : 'danger',\n                      children: conn.status === 'connected' ? '✅ Connected' : '❌ Disconnected'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 324,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 323,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: conn.version || 'Unknown'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 328,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: [conn.status === 'connected' ? /*#__PURE__*/_jsxDEV(Button, {\n                      size: \"sm\",\n                      variant: \"outline-danger\",\n                      className: \"me-1\",\n                      onClick: handleDisconnect,\n                      disabled: testingConnection,\n                      children: \"\\uD83D\\uDD0C Disconnect\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 331,\n                      columnNumber: 27\n                    }, this) : /*#__PURE__*/_jsxDEV(Button, {\n                      size: \"sm\",\n                      variant: \"outline-success\",\n                      className: \"me-1\",\n                      onClick: () => connectToSavedConnection(conn),\n                      disabled: testingConnection,\n                      children: [testingConnection ? '⏳' : '🔌', \" Connect\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 341,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Button, {\n                      size: \"sm\",\n                      variant: \"outline-danger\",\n                      onClick: () => deleteConnection(conn.id),\n                      children: \"\\uD83D\\uDDD1\\uFE0F\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 351,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 329,\n                    columnNumber: 23\n                  }, this)]\n                }, conn.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 316,\n                  columnNumber: 21\n                }, this)), connections.length === 0 && /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: /*#__PURE__*/_jsxDEV(\"td\", {\n                    colSpan: \"6\",\n                    className: \"text-center text-muted\",\n                    children: \"No connections configured. Add a new connection to get started.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 363,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 362,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 314,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 303,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 302,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 295,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 294,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        lg: 4,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"shadow-sm h-100\",\n          children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n            className: \"bg-info text-white\",\n            children: /*#__PURE__*/_jsxDEV(\"h5\", {\n              className: \"mb-0\",\n              children: \"\\uD83D\\uDCA1 XUI Database Info\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 377,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 376,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n            children: [state.databaseConnection.isConnected && state.databaseConnection.serverInfo && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                children: \"\\uD83D\\uDCCA Current Connection:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 382,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                className: \"list-unstyled\",\n                children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Host:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 384,\n                    columnNumber: 25\n                  }, this), \" \", state.databaseConnection.host]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 384,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Database:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 385,\n                    columnNumber: 25\n                  }, this), \" \", state.databaseConnection.database]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 385,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"User:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 386,\n                    columnNumber: 25\n                  }, this), \" \", state.databaseConnection.username]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 386,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Connected:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 387,\n                    columnNumber: 25\n                  }, this), \" \", new Date(state.databaseConnection.lastConnected).toLocaleString()]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 387,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 383,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 389,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 381,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h6\", {\n              children: \"\\uD83C\\uDFAF Target Tables:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 393,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              className: \"list-unstyled\",\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                children: [/*#__PURE__*/_jsxDEV(\"code\", {\n                  children: \"\\uD83D\\uDCFA streams\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 395,\n                  columnNumber: 21\n                }, this), \" - Live channels\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 395,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: [/*#__PURE__*/_jsxDEV(\"code\", {\n                  children: \"\\uD83C\\uDFAC stream_categories\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 396,\n                  columnNumber: 21\n                }, this), \" - Categories\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 396,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: [/*#__PURE__*/_jsxDEV(\"code\", {\n                  children: \"\\uD83D\\uDCDA series\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 397,\n                  columnNumber: 21\n                }, this), \" - TV Series\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 397,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: [/*#__PURE__*/_jsxDEV(\"code\", {\n                  children: \"\\uD83C\\uDF9E\\uFE0F series_episodes\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 398,\n                  columnNumber: 21\n                }, this), \" - Episodes\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 398,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: [/*#__PURE__*/_jsxDEV(\"code\", {\n                  children: \"\\uD83C\\uDFAA bouquets\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 399,\n                  columnNumber: 21\n                }, this), \" - Channel groups\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 399,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: [/*#__PURE__*/_jsxDEV(\"code\", {\n                  children: \"\\uD83D\\uDC65 users\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 400,\n                  columnNumber: 21\n                }, this), \" - User accounts\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 400,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 394,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h6\", {\n              children: \"\\u2699\\uFE0F Required Permissions:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 403,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              className: \"list-unstyled\",\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                children: [\"\\u2705 \", /*#__PURE__*/_jsxDEV(\"code\", {\n                  children: \"SELECT\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 405,\n                  columnNumber: 23\n                }, this), \" - Read existing data\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 405,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: [\"\\u2705 \", /*#__PURE__*/_jsxDEV(\"code\", {\n                  children: \"INSERT\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 406,\n                  columnNumber: 23\n                }, this), \" - Add new content\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 406,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: [\"\\u2705 \", /*#__PURE__*/_jsxDEV(\"code\", {\n                  children: \"UPDATE\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 407,\n                  columnNumber: 23\n                }, this), \" - Modify metadata\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 407,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: [\"\\u2705 \", /*#__PURE__*/_jsxDEV(\"code\", {\n                  children: \"DELETE\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 408,\n                  columnNumber: 23\n                }, this), \" - Remove duplicates\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 408,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 404,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Alert, {\n              variant: \"warning\",\n              className: \"mt-3\",\n              children: /*#__PURE__*/_jsxDEV(\"small\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"\\u26A0\\uFE0F Important:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 413,\n                  columnNumber: 19\n                }, this), \" Always backup your XUI database before importing large M3U files.\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 412,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 411,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 379,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 375,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 374,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 293,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      className: \"mb-4\",\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        lg: 6,\n        children: /*#__PURE__*/_jsxDEV(ConnectivityDiagnostic, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 424,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 423,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        lg: 6,\n        children: state.databaseConnection.isConnected && /*#__PURE__*/_jsxDEV(DatabaseOptimization, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 429,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 426,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 422,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      show: showModal,\n      onHide: handleCloseModal,\n      size: \"lg\",\n      children: [/*#__PURE__*/_jsxDEV(Modal.Header, {\n        closeButton: true,\n        children: /*#__PURE__*/_jsxDEV(Modal.Title, {\n          children: \"\\uD83D\\uDD17 Add New XUI Server Connection\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 437,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 436,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Body, {\n        children: [connectionStatus && /*#__PURE__*/_jsxDEV(Alert, {\n          variant: connectionStatus.type,\n          dismissible: true,\n          onClose: () => setConnectionStatus(null),\n          children: connectionStatus.message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 441,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Form, {\n          children: [/*#__PURE__*/_jsxDEV(Row, {\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"\\uD83D\\uDCDB Connection Name\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 450,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  type: \"text\",\n                  placeholder: \"e.g., Main Production Server\",\n                  value: newConnection.name,\n                  onChange: e => handleInputChange('name', e.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 451,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 449,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 448,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"\\uD83D\\uDDC4\\uFE0F Database Name\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 461,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  type: \"text\",\n                  value: newConnection.database,\n                  onChange: e => handleInputChange('database', e.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 462,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 460,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 459,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 447,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Row, {\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              md: 8,\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"\\uD83C\\uDF10 Host/IP Address\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 474,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  type: \"text\",\n                  placeholder: \"************* or xuiserver.com\",\n                  value: newConnection.host,\n                  onChange: e => handleInputChange('host', e.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 475,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 473,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 472,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              md: 4,\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"\\uD83D\\uDD0C Port\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 485,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  type: \"text\",\n                  value: newConnection.port,\n                  onChange: e => handleInputChange('port', e.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 486,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 484,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 483,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 471,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Row, {\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"\\uD83D\\uDC64 Username\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 498,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  type: \"text\",\n                  placeholder: \"Database username\",\n                  value: newConnection.username,\n                  onChange: e => handleInputChange('username', e.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 499,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 497,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 496,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"\\uD83D\\uDD10 Password\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 509,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  type: \"password\",\n                  placeholder: \"Database password\",\n                  value: newConnection.password,\n                  onChange: e => handleInputChange('password', e.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 510,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 508,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 507,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 495,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Alert, {\n            variant: \"info\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\uD83D\\uDCA1 Tip:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 521,\n              columnNumber: 15\n            }, this), \" Make sure your XUI server allows remote MySQL connections and the user has the necessary permissions.\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 520,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 446,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 439,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Footer, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"secondary\",\n          onClick: handleCloseModal,\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 526,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"warning\",\n          onClick: testConnection,\n          disabled: testingConnection || !newConnection.host || !newConnection.username,\n          children: testingConnection ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(Spinner, {\n              animation: \"border\",\n              size: \"sm\",\n              className: \"me-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 536,\n              columnNumber: 17\n            }, this), \"Testing...\"]\n          }, void 0, true) : '🧪 Test Connection'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 529,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 525,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 435,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DataReadingModal, {\n      show: showDataReadingModal,\n      onHide: () => setShowDataReadingModal(false)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 547,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 257,\n    columnNumber: 5\n  }, this);\n};\n_s(Connections, \"dR6mxB8nOogwlzytlnpFFy/dEA0=\", false, function () {\n  return [useApp];\n});\n_c = Connections;\nexport default Connections;\nvar _c;\n$RefreshReg$(_c, \"Connections\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Row", "Col", "Card", "<PERSON><PERSON>", "Form", "<PERSON><PERSON>", "Table", "Modal", "Badge", "Spinner", "useApp", "DataReadingProgress", "DataReadingModal", "DatabaseOptimization", "ConnectivityDiagnostic", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Connections", "_s", "_state$dataReading2", "_state$dataReading3", "_state$dataReading4", "state", "connectToDatabase", "disconnectFromDatabase", "setError", "showModal", "setShowModal", "showDataReadingModal", "setShowDataReadingModal", "testingConnection", "setTestingConnection", "connectionStatus", "setConnectionStatus", "connections", "setConnections", "newConnection", "setNewConnection", "name", "host", "port", "database", "username", "password", "loadSavedConnections", "updateConnectionStatus", "databaseConnection", "_state$dataReading", "dataReading", "isReading", "savedConnections", "localStorage", "getItem", "parsed", "JSON", "parse", "id", "status", "lastTest", "version", "isActive", "error", "console", "saveConnections", "connectionsData", "setItem", "stringify", "clearAllConnections", "log", "removeItem", "isConnected", "prev", "map", "conn", "_state$databaseConnec", "Date", "toLocaleString", "serverInfo", "handleInputChange", "field", "value", "testConnection", "window", "debugLog", "result", "success", "_state$databaseConnec2", "type", "message", "connectionToSave", "now", "Math", "random", "toString", "substring", "updatedConnections", "filter", "c", "setTimeout", "connectToSavedConnection", "connection", "prompt", "connectionData", "handleDisconnect", "deleteConnection", "connectionId", "handleCloseModal", "style", "width", "max<PERSON><PERSON><PERSON>", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "bg", "variant", "onClick", "size", "dismissible", "onClose", "showCard", "showControls", "lg", "Header", "isLoading", "animation", "Body", "striped", "hover", "disabled", "length", "colSpan", "lastConnected", "show", "onHide", "closeButton", "Title", "md", "Group", "Label", "Control", "placeholder", "onChange", "e", "target", "Footer", "_c", "$RefreshReg$"], "sources": ["F:/WORKSPACE/XUI IMPORTER/xui-importer/src/components/Connections.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { Row, Col, Card, Button, Form, Alert, Table, Modal, Badge, Spinner } from 'react-bootstrap';\r\nimport { useApp } from '../context/AppContext';\r\nimport DataReadingProgress from './DataReadingProgress';\r\nimport DataReadingModal from './DataReadingModal';\r\nimport DatabaseOptimization from './DatabaseOptimization';\r\nimport ConnectivityDiagnostic from './ConnectivityDiagnostic';\r\n\r\nconst Connections = () => {\r\n  const { \r\n    state, \r\n    connectToDatabase, \r\n    disconnectFromDatabase, \r\n    setError\r\n  } = useApp();\r\n\r\n  const [showModal, setShowModal] = useState(false);\r\n  const [showDataReadingModal, setShowDataReadingModal] = useState(false);\r\n  const [testingConnection, setTestingConnection] = useState(false);\r\n  const [connectionStatus, setConnectionStatus] = useState(null);\r\n  const [connections, setConnections] = useState([]);\r\n\r\n  const [newConnection, setNewConnection] = useState({\r\n    name: '',\r\n    host: '',\r\n    port: '3306',\r\n    database: 'xui', // Usar 'xui' como base de datos por defecto\r\n    username: '',\r\n    password: ''\r\n  });\r\n\r\n  // Cargar conexiones guardadas al montar componente\r\n  useEffect(() => {\r\n    loadSavedConnections();\r\n  }, []);\r\n\r\n  // Actualizar estado cuando cambie la conexión global\r\n  useEffect(() => {\r\n    updateConnectionStatus();\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, [state.databaseConnection]);\r\n\r\n  // Mostrar modal de progreso cuando se inicie la lectura de datos\r\n  useEffect(() => {\r\n    if (state.dataReading?.isReading && !showDataReadingModal) {\r\n      setShowDataReadingModal(true);\r\n    }\r\n  }, [state.dataReading?.isReading, showDataReadingModal]);\r\n\r\n  const loadSavedConnections = () => {\r\n    try {\r\n      const savedConnections = localStorage.getItem('xuiConnections');\r\n      if (savedConnections) {\r\n        const parsed = JSON.parse(savedConnections);\r\n        setConnections(parsed);\r\n      } else {\r\n        // Mock data inicial si no hay conexiones guardadas\r\n        setConnections([\r\n          {\r\n            id: 1,\r\n            name: \"Local XUI Server\",\r\n            host: \"localhost\",\r\n            port: \"3306\",\r\n            database: \"xtream_codes\",\r\n            username: \"root\",\r\n            status: \"disconnected\",\r\n            lastTest: null,\r\n            version: null,\r\n            isActive: false\r\n          }\r\n        ]);\r\n      }\r\n    } catch (error) {\r\n      console.error('Error cargando conexiones:', error);\r\n      setConnections([]);\r\n    }\r\n  };\r\n\r\n  const saveConnections = (connectionsData) => {\r\n    localStorage.setItem('xuiConnections', JSON.stringify(connectionsData));\r\n  };\r\n\r\n  const clearAllConnections = () => {\r\n    console.log('🧹 Limpiando todas las conexiones guardadas...');\r\n    localStorage.removeItem('xuiConnections');\r\n    setConnections([]);\r\n    console.log('✅ Conexiones limpiadas');\r\n  };\r\n\r\n  const updateConnectionStatus = () => {\r\n    if (state.databaseConnection.isConnected) {\r\n      setConnections(prev => prev.map(conn => ({\r\n        ...conn,\r\n        status: conn.host === state.databaseConnection.host && \r\n                conn.database === state.databaseConnection.database ? 'connected' : 'disconnected',\r\n        isActive: conn.host === state.databaseConnection.host && \r\n                 conn.database === state.databaseConnection.database,\r\n        lastTest: conn.host === state.databaseConnection.host && \r\n                 conn.database === state.databaseConnection.database ? \r\n                 new Date().toLocaleString() : conn.lastTest,\r\n        version: conn.host === state.databaseConnection.host && \r\n                conn.database === state.databaseConnection.database ? \r\n                state.databaseConnection.serverInfo?.version : conn.version\r\n      })));\r\n    } else {\r\n      setConnections(prev => prev.map(conn => ({\r\n        ...conn,\r\n        status: 'disconnected',\r\n        isActive: false\r\n      })));\r\n    }\r\n  };\r\n\r\n  const handleInputChange = (field, value) => {\r\n    setNewConnection(prev => ({\r\n      ...prev,\r\n      [field]: value\r\n    }));\r\n  };\r\n\r\n  const testConnection = async () => {\r\n    setTestingConnection(true);\r\n    setConnectionStatus(null);\r\n    \r\n    try {\r\n      if (window.debugLog) {\r\n        window.debugLog(`🔍 Testing connection to ${newConnection.host}`, 'info');\r\n      }\r\n\r\n      const result = await connectToDatabase(newConnection);\r\n      \r\n      if (result.success) {\r\n        setConnectionStatus({\r\n          type: 'success',\r\n          message: '✅ Conexión exitosa! Base de datos conectada.'\r\n        });\r\n        \r\n        // Guardar la conexión si es exitosa\r\n        const connectionToSave = {\r\n          id: `conn_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,\r\n          name: newConnection.name || `${newConnection.host}:${newConnection.port}`,\r\n          host: newConnection.host,\r\n          port: newConnection.port,\r\n          database: newConnection.database,\r\n          username: newConnection.username,\r\n          status: 'connected',\r\n          lastTest: new Date().toLocaleString(),\r\n          version: state.databaseConnection.serverInfo?.version || 'Unknown',\r\n          isActive: true\r\n        };\r\n        \r\n        // Actualizar lista de conexiones\r\n        const updatedConnections = [...connections.filter(c => \r\n          !(c.host === newConnection.host && c.database === newConnection.database)\r\n        ), connectionToSave];\r\n        \r\n        setConnections(updatedConnections);\r\n        saveConnections(updatedConnections);\r\n        \r\n        // Limpiar formulario\r\n        setNewConnection({\r\n          name: '',\r\n          host: '',\r\n          port: '3306',\r\n          database: 'xtream_codes',\r\n          username: '',\r\n          password: ''\r\n        });\r\n        \r\n        // Cerrar modal después de un momento\r\n        setTimeout(() => {\r\n          setShowModal(false);\r\n        }, 2000);\r\n        \r\n      } else {\r\n        setConnectionStatus({\r\n          type: 'danger',\r\n          message: `❌ Error de conexión: ${result.error}`\r\n        });\r\n      }\r\n      \r\n    } catch (error) {\r\n      console.error('Error testing connection:', error);\r\n      setConnectionStatus({\r\n        type: 'danger',\r\n        message: `❌ Error: ${error.message}`\r\n      });\r\n    } finally {\r\n      setTestingConnection(false);\r\n    }\r\n  };\r\n\r\n  const connectToSavedConnection = async (connection) => {\r\n    // Prompt for password since we don't store passwords for security\r\n    const password = prompt(`Ingrese la contraseña para ${connection.name} (${connection.username}@${connection.host}):`);\r\n    \r\n    if (!password) {\r\n      setError('Se requiere contraseña para conectar');\r\n      return;\r\n    }\r\n    \r\n    setTestingConnection(true);\r\n    \r\n    try {\r\n      const connectionData = {\r\n        host: connection.host,\r\n        port: connection.port,\r\n        database: connection.database,\r\n        username: connection.username,\r\n        password: password\r\n      };\r\n\r\n      const result = await connectToDatabase(connectionData);\r\n      \r\n      if (!result.success) {\r\n        setError(`Error conectando a ${connection.name}: ${result.error}`);\r\n      } else {\r\n        setError(null); // Clear any previous errors\r\n      }\r\n      \r\n    } catch (error) {\r\n      setError(`Error: ${error.message}`);\r\n    } finally {\r\n      setTestingConnection(false);\r\n    }\r\n  };\r\n\r\n  const handleDisconnect = async () => {\r\n    try {\r\n      await disconnectFromDatabase();\r\n      updateConnectionStatus();\r\n    } catch (error) {\r\n      setError(`Error desconectando: ${error.message}`);\r\n    }\r\n  };\r\n\r\n  const deleteConnection = (connectionId) => {\r\n    const updatedConnections = connections.filter(c => c.id !== connectionId);\r\n    setConnections(updatedConnections);\r\n    saveConnections(updatedConnections);\r\n  };\r\n\r\n  const handleCloseModal = () => {\r\n    setShowModal(false);\r\n    setNewConnection({\r\n      name: '',\r\n      host: '',\r\n      port: '3306',\r\n      database: 'xtream_codes',\r\n      username: '',\r\n      password: ''\r\n    });\r\n    setConnectionStatus(null);\r\n  };\r\n\r\n  return (\r\n    <div style={{ width: '100%', maxWidth: 'none' }}>\r\n      <div className=\"d-flex justify-content-between align-items-center mb-4\">\r\n        <h1 className=\"text-danger\">🔗 XUI Server Connections</h1>\r\n        <div>\r\n          {state.databaseConnection.isConnected && (\r\n            <Badge bg=\"success\" className=\"me-2\">\r\n              ✅ Connected to {state.databaseConnection.host}\r\n            </Badge>\r\n          )}\r\n          <Button variant=\"danger\" onClick={clearAllConnections} className=\"me-2\" size=\"sm\">\r\n            🧹 Clear All\r\n          </Button>\r\n          <Button variant=\"success\" onClick={() => setShowModal(true)}>\r\n            ➕ Add New Connection\r\n          </Button>\r\n        </div>\r\n      </div>\r\n\r\n      {state.error && (\r\n        <Alert variant=\"danger\" dismissible onClose={() => setError(null)}>\r\n          {state.error}\r\n        </Alert>\r\n      )}\r\n\r\n      {/* Mostrar progreso de lectura de datos si está activo */}\r\n      {(state.dataReading?.isReading || state.dataReading?.result) && (\r\n        <Row className=\"mb-4\">\r\n          <Col>\r\n            <DataReadingProgress\r\n              showCard={true}\r\n              showControls={true}\r\n            />\r\n          </Col>\r\n        </Row>\r\n      )}\r\n\r\n      <Row className=\"mb-4\">\r\n        <Col lg={8}>\r\n          <Card className=\"shadow-sm\">\r\n            <Card.Header className=\"bg-danger text-white d-flex justify-content-between align-items-center\">\r\n              <h5 className=\"mb-0\">🖥️ Active XUI Servers</h5>\r\n              {state.isLoading && (\r\n                <Spinner animation=\"border\" size=\"sm\" variant=\"light\" />\r\n              )}\r\n            </Card.Header>\r\n            <Card.Body>\r\n              <Table striped hover>\r\n                <thead>\r\n                  <tr>\r\n                    <th>📛 Server Name</th>\r\n                    <th>🌐 Host:Port</th>\r\n                    <th>🗄️ Database</th>\r\n                    <th>📊 Status</th>\r\n                    <th>🔧 Version</th>\r\n                    <th>⚡ Actions</th>\r\n                  </tr>\r\n                </thead>\r\n                <tbody>\r\n                  {connections.map(conn => (\r\n                    <tr key={conn.id} className={conn.isActive ? 'table-success' : ''}>\r\n                      <td>\r\n                        <strong>{conn.name}</strong>\r\n                        {conn.isActive && <Badge bg=\"danger\" className=\"ms-2\">Active</Badge>}\r\n                      </td>\r\n                      <td>{conn.host}:{conn.port}</td>\r\n                      <td>{conn.database}</td>\r\n                      <td>\r\n                        <Badge bg={conn.status === 'connected' ? 'success' : 'danger'}>\r\n                          {conn.status === 'connected' ? '✅ Connected' : '❌ Disconnected'}\r\n                        </Badge>\r\n                      </td>\r\n                      <td>{conn.version || 'Unknown'}</td>\r\n                      <td>\r\n                        {conn.status === 'connected' ? (\r\n                          <Button \r\n                            size=\"sm\" \r\n                            variant=\"outline-danger\" \r\n                            className=\"me-1\"\r\n                            onClick={handleDisconnect}\r\n                            disabled={testingConnection}\r\n                          >\r\n                            🔌 Disconnect\r\n                          </Button>\r\n                        ) : (\r\n                          <Button \r\n                            size=\"sm\" \r\n                            variant=\"outline-success\" \r\n                            className=\"me-1\"\r\n                            onClick={() => connectToSavedConnection(conn)}\r\n                            disabled={testingConnection}\r\n                          >\r\n                            {testingConnection ? '⏳' : '🔌'} Connect\r\n                          </Button>\r\n                        )}\r\n                        <Button \r\n                          size=\"sm\" \r\n                          variant=\"outline-danger\"\r\n                          onClick={() => deleteConnection(conn.id)}\r\n                        >\r\n                          🗑️\r\n                        </Button>\r\n                      </td>\r\n                    </tr>\r\n                  ))}\r\n                  {connections.length === 0 && (\r\n                    <tr>\r\n                      <td colSpan=\"6\" className=\"text-center text-muted\">\r\n                        No connections configured. Add a new connection to get started.\r\n                      </td>\r\n                    </tr>\r\n                  )}\r\n                </tbody>\r\n              </Table>\r\n            </Card.Body>\r\n          </Card>\r\n        </Col>\r\n\r\n        <Col lg={4}>\r\n          <Card className=\"shadow-sm h-100\">\r\n            <Card.Header className=\"bg-info text-white\">\r\n              <h5 className=\"mb-0\">💡 XUI Database Info</h5>\r\n            </Card.Header>\r\n            <Card.Body>\r\n              {state.databaseConnection.isConnected && state.databaseConnection.serverInfo && (\r\n                <div className=\"mb-3\">\r\n                  <h6>📊 Current Connection:</h6>\r\n                  <ul className=\"list-unstyled\">\r\n                    <li><strong>Host:</strong> {state.databaseConnection.host}</li>\r\n                    <li><strong>Database:</strong> {state.databaseConnection.database}</li>\r\n                    <li><strong>User:</strong> {state.databaseConnection.username}</li>\r\n                    <li><strong>Connected:</strong> {new Date(state.databaseConnection.lastConnected).toLocaleString()}</li>\r\n                  </ul>\r\n                  <hr />\r\n                </div>\r\n              )}\r\n              \r\n              <h6>🎯 Target Tables:</h6>\r\n              <ul className=\"list-unstyled\">\r\n                <li><code>📺 streams</code> - Live channels</li>\r\n                <li><code>🎬 stream_categories</code> - Categories</li>\r\n                <li><code>📚 series</code> - TV Series</li>\r\n                <li><code>🎞️ series_episodes</code> - Episodes</li>\r\n                <li><code>🎪 bouquets</code> - Channel groups</li>\r\n                <li><code>👥 users</code> - User accounts</li>\r\n              </ul>\r\n\r\n              <h6>⚙️ Required Permissions:</h6>\r\n              <ul className=\"list-unstyled\">\r\n                <li>✅ <code>SELECT</code> - Read existing data</li>\r\n                <li>✅ <code>INSERT</code> - Add new content</li>\r\n                <li>✅ <code>UPDATE</code> - Modify metadata</li>\r\n                <li>✅ <code>DELETE</code> - Remove duplicates</li>\r\n              </ul>\r\n\r\n              <Alert variant=\"warning\" className=\"mt-3\">\r\n                <small>\r\n                  <strong>⚠️ Important:</strong> Always backup your XUI database before importing large M3U files.\r\n                </small>\r\n              </Alert>\r\n            </Card.Body>\r\n          </Card>\r\n        </Col>\r\n      </Row>\r\n\r\n      {/* Componente de diagnóstico de conectividad */}\r\n      <Row className=\"mb-4\">\r\n        <Col lg={6}>\r\n          <ConnectivityDiagnostic />\r\n        </Col>\r\n        <Col lg={6}>\r\n          {/* Componente de optimización de base de datos */}\r\n          {state.databaseConnection.isConnected && (\r\n            <DatabaseOptimization />\r\n          )}\r\n        </Col>\r\n      </Row>\r\n\r\n      {/* Modal para nueva conexión */}\r\n      <Modal show={showModal} onHide={handleCloseModal} size=\"lg\">\r\n        <Modal.Header closeButton>\r\n          <Modal.Title>🔗 Add New XUI Server Connection</Modal.Title>\r\n        </Modal.Header>\r\n        <Modal.Body>\r\n          {connectionStatus && (\r\n            <Alert variant={connectionStatus.type} dismissible onClose={() => setConnectionStatus(null)}>\r\n              {connectionStatus.message}\r\n            </Alert>\r\n          )}\r\n\r\n          <Form>\r\n            <Row>\r\n              <Col md={6}>\r\n                <Form.Group className=\"mb-3\">\r\n                  <Form.Label>📛 Connection Name</Form.Label>\r\n                  <Form.Control\r\n                    type=\"text\"\r\n                    placeholder=\"e.g., Main Production Server\"\r\n                    value={newConnection.name}\r\n                    onChange={(e) => handleInputChange('name', e.target.value)}\r\n                  />\r\n                </Form.Group>\r\n              </Col>\r\n              <Col md={6}>\r\n                <Form.Group className=\"mb-3\">\r\n                  <Form.Label>🗄️ Database Name</Form.Label>\r\n                  <Form.Control\r\n                    type=\"text\"\r\n                    value={newConnection.database}\r\n                    onChange={(e) => handleInputChange('database', e.target.value)}\r\n                  />\r\n                </Form.Group>\r\n              </Col>\r\n            </Row>\r\n\r\n            <Row>\r\n              <Col md={8}>\r\n                <Form.Group className=\"mb-3\">\r\n                  <Form.Label>🌐 Host/IP Address</Form.Label>\r\n                  <Form.Control\r\n                    type=\"text\"\r\n                    placeholder=\"************* or xuiserver.com\"\r\n                    value={newConnection.host}\r\n                    onChange={(e) => handleInputChange('host', e.target.value)}\r\n                  />\r\n                </Form.Group>\r\n              </Col>\r\n              <Col md={4}>\r\n                <Form.Group className=\"mb-3\">\r\n                  <Form.Label>🔌 Port</Form.Label>\r\n                  <Form.Control\r\n                    type=\"text\"\r\n                    value={newConnection.port}\r\n                    onChange={(e) => handleInputChange('port', e.target.value)}\r\n                  />\r\n                </Form.Group>\r\n              </Col>\r\n            </Row>\r\n\r\n            <Row>\r\n              <Col md={6}>\r\n                <Form.Group className=\"mb-3\">\r\n                  <Form.Label>👤 Username</Form.Label>\r\n                  <Form.Control\r\n                    type=\"text\"\r\n                    placeholder=\"Database username\"\r\n                    value={newConnection.username}\r\n                    onChange={(e) => handleInputChange('username', e.target.value)}\r\n                  />\r\n                </Form.Group>\r\n              </Col>\r\n              <Col md={6}>\r\n                <Form.Group className=\"mb-3\">\r\n                  <Form.Label>🔐 Password</Form.Label>\r\n                  <Form.Control\r\n                    type=\"password\"\r\n                    placeholder=\"Database password\"\r\n                    value={newConnection.password}\r\n                    onChange={(e) => handleInputChange('password', e.target.value)}\r\n                  />\r\n                </Form.Group>\r\n              </Col>\r\n            </Row>\r\n\r\n            <Alert variant=\"info\">\r\n              <strong>💡 Tip:</strong> Make sure your XUI server allows remote MySQL connections and the user has the necessary permissions.\r\n            </Alert>\r\n          </Form>\r\n        </Modal.Body>\r\n        <Modal.Footer>\r\n          <Button variant=\"secondary\" onClick={handleCloseModal}>\r\n            Cancel\r\n          </Button>\r\n          <Button \r\n            variant=\"warning\" \r\n            onClick={testConnection}\r\n            disabled={testingConnection || !newConnection.host || !newConnection.username}\r\n          >\r\n            {testingConnection ? (\r\n              <>\r\n                <Spinner animation=\"border\" size=\"sm\" className=\"me-2\" />\r\n                Testing...\r\n              </>\r\n            ) : (\r\n              '🧪 Test Connection'\r\n            )}\r\n          </Button>\r\n        </Modal.Footer>\r\n      </Modal>\r\n\r\n      {/* Modal de progreso de lectura de datos */}\r\n      <DataReadingModal\r\n        show={showDataReadingModal}\r\n        onHide={() => setShowDataReadingModal(false)}\r\n      />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Connections;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAEC,MAAM,EAAEC,IAAI,EAAEC,KAAK,EAAEC,KAAK,EAAEC,KAAK,EAAEC,KAAK,EAAEC,OAAO,QAAQ,iBAAiB;AACnG,SAASC,MAAM,QAAQ,uBAAuB;AAC9C,OAAOC,mBAAmB,MAAM,uBAAuB;AACvD,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,OAAOC,oBAAoB,MAAM,wBAAwB;AACzD,OAAOC,sBAAsB,MAAM,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE9D,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,mBAAA,EAAAC,mBAAA,EAAAC,mBAAA;EACxB,MAAM;IACJC,KAAK;IACLC,iBAAiB;IACjBC,sBAAsB;IACtBC;EACF,CAAC,GAAGjB,MAAM,CAAC,CAAC;EAEZ,MAAM,CAACkB,SAAS,EAAEC,YAAY,CAAC,GAAG/B,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACgC,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGjC,QAAQ,CAAC,KAAK,CAAC;EACvE,MAAM,CAACkC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGnC,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAACoC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGrC,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAACsC,WAAW,EAAEC,cAAc,CAAC,GAAGvC,QAAQ,CAAC,EAAE,CAAC;EAElD,MAAM,CAACwC,aAAa,EAAEC,gBAAgB,CAAC,GAAGzC,QAAQ,CAAC;IACjD0C,IAAI,EAAE,EAAE;IACRC,IAAI,EAAE,EAAE;IACRC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE,KAAK;IAAE;IACjBC,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE;EACZ,CAAC,CAAC;;EAEF;EACA9C,SAAS,CAAC,MAAM;IACd+C,oBAAoB,CAAC,CAAC;EACxB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA/C,SAAS,CAAC,MAAM;IACdgD,sBAAsB,CAAC,CAAC;IACxB;EACF,CAAC,EAAE,CAACvB,KAAK,CAACwB,kBAAkB,CAAC,CAAC;;EAE9B;EACAjD,SAAS,CAAC,MAAM;IAAA,IAAAkD,kBAAA;IACd,IAAI,CAAAA,kBAAA,GAAAzB,KAAK,CAAC0B,WAAW,cAAAD,kBAAA,eAAjBA,kBAAA,CAAmBE,SAAS,IAAI,CAACrB,oBAAoB,EAAE;MACzDC,uBAAuB,CAAC,IAAI,CAAC;IAC/B;EACF,CAAC,EAAE,EAAAV,mBAAA,GAACG,KAAK,CAAC0B,WAAW,cAAA7B,mBAAA,uBAAjBA,mBAAA,CAAmB8B,SAAS,EAAErB,oBAAoB,CAAC,CAAC;EAExD,MAAMgB,oBAAoB,GAAGA,CAAA,KAAM;IACjC,IAAI;MACF,MAAMM,gBAAgB,GAAGC,YAAY,CAACC,OAAO,CAAC,gBAAgB,CAAC;MAC/D,IAAIF,gBAAgB,EAAE;QACpB,MAAMG,MAAM,GAAGC,IAAI,CAACC,KAAK,CAACL,gBAAgB,CAAC;QAC3Cf,cAAc,CAACkB,MAAM,CAAC;MACxB,CAAC,MAAM;QACL;QACAlB,cAAc,CAAC,CACb;UACEqB,EAAE,EAAE,CAAC;UACLlB,IAAI,EAAE,kBAAkB;UACxBC,IAAI,EAAE,WAAW;UACjBC,IAAI,EAAE,MAAM;UACZC,QAAQ,EAAE,cAAc;UACxBC,QAAQ,EAAE,MAAM;UAChBe,MAAM,EAAE,cAAc;UACtBC,QAAQ,EAAE,IAAI;UACdC,OAAO,EAAE,IAAI;UACbC,QAAQ,EAAE;QACZ,CAAC,CACF,CAAC;MACJ;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClD1B,cAAc,CAAC,EAAE,CAAC;IACpB;EACF,CAAC;EAED,MAAM4B,eAAe,GAAIC,eAAe,IAAK;IAC3Cb,YAAY,CAACc,OAAO,CAAC,gBAAgB,EAAEX,IAAI,CAACY,SAAS,CAACF,eAAe,CAAC,CAAC;EACzE,CAAC;EAED,MAAMG,mBAAmB,GAAGA,CAAA,KAAM;IAChCL,OAAO,CAACM,GAAG,CAAC,gDAAgD,CAAC;IAC7DjB,YAAY,CAACkB,UAAU,CAAC,gBAAgB,CAAC;IACzClC,cAAc,CAAC,EAAE,CAAC;IAClB2B,OAAO,CAACM,GAAG,CAAC,wBAAwB,CAAC;EACvC,CAAC;EAED,MAAMvB,sBAAsB,GAAGA,CAAA,KAAM;IACnC,IAAIvB,KAAK,CAACwB,kBAAkB,CAACwB,WAAW,EAAE;MACxCnC,cAAc,CAACoC,IAAI,IAAIA,IAAI,CAACC,GAAG,CAACC,IAAI;QAAA,IAAAC,qBAAA;QAAA,OAAK;UACvC,GAAGD,IAAI;UACPhB,MAAM,EAAEgB,IAAI,CAAClC,IAAI,KAAKjB,KAAK,CAACwB,kBAAkB,CAACP,IAAI,IAC3CkC,IAAI,CAAChC,QAAQ,KAAKnB,KAAK,CAACwB,kBAAkB,CAACL,QAAQ,GAAG,WAAW,GAAG,cAAc;UAC1FmB,QAAQ,EAAEa,IAAI,CAAClC,IAAI,KAAKjB,KAAK,CAACwB,kBAAkB,CAACP,IAAI,IAC5CkC,IAAI,CAAChC,QAAQ,KAAKnB,KAAK,CAACwB,kBAAkB,CAACL,QAAQ;UAC5DiB,QAAQ,EAAEe,IAAI,CAAClC,IAAI,KAAKjB,KAAK,CAACwB,kBAAkB,CAACP,IAAI,IAC5CkC,IAAI,CAAChC,QAAQ,KAAKnB,KAAK,CAACwB,kBAAkB,CAACL,QAAQ,GACnD,IAAIkC,IAAI,CAAC,CAAC,CAACC,cAAc,CAAC,CAAC,GAAGH,IAAI,CAACf,QAAQ;UACpDC,OAAO,EAAEc,IAAI,CAAClC,IAAI,KAAKjB,KAAK,CAACwB,kBAAkB,CAACP,IAAI,IAC5CkC,IAAI,CAAChC,QAAQ,KAAKnB,KAAK,CAACwB,kBAAkB,CAACL,QAAQ,IAAAiC,qBAAA,GACnDpD,KAAK,CAACwB,kBAAkB,CAAC+B,UAAU,cAAAH,qBAAA,uBAAnCA,qBAAA,CAAqCf,OAAO,GAAGc,IAAI,CAACd;QAC9D,CAAC;MAAA,CAAC,CAAC,CAAC;IACN,CAAC,MAAM;MACLxB,cAAc,CAACoC,IAAI,IAAIA,IAAI,CAACC,GAAG,CAACC,IAAI,KAAK;QACvC,GAAGA,IAAI;QACPhB,MAAM,EAAE,cAAc;QACtBG,QAAQ,EAAE;MACZ,CAAC,CAAC,CAAC,CAAC;IACN;EACF,CAAC;EAED,MAAMkB,iBAAiB,GAAGA,CAACC,KAAK,EAAEC,KAAK,KAAK;IAC1C3C,gBAAgB,CAACkC,IAAI,KAAK;MACxB,GAAGA,IAAI;MACP,CAACQ,KAAK,GAAGC;IACX,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMC,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjClD,oBAAoB,CAAC,IAAI,CAAC;IAC1BE,mBAAmB,CAAC,IAAI,CAAC;IAEzB,IAAI;MACF,IAAIiD,MAAM,CAACC,QAAQ,EAAE;QACnBD,MAAM,CAACC,QAAQ,CAAC,4BAA4B/C,aAAa,CAACG,IAAI,EAAE,EAAE,MAAM,CAAC;MAC3E;MAEA,MAAM6C,MAAM,GAAG,MAAM7D,iBAAiB,CAACa,aAAa,CAAC;MAErD,IAAIgD,MAAM,CAACC,OAAO,EAAE;QAAA,IAAAC,sBAAA;QAClBrD,mBAAmB,CAAC;UAClBsD,IAAI,EAAE,SAAS;UACfC,OAAO,EAAE;QACX,CAAC,CAAC;;QAEF;QACA,MAAMC,gBAAgB,GAAG;UACvBjC,EAAE,EAAE,QAAQmB,IAAI,CAACe,GAAG,CAAC,CAAC,IAAIC,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE;UACvExD,IAAI,EAAEF,aAAa,CAACE,IAAI,IAAI,GAAGF,aAAa,CAACG,IAAI,IAAIH,aAAa,CAACI,IAAI,EAAE;UACzED,IAAI,EAAEH,aAAa,CAACG,IAAI;UACxBC,IAAI,EAAEJ,aAAa,CAACI,IAAI;UACxBC,QAAQ,EAAEL,aAAa,CAACK,QAAQ;UAChCC,QAAQ,EAAEN,aAAa,CAACM,QAAQ;UAChCe,MAAM,EAAE,WAAW;UACnBC,QAAQ,EAAE,IAAIiB,IAAI,CAAC,CAAC,CAACC,cAAc,CAAC,CAAC;UACrCjB,OAAO,EAAE,EAAA2B,sBAAA,GAAAhE,KAAK,CAACwB,kBAAkB,CAAC+B,UAAU,cAAAS,sBAAA,uBAAnCA,sBAAA,CAAqC3B,OAAO,KAAI,SAAS;UAClEC,QAAQ,EAAE;QACZ,CAAC;;QAED;QACA,MAAMmC,kBAAkB,GAAG,CAAC,GAAG7D,WAAW,CAAC8D,MAAM,CAACC,CAAC,IACjD,EAAEA,CAAC,CAAC1D,IAAI,KAAKH,aAAa,CAACG,IAAI,IAAI0D,CAAC,CAACxD,QAAQ,KAAKL,aAAa,CAACK,QAAQ,CAC1E,CAAC,EAAEgD,gBAAgB,CAAC;QAEpBtD,cAAc,CAAC4D,kBAAkB,CAAC;QAClChC,eAAe,CAACgC,kBAAkB,CAAC;;QAEnC;QACA1D,gBAAgB,CAAC;UACfC,IAAI,EAAE,EAAE;UACRC,IAAI,EAAE,EAAE;UACRC,IAAI,EAAE,MAAM;UACZC,QAAQ,EAAE,cAAc;UACxBC,QAAQ,EAAE,EAAE;UACZC,QAAQ,EAAE;QACZ,CAAC,CAAC;;QAEF;QACAuD,UAAU,CAAC,MAAM;UACfvE,YAAY,CAAC,KAAK,CAAC;QACrB,CAAC,EAAE,IAAI,CAAC;MAEV,CAAC,MAAM;QACLM,mBAAmB,CAAC;UAClBsD,IAAI,EAAE,QAAQ;UACdC,OAAO,EAAE,wBAAwBJ,MAAM,CAACvB,KAAK;QAC/C,CAAC,CAAC;MACJ;IAEF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD5B,mBAAmB,CAAC;QAClBsD,IAAI,EAAE,QAAQ;QACdC,OAAO,EAAE,YAAY3B,KAAK,CAAC2B,OAAO;MACpC,CAAC,CAAC;IACJ,CAAC,SAAS;MACRzD,oBAAoB,CAAC,KAAK,CAAC;IAC7B;EACF,CAAC;EAED,MAAMoE,wBAAwB,GAAG,MAAOC,UAAU,IAAK;IACrD;IACA,MAAMzD,QAAQ,GAAG0D,MAAM,CAAC,8BAA8BD,UAAU,CAAC9D,IAAI,KAAK8D,UAAU,CAAC1D,QAAQ,IAAI0D,UAAU,CAAC7D,IAAI,IAAI,CAAC;IAErH,IAAI,CAACI,QAAQ,EAAE;MACblB,QAAQ,CAAC,sCAAsC,CAAC;MAChD;IACF;IAEAM,oBAAoB,CAAC,IAAI,CAAC;IAE1B,IAAI;MACF,MAAMuE,cAAc,GAAG;QACrB/D,IAAI,EAAE6D,UAAU,CAAC7D,IAAI;QACrBC,IAAI,EAAE4D,UAAU,CAAC5D,IAAI;QACrBC,QAAQ,EAAE2D,UAAU,CAAC3D,QAAQ;QAC7BC,QAAQ,EAAE0D,UAAU,CAAC1D,QAAQ;QAC7BC,QAAQ,EAAEA;MACZ,CAAC;MAED,MAAMyC,MAAM,GAAG,MAAM7D,iBAAiB,CAAC+E,cAAc,CAAC;MAEtD,IAAI,CAAClB,MAAM,CAACC,OAAO,EAAE;QACnB5D,QAAQ,CAAC,sBAAsB2E,UAAU,CAAC9D,IAAI,KAAK8C,MAAM,CAACvB,KAAK,EAAE,CAAC;MACpE,CAAC,MAAM;QACLpC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;MAClB;IAEF,CAAC,CAAC,OAAOoC,KAAK,EAAE;MACdpC,QAAQ,CAAC,UAAUoC,KAAK,CAAC2B,OAAO,EAAE,CAAC;IACrC,CAAC,SAAS;MACRzD,oBAAoB,CAAC,KAAK,CAAC;IAC7B;EACF,CAAC;EAED,MAAMwE,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACF,MAAM/E,sBAAsB,CAAC,CAAC;MAC9BqB,sBAAsB,CAAC,CAAC;IAC1B,CAAC,CAAC,OAAOgB,KAAK,EAAE;MACdpC,QAAQ,CAAC,wBAAwBoC,KAAK,CAAC2B,OAAO,EAAE,CAAC;IACnD;EACF,CAAC;EAED,MAAMgB,gBAAgB,GAAIC,YAAY,IAAK;IACzC,MAAMV,kBAAkB,GAAG7D,WAAW,CAAC8D,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACzC,EAAE,KAAKiD,YAAY,CAAC;IACzEtE,cAAc,CAAC4D,kBAAkB,CAAC;IAClChC,eAAe,CAACgC,kBAAkB,CAAC;EACrC,CAAC;EAED,MAAMW,gBAAgB,GAAGA,CAAA,KAAM;IAC7B/E,YAAY,CAAC,KAAK,CAAC;IACnBU,gBAAgB,CAAC;MACfC,IAAI,EAAE,EAAE;MACRC,IAAI,EAAE,EAAE;MACRC,IAAI,EAAE,MAAM;MACZC,QAAQ,EAAE,cAAc;MACxBC,QAAQ,EAAE,EAAE;MACZC,QAAQ,EAAE;IACZ,CAAC,CAAC;IACFV,mBAAmB,CAAC,IAAI,CAAC;EAC3B,CAAC;EAED,oBACEnB,OAAA;IAAK6F,KAAK,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,QAAQ,EAAE;IAAO,CAAE;IAAAC,QAAA,gBAC9ChG,OAAA;MAAKiG,SAAS,EAAC,wDAAwD;MAAAD,QAAA,gBACrEhG,OAAA;QAAIiG,SAAS,EAAC,aAAa;QAAAD,QAAA,EAAC;MAAyB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC1DrG,OAAA;QAAAgG,QAAA,GACGxF,KAAK,CAACwB,kBAAkB,CAACwB,WAAW,iBACnCxD,OAAA,CAACR,KAAK;UAAC8G,EAAE,EAAC,SAAS;UAACL,SAAS,EAAC,MAAM;UAAAD,QAAA,GAAC,sBACpB,EAACxF,KAAK,CAACwB,kBAAkB,CAACP,IAAI;QAAA;UAAAyE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxC,CACR,eACDrG,OAAA,CAACb,MAAM;UAACoH,OAAO,EAAC,QAAQ;UAACC,OAAO,EAAEnD,mBAAoB;UAAC4C,SAAS,EAAC,MAAM;UAACQ,IAAI,EAAC,IAAI;UAAAT,QAAA,EAAC;QAElF;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTrG,OAAA,CAACb,MAAM;UAACoH,OAAO,EAAC,SAAS;UAACC,OAAO,EAAEA,CAAA,KAAM3F,YAAY,CAAC,IAAI,CAAE;UAAAmF,QAAA,EAAC;QAE7D;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAEL7F,KAAK,CAACuC,KAAK,iBACV/C,OAAA,CAACX,KAAK;MAACkH,OAAO,EAAC,QAAQ;MAACG,WAAW;MAACC,OAAO,EAAEA,CAAA,KAAMhG,QAAQ,CAAC,IAAI,CAAE;MAAAqF,QAAA,EAC/DxF,KAAK,CAACuC;IAAK;MAAAmD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CACR,EAGA,CAAC,EAAA/F,mBAAA,GAAAE,KAAK,CAAC0B,WAAW,cAAA5B,mBAAA,uBAAjBA,mBAAA,CAAmB6B,SAAS,OAAA5B,mBAAA,GAAIC,KAAK,CAAC0B,WAAW,cAAA3B,mBAAA,uBAAjBA,mBAAA,CAAmB+D,MAAM,mBACzDtE,OAAA,CAAChB,GAAG;MAACiH,SAAS,EAAC,MAAM;MAAAD,QAAA,eACnBhG,OAAA,CAACf,GAAG;QAAA+G,QAAA,eACFhG,OAAA,CAACL,mBAAmB;UAClBiH,QAAQ,EAAE,IAAK;UACfC,YAAY,EAAE;QAAK;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAEDrG,OAAA,CAAChB,GAAG;MAACiH,SAAS,EAAC,MAAM;MAAAD,QAAA,gBACnBhG,OAAA,CAACf,GAAG;QAAC6H,EAAE,EAAE,CAAE;QAAAd,QAAA,eACThG,OAAA,CAACd,IAAI;UAAC+G,SAAS,EAAC,WAAW;UAAAD,QAAA,gBACzBhG,OAAA,CAACd,IAAI,CAAC6H,MAAM;YAACd,SAAS,EAAC,wEAAwE;YAAAD,QAAA,gBAC7FhG,OAAA;cAAIiG,SAAS,EAAC,MAAM;cAAAD,QAAA,EAAC;YAAsB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAC/C7F,KAAK,CAACwG,SAAS,iBACdhH,OAAA,CAACP,OAAO;cAACwH,SAAS,EAAC,QAAQ;cAACR,IAAI,EAAC,IAAI;cAACF,OAAO,EAAC;YAAO;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CACxD;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACU,CAAC,eACdrG,OAAA,CAACd,IAAI,CAACgI,IAAI;YAAAlB,QAAA,eACRhG,OAAA,CAACV,KAAK;cAAC6H,OAAO;cAACC,KAAK;cAAApB,QAAA,gBAClBhG,OAAA;gBAAAgG,QAAA,eACEhG,OAAA;kBAAAgG,QAAA,gBACEhG,OAAA;oBAAAgG,QAAA,EAAI;kBAAc;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACvBrG,OAAA;oBAAAgG,QAAA,EAAI;kBAAY;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACrBrG,OAAA;oBAAAgG,QAAA,EAAI;kBAAY;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACrBrG,OAAA;oBAAAgG,QAAA,EAAI;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAClBrG,OAAA;oBAAAgG,QAAA,EAAI;kBAAU;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACnBrG,OAAA;oBAAAgG,QAAA,EAAI;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,eACRrG,OAAA;gBAAAgG,QAAA,GACG5E,WAAW,CAACsC,GAAG,CAACC,IAAI,iBACnB3D,OAAA;kBAAkBiG,SAAS,EAAEtC,IAAI,CAACb,QAAQ,GAAG,eAAe,GAAG,EAAG;kBAAAkD,QAAA,gBAChEhG,OAAA;oBAAAgG,QAAA,gBACEhG,OAAA;sBAAAgG,QAAA,EAASrC,IAAI,CAACnC;oBAAI;sBAAA0E,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAS,CAAC,EAC3B1C,IAAI,CAACb,QAAQ,iBAAI9C,OAAA,CAACR,KAAK;sBAAC8G,EAAE,EAAC,QAAQ;sBAACL,SAAS,EAAC,MAAM;sBAAAD,QAAA,EAAC;oBAAM;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClE,CAAC,eACLrG,OAAA;oBAAAgG,QAAA,GAAKrC,IAAI,CAAClC,IAAI,EAAC,GAAC,EAACkC,IAAI,CAACjC,IAAI;kBAAA;oBAAAwE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAChCrG,OAAA;oBAAAgG,QAAA,EAAKrC,IAAI,CAAChC;kBAAQ;oBAAAuE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACxBrG,OAAA;oBAAAgG,QAAA,eACEhG,OAAA,CAACR,KAAK;sBAAC8G,EAAE,EAAE3C,IAAI,CAAChB,MAAM,KAAK,WAAW,GAAG,SAAS,GAAG,QAAS;sBAAAqD,QAAA,EAC3DrC,IAAI,CAAChB,MAAM,KAAK,WAAW,GAAG,aAAa,GAAG;oBAAgB;sBAAAuD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1D;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,eACLrG,OAAA;oBAAAgG,QAAA,EAAKrC,IAAI,CAACd,OAAO,IAAI;kBAAS;oBAAAqD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACpCrG,OAAA;oBAAAgG,QAAA,GACGrC,IAAI,CAAChB,MAAM,KAAK,WAAW,gBAC1B3C,OAAA,CAACb,MAAM;sBACLsH,IAAI,EAAC,IAAI;sBACTF,OAAO,EAAC,gBAAgB;sBACxBN,SAAS,EAAC,MAAM;sBAChBO,OAAO,EAAEf,gBAAiB;sBAC1B4B,QAAQ,EAAErG,iBAAkB;sBAAAgF,QAAA,EAC7B;oBAED;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,gBAETrG,OAAA,CAACb,MAAM;sBACLsH,IAAI,EAAC,IAAI;sBACTF,OAAO,EAAC,iBAAiB;sBACzBN,SAAS,EAAC,MAAM;sBAChBO,OAAO,EAAEA,CAAA,KAAMnB,wBAAwB,CAAC1B,IAAI,CAAE;sBAC9C0D,QAAQ,EAAErG,iBAAkB;sBAAAgF,QAAA,GAE3BhF,iBAAiB,GAAG,GAAG,GAAG,IAAI,EAAC,UAClC;oBAAA;sBAAAkF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CACT,eACDrG,OAAA,CAACb,MAAM;sBACLsH,IAAI,EAAC,IAAI;sBACTF,OAAO,EAAC,gBAAgB;sBACxBC,OAAO,EAAEA,CAAA,KAAMd,gBAAgB,CAAC/B,IAAI,CAACjB,EAAE,CAAE;sBAAAsD,QAAA,EAC1C;oBAED;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACP,CAAC;gBAAA,GA1CE1C,IAAI,CAACjB,EAAE;kBAAAwD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OA2CZ,CACL,CAAC,EACDjF,WAAW,CAACkG,MAAM,KAAK,CAAC,iBACvBtH,OAAA;kBAAAgG,QAAA,eACEhG,OAAA;oBAAIuH,OAAO,EAAC,GAAG;oBAACtB,SAAS,EAAC,wBAAwB;oBAAAD,QAAA,EAAC;kBAEnD;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACL;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAENrG,OAAA,CAACf,GAAG;QAAC6H,EAAE,EAAE,CAAE;QAAAd,QAAA,eACThG,OAAA,CAACd,IAAI;UAAC+G,SAAS,EAAC,iBAAiB;UAAAD,QAAA,gBAC/BhG,OAAA,CAACd,IAAI,CAAC6H,MAAM;YAACd,SAAS,EAAC,oBAAoB;YAAAD,QAAA,eACzChG,OAAA;cAAIiG,SAAS,EAAC,MAAM;cAAAD,QAAA,EAAC;YAAoB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CAAC,eACdrG,OAAA,CAACd,IAAI,CAACgI,IAAI;YAAAlB,QAAA,GACPxF,KAAK,CAACwB,kBAAkB,CAACwB,WAAW,IAAIhD,KAAK,CAACwB,kBAAkB,CAAC+B,UAAU,iBAC1E/D,OAAA;cAAKiG,SAAS,EAAC,MAAM;cAAAD,QAAA,gBACnBhG,OAAA;gBAAAgG,QAAA,EAAI;cAAsB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC/BrG,OAAA;gBAAIiG,SAAS,EAAC,eAAe;gBAAAD,QAAA,gBAC3BhG,OAAA;kBAAAgG,QAAA,gBAAIhG,OAAA;oBAAAgG,QAAA,EAAQ;kBAAK;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,KAAC,EAAC7F,KAAK,CAACwB,kBAAkB,CAACP,IAAI;gBAAA;kBAAAyE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC/DrG,OAAA;kBAAAgG,QAAA,gBAAIhG,OAAA;oBAAAgG,QAAA,EAAQ;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,KAAC,EAAC7F,KAAK,CAACwB,kBAAkB,CAACL,QAAQ;gBAAA;kBAAAuE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACvErG,OAAA;kBAAAgG,QAAA,gBAAIhG,OAAA;oBAAAgG,QAAA,EAAQ;kBAAK;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,KAAC,EAAC7F,KAAK,CAACwB,kBAAkB,CAACJ,QAAQ;gBAAA;kBAAAsE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACnErG,OAAA;kBAAAgG,QAAA,gBAAIhG,OAAA;oBAAAgG,QAAA,EAAQ;kBAAU;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,KAAC,EAAC,IAAIxC,IAAI,CAACrD,KAAK,CAACwB,kBAAkB,CAACwF,aAAa,CAAC,CAAC1D,cAAc,CAAC,CAAC;gBAAA;kBAAAoC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtG,CAAC,eACLrG,OAAA;gBAAAkG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN,eAEDrG,OAAA;cAAAgG,QAAA,EAAI;YAAiB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC1BrG,OAAA;cAAIiG,SAAS,EAAC,eAAe;cAAAD,QAAA,gBAC3BhG,OAAA;gBAAAgG,QAAA,gBAAIhG,OAAA;kBAAAgG,QAAA,EAAM;gBAAU;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,oBAAgB;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAChDrG,OAAA;gBAAAgG,QAAA,gBAAIhG,OAAA;kBAAAgG,QAAA,EAAM;gBAAoB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,iBAAa;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACvDrG,OAAA;gBAAAgG,QAAA,gBAAIhG,OAAA;kBAAAgG,QAAA,EAAM;gBAAS;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,gBAAY;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC3CrG,OAAA;gBAAAgG,QAAA,gBAAIhG,OAAA;kBAAAgG,QAAA,EAAM;gBAAmB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAAW;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACpDrG,OAAA;gBAAAgG,QAAA,gBAAIhG,OAAA;kBAAAgG,QAAA,EAAM;gBAAW;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,qBAAiB;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAClDrG,OAAA;gBAAAgG,QAAA,gBAAIhG,OAAA;kBAAAgG,QAAA,EAAM;gBAAQ;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,oBAAgB;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5C,CAAC,eAELrG,OAAA;cAAAgG,QAAA,EAAI;YAAwB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACjCrG,OAAA;cAAIiG,SAAS,EAAC,eAAe;cAAAD,QAAA,gBAC3BhG,OAAA;gBAAAgG,QAAA,GAAI,SAAE,eAAAhG,OAAA;kBAAAgG,QAAA,EAAM;gBAAM;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,yBAAqB;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACnDrG,OAAA;gBAAAgG,QAAA,GAAI,SAAE,eAAAhG,OAAA;kBAAAgG,QAAA,EAAM;gBAAM;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,sBAAkB;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAChDrG,OAAA;gBAAAgG,QAAA,GAAI,SAAE,eAAAhG,OAAA;kBAAAgG,QAAA,EAAM;gBAAM;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,sBAAkB;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAChDrG,OAAA;gBAAAgG,QAAA,GAAI,SAAE,eAAAhG,OAAA;kBAAAgG,QAAA,EAAM;gBAAM;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,wBAAoB;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD,CAAC,eAELrG,OAAA,CAACX,KAAK;cAACkH,OAAO,EAAC,SAAS;cAACN,SAAS,EAAC,MAAM;cAAAD,QAAA,eACvChG,OAAA;gBAAAgG,QAAA,gBACEhG,OAAA;kBAAAgG,QAAA,EAAQ;gBAAa;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,sEAChC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNrG,OAAA,CAAChB,GAAG;MAACiH,SAAS,EAAC,MAAM;MAAAD,QAAA,gBACnBhG,OAAA,CAACf,GAAG;QAAC6H,EAAE,EAAE,CAAE;QAAAd,QAAA,eACThG,OAAA,CAACF,sBAAsB;UAAAoG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvB,CAAC,eACNrG,OAAA,CAACf,GAAG;QAAC6H,EAAE,EAAE,CAAE;QAAAd,QAAA,EAERxF,KAAK,CAACwB,kBAAkB,CAACwB,WAAW,iBACnCxD,OAAA,CAACH,oBAAoB;UAAAqG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MACxB;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNrG,OAAA,CAACT,KAAK;MAACkI,IAAI,EAAE7G,SAAU;MAAC8G,MAAM,EAAE9B,gBAAiB;MAACa,IAAI,EAAC,IAAI;MAAAT,QAAA,gBACzDhG,OAAA,CAACT,KAAK,CAACwH,MAAM;QAACY,WAAW;QAAA3B,QAAA,eACvBhG,OAAA,CAACT,KAAK,CAACqI,KAAK;UAAA5B,QAAA,EAAC;QAAgC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/C,CAAC,eACfrG,OAAA,CAACT,KAAK,CAAC2H,IAAI;QAAAlB,QAAA,GACR9E,gBAAgB,iBACflB,OAAA,CAACX,KAAK;UAACkH,OAAO,EAAErF,gBAAgB,CAACuD,IAAK;UAACiC,WAAW;UAACC,OAAO,EAAEA,CAAA,KAAMxF,mBAAmB,CAAC,IAAI,CAAE;UAAA6E,QAAA,EACzF9E,gBAAgB,CAACwD;QAAO;UAAAwB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB,CACR,eAEDrG,OAAA,CAACZ,IAAI;UAAA4G,QAAA,gBACHhG,OAAA,CAAChB,GAAG;YAAAgH,QAAA,gBACFhG,OAAA,CAACf,GAAG;cAAC4I,EAAE,EAAE,CAAE;cAAA7B,QAAA,eACThG,OAAA,CAACZ,IAAI,CAAC0I,KAAK;gBAAC7B,SAAS,EAAC,MAAM;gBAAAD,QAAA,gBAC1BhG,OAAA,CAACZ,IAAI,CAAC2I,KAAK;kBAAA/B,QAAA,EAAC;gBAAkB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC3CrG,OAAA,CAACZ,IAAI,CAAC4I,OAAO;kBACXvD,IAAI,EAAC,MAAM;kBACXwD,WAAW,EAAC,8BAA8B;kBAC1C/D,KAAK,EAAE5C,aAAa,CAACE,IAAK;kBAC1B0G,QAAQ,EAAGC,CAAC,IAAKnE,iBAAiB,CAAC,MAAM,EAAEmE,CAAC,CAACC,MAAM,CAAClE,KAAK;gBAAE;kBAAAgC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5D,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACNrG,OAAA,CAACf,GAAG;cAAC4I,EAAE,EAAE,CAAE;cAAA7B,QAAA,eACThG,OAAA,CAACZ,IAAI,CAAC0I,KAAK;gBAAC7B,SAAS,EAAC,MAAM;gBAAAD,QAAA,gBAC1BhG,OAAA,CAACZ,IAAI,CAAC2I,KAAK;kBAAA/B,QAAA,EAAC;gBAAiB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC1CrG,OAAA,CAACZ,IAAI,CAAC4I,OAAO;kBACXvD,IAAI,EAAC,MAAM;kBACXP,KAAK,EAAE5C,aAAa,CAACK,QAAS;kBAC9BuG,QAAQ,EAAGC,CAAC,IAAKnE,iBAAiB,CAAC,UAAU,EAAEmE,CAAC,CAACC,MAAM,CAAClE,KAAK;gBAAE;kBAAAgC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENrG,OAAA,CAAChB,GAAG;YAAAgH,QAAA,gBACFhG,OAAA,CAACf,GAAG;cAAC4I,EAAE,EAAE,CAAE;cAAA7B,QAAA,eACThG,OAAA,CAACZ,IAAI,CAAC0I,KAAK;gBAAC7B,SAAS,EAAC,MAAM;gBAAAD,QAAA,gBAC1BhG,OAAA,CAACZ,IAAI,CAAC2I,KAAK;kBAAA/B,QAAA,EAAC;gBAAkB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC3CrG,OAAA,CAACZ,IAAI,CAAC4I,OAAO;kBACXvD,IAAI,EAAC,MAAM;kBACXwD,WAAW,EAAC,gCAAgC;kBAC5C/D,KAAK,EAAE5C,aAAa,CAACG,IAAK;kBAC1ByG,QAAQ,EAAGC,CAAC,IAAKnE,iBAAiB,CAAC,MAAM,EAAEmE,CAAC,CAACC,MAAM,CAAClE,KAAK;gBAAE;kBAAAgC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5D,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACNrG,OAAA,CAACf,GAAG;cAAC4I,EAAE,EAAE,CAAE;cAAA7B,QAAA,eACThG,OAAA,CAACZ,IAAI,CAAC0I,KAAK;gBAAC7B,SAAS,EAAC,MAAM;gBAAAD,QAAA,gBAC1BhG,OAAA,CAACZ,IAAI,CAAC2I,KAAK;kBAAA/B,QAAA,EAAC;gBAAO;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAChCrG,OAAA,CAACZ,IAAI,CAAC4I,OAAO;kBACXvD,IAAI,EAAC,MAAM;kBACXP,KAAK,EAAE5C,aAAa,CAACI,IAAK;kBAC1BwG,QAAQ,EAAGC,CAAC,IAAKnE,iBAAiB,CAAC,MAAM,EAAEmE,CAAC,CAACC,MAAM,CAAClE,KAAK;gBAAE;kBAAAgC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5D,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENrG,OAAA,CAAChB,GAAG;YAAAgH,QAAA,gBACFhG,OAAA,CAACf,GAAG;cAAC4I,EAAE,EAAE,CAAE;cAAA7B,QAAA,eACThG,OAAA,CAACZ,IAAI,CAAC0I,KAAK;gBAAC7B,SAAS,EAAC,MAAM;gBAAAD,QAAA,gBAC1BhG,OAAA,CAACZ,IAAI,CAAC2I,KAAK;kBAAA/B,QAAA,EAAC;gBAAW;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACpCrG,OAAA,CAACZ,IAAI,CAAC4I,OAAO;kBACXvD,IAAI,EAAC,MAAM;kBACXwD,WAAW,EAAC,mBAAmB;kBAC/B/D,KAAK,EAAE5C,aAAa,CAACM,QAAS;kBAC9BsG,QAAQ,EAAGC,CAAC,IAAKnE,iBAAiB,CAAC,UAAU,EAAEmE,CAAC,CAACC,MAAM,CAAClE,KAAK;gBAAE;kBAAAgC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACNrG,OAAA,CAACf,GAAG;cAAC4I,EAAE,EAAE,CAAE;cAAA7B,QAAA,eACThG,OAAA,CAACZ,IAAI,CAAC0I,KAAK;gBAAC7B,SAAS,EAAC,MAAM;gBAAAD,QAAA,gBAC1BhG,OAAA,CAACZ,IAAI,CAAC2I,KAAK;kBAAA/B,QAAA,EAAC;gBAAW;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACpCrG,OAAA,CAACZ,IAAI,CAAC4I,OAAO;kBACXvD,IAAI,EAAC,UAAU;kBACfwD,WAAW,EAAC,mBAAmB;kBAC/B/D,KAAK,EAAE5C,aAAa,CAACO,QAAS;kBAC9BqG,QAAQ,EAAGC,CAAC,IAAKnE,iBAAiB,CAAC,UAAU,EAAEmE,CAAC,CAACC,MAAM,CAAClE,KAAK;gBAAE;kBAAAgC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENrG,OAAA,CAACX,KAAK;YAACkH,OAAO,EAAC,MAAM;YAAAP,QAAA,gBACnBhG,OAAA;cAAAgG,QAAA,EAAQ;YAAO;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,0GAC1B;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eACbrG,OAAA,CAACT,KAAK,CAAC8I,MAAM;QAAArC,QAAA,gBACXhG,OAAA,CAACb,MAAM;UAACoH,OAAO,EAAC,WAAW;UAACC,OAAO,EAAEZ,gBAAiB;UAAAI,QAAA,EAAC;QAEvD;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTrG,OAAA,CAACb,MAAM;UACLoH,OAAO,EAAC,SAAS;UACjBC,OAAO,EAAErC,cAAe;UACxBkD,QAAQ,EAAErG,iBAAiB,IAAI,CAACM,aAAa,CAACG,IAAI,IAAI,CAACH,aAAa,CAACM,QAAS;UAAAoE,QAAA,EAE7EhF,iBAAiB,gBAChBhB,OAAA,CAAAE,SAAA;YAAA8F,QAAA,gBACEhG,OAAA,CAACP,OAAO;cAACwH,SAAS,EAAC,QAAQ;cAACR,IAAI,EAAC,IAAI;cAACR,SAAS,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,cAE3D;UAAA,eAAE,CAAC,GAEH;QACD;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGRrG,OAAA,CAACJ,gBAAgB;MACf6H,IAAI,EAAE3G,oBAAqB;MAC3B4G,MAAM,EAAEA,CAAA,KAAM3G,uBAAuB,CAAC,KAAK;IAAE;MAAAmF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9C,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAACjG,EAAA,CAhiBID,WAAW;EAAA,QAMXT,MAAM;AAAA;AAAA4I,EAAA,GANNnI,WAAW;AAkiBjB,eAAeA,WAAW;AAAC,IAAAmI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}