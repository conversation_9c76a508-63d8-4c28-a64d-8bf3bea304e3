{"ast": null, "code": "var _jsxFileName = \"F:\\\\WORKSPACE\\\\XUI IMPORTER\\\\xui-importer\\\\src\\\\components\\\\Dashboard.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport { <PERSON>, Col, Card, Button, Table, Badge, Spinner, Alert } from 'react-bootstrap';\nimport { useApp } from '../context/AppContext';\nimport { databaseAPI } from '../services/apiService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Dashboard = () => {\n  _s();\n  var _data$stats, _data$stats$tvSeries, _data$stats2, _data$stats2$movies, _data$stats3, _data$stats3$episodes, _data$stats4, _data$stats4$liveStre, _data$contentByType, _data$recentSeries, _data$recentSeries2, _data$servers, _data$servers2, _data$categories;\n  const {\n    state\n  } = useApp();\n  const [dashboardData, setDashboardData] = useState({\n    recentSeries: [],\n    servers: [],\n    categories: [],\n    contentByType: []\n  });\n  const [loading, setLoading] = useState(false);\n  useEffect(() => {\n    if (window.debugLog) {\n      window.debugLog('📊 Dashboard component loaded', 'success');\n    }\n\n    // Cargar datos si hay conexión a BD\n    if (state.databaseConnection.isConnected) {\n      loadDashboardData();\n    }\n  }, [state.databaseConnection.isConnected]);\n  const loadDashboardData = async () => {\n    setLoading(true);\n    try {\n      if (window.debugLog) {\n        window.debugLog('📈 Loading dashboard data from database...', 'info');\n      }\n      const result = await databaseAPI.getDashboardData();\n      if (result.success) {\n        setDashboardData(result.data);\n        if (window.debugLog) {\n          window.debugLog('✅ Dashboard data loaded successfully', 'success');\n        }\n      } else {\n        console.error('Error loading dashboard data:', result.message);\n        if (window.debugLog) {\n          window.debugLog(`❌ Error loading dashboard: ${result.message}`, 'error');\n        }\n      }\n    } catch (error) {\n      console.error('Error loading dashboard data:', error);\n      if (window.debugLog) {\n        window.debugLog(`❌ Dashboard error: ${error.message}`, 'error');\n      }\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleRefresh = () => {\n    if (window.debugLog) {\n      window.debugLog('🔄 Refreshing dashboard data...', 'info');\n    }\n    loadDashboardData();\n  };\n\n  // Datos por defecto si no hay conexión\n  const defaultData = {\n    totalSeries: 0,\n    totalEpisodes: 0,\n    totalStreams: 0,\n    categories: [],\n    servers: [],\n    recentSeries: []\n  };\n  const data = dashboardData || defaultData;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      width: '100%',\n      maxWidth: 'none'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"d-flex justify-content-between align-items-center mb-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-danger\",\n          children: \"\\uD83D\\uDCCA Dashboard - RGS IMPORT TOOL XUI\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 80,\n          columnNumber: 11\n        }, this), state.databaseConnection.isConnected && /*#__PURE__*/_jsxDEV(\"small\", {\n          className: \"text-success fw-bold\",\n          children: [\"\\u2705 Connected to \", state.databaseConnection.host, \":\", state.databaseConnection.database]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 82,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 79,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [loading && /*#__PURE__*/_jsxDEV(Spinner, {\n          animation: \"border\",\n          size: \"sm\",\n          className: \"me-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 88,\n          columnNumber: 23\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"success\",\n          size: \"sm\",\n          onClick: handleRefresh,\n          disabled: loading || !state.databaseConnection.isConnected,\n          children: \"\\uD83D\\uDD04 Refresh Data\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 89,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 87,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 78,\n      columnNumber: 7\n    }, this), !state.databaseConnection.isConnected && /*#__PURE__*/_jsxDEV(Alert, {\n      variant: \"warning\",\n      className: \"mb-4\",\n      children: [/*#__PURE__*/_jsxDEV(Alert.Heading, {\n        children: \"\\uD83D\\uDD17 No Database Connection\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 102,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: [\"Connect to your XUI database in the \", /*#__PURE__*/_jsxDEV(\"strong\", {\n          children: \"Connections\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 103,\n          columnNumber: 50\n        }, this), \" tab to view real data.\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 103,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 101,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      className: \"mb-4\",\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          bg: \"success\",\n          text: \"white\",\n          className: \"mb-2 shadow\",\n          children: /*#__PURE__*/_jsxDEV(Card.Body, {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n              children: \"\\uD83D\\uDCFA TV Series\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 111,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"display-4\",\n              children: ((_data$stats = data.stats) === null || _data$stats === void 0 ? void 0 : (_data$stats$tvSeries = _data$stats.tvSeries) === null || _data$stats$tvSeries === void 0 ? void 0 : _data$stats$tvSeries.toLocaleString()) || 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 112,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n              children: \"Series Available\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 113,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 110,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 109,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 108,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          bg: \"info\",\n          text: \"white\",\n          className: \"mb-2 shadow\",\n          children: /*#__PURE__*/_jsxDEV(Card.Body, {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n              children: \"\\uD83C\\uDFAC Movies\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 120,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"display-4\",\n              children: ((_data$stats2 = data.stats) === null || _data$stats2 === void 0 ? void 0 : (_data$stats2$movies = _data$stats2.movies) === null || _data$stats2$movies === void 0 ? void 0 : _data$stats2$movies.toLocaleString()) || 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 121,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n              children: \"Movies Available\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 122,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 119,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 118,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 117,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          bg: \"warning\",\n          text: \"white\",\n          className: \"mb-2 shadow\",\n          children: /*#__PURE__*/_jsxDEV(Card.Body, {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n              children: \"\\uD83C\\uDF9E\\uFE0F Episodes\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 129,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"display-4\",\n              children: ((_data$stats3 = data.stats) === null || _data$stats3 === void 0 ? void 0 : (_data$stats3$episodes = _data$stats3.episodes) === null || _data$stats3$episodes === void 0 ? void 0 : _data$stats3$episodes.toLocaleString()) || 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 130,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n              children: \"Total Episodes\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 131,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 128,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 127,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 126,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          bg: \"danger\",\n          text: \"white\",\n          className: \"mb-2 shadow\",\n          children: /*#__PURE__*/_jsxDEV(Card.Body, {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n              children: \"\\uD83D\\uDCFA Live TV\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 138,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"display-4\",\n              children: ((_data$stats4 = data.stats) === null || _data$stats4 === void 0 ? void 0 : (_data$stats4$liveStre = _data$stats4.liveStreams) === null || _data$stats4$liveStre === void 0 ? void 0 : _data$stats4$liveStre.toLocaleString()) || 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 139,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n              children: \"Live Channels\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 140,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 137,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 136,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 135,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 107,\n      columnNumber: 7\n    }, this), ((_data$contentByType = data.contentByType) === null || _data$contentByType === void 0 ? void 0 : _data$contentByType.length) > 0 && /*#__PURE__*/_jsxDEV(Row, {\n      className: \"mb-4\",\n      children: /*#__PURE__*/_jsxDEV(Col, {\n        lg: 12,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"shadow-sm\",\n          children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n            className: \"bg-dark text-white\",\n            children: /*#__PURE__*/_jsxDEV(\"h5\", {\n              className: \"mb-0\",\n              children: \"\\uD83D\\uDCCA Content Breakdown by Type\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 152,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 151,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n            children: /*#__PURE__*/_jsxDEV(Row, {\n              children: data.contentByType.map((contentType, index) => {\n                var _contentType$count;\n                return /*#__PURE__*/_jsxDEV(Col, {\n                  md: 4,\n                  className: \"mb-3\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-center p-3 border rounded\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"mb-1\",\n                      children: ((_contentType$count = contentType.count) === null || _contentType$count === void 0 ? void 0 : _contentType$count.toLocaleString()) || 0\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 159,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Badge, {\n                      bg: \"primary\",\n                      className: \"mb-2\",\n                      children: contentType.type\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 160,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"mb-0 text-secondary fw-normal\",\n                      children: contentType.type || 'Unknown Type'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 161,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 158,\n                    columnNumber: 23\n                  }, this)\n                }, `content-type-${contentType.type}-${index}`, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 157,\n                  columnNumber: 21\n                }, this);\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 155,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 154,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 150,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 149,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 148,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      className: \"mb-4\",\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        lg: 8,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"shadow-sm\",\n          children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n            className: \"bg-danger text-white\",\n            children: /*#__PURE__*/_jsxDEV(\"h5\", {\n              className: \"mb-0\",\n              children: [\"\\uD83D\\uDCDA Recent Series \", ((_data$recentSeries = data.recentSeries) === null || _data$recentSeries === void 0 ? void 0 : _data$recentSeries.length) > 0 && `(${data.recentSeries.length})`]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 176,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 175,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n            children: ((_data$recentSeries2 = data.recentSeries) === null || _data$recentSeries2 === void 0 ? void 0 : _data$recentSeries2.length) > 0 ? /*#__PURE__*/_jsxDEV(Table, {\n              striped: true,\n              hover: true,\n              children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"\\uD83D\\uDCFA Series Name\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 183,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"\\uD83C\\uDF9E\\uFE0F Episodes\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 184,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"\\uD83D\\uDCC5 Added\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 185,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"\\uD83C\\uDFF7\\uFE0F Category\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 186,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 182,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 181,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                children: data.recentSeries.map((series, index) => /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: series.title || 'Unknown Series'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 192,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 192,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(Badge, {\n                      bg: \"info\",\n                      children: [series.episode_count || 0, \" eps\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 193,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 193,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: series.year || 'Invalid Date'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 194,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(Badge, {\n                      bg: \"secondary\",\n                      children: \"Unknown\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 195,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 195,\n                    columnNumber: 25\n                  }, this)]\n                }, `series-${series.title || series.id || index}`, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 191,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 189,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 180,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-secondary text-center py-3 fw-normal\",\n              children: state.databaseConnection.isConnected ? 'No series found in database' : 'Connect to database to view series'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 201,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 178,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 174,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 173,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        lg: 4,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"shadow-sm h-100\",\n          children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n            className: \"bg-success text-white\",\n            children: /*#__PURE__*/_jsxDEV(\"h5\", {\n              className: \"mb-0\",\n              children: \"\\u26A1 Quick Actions\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 215,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 214,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n            className: \"d-flex flex-column\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                variant: \"primary\",\n                className: \"w-100 mb-2\",\n                size: \"lg\",\n                children: \"\\uD83D\\uDCE5 Import M3U File\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 219,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"info\",\n                className: \"w-100 mb-2\",\n                children: \"\\uD83D\\uDD17 Manage Connections\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 222,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"warning\",\n                className: \"w-100 mb-2\",\n                children: \"\\uD83D\\uDCCA View Import History\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 225,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 218,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-auto\",\n              children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                children: \"\\uD83D\\uDDA5\\uFE0F Available Servers:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 231,\n                columnNumber: 17\n              }, this), ((_data$servers = data.servers) === null || _data$servers === void 0 ? void 0 : _data$servers.length) > 0 ? /*#__PURE__*/_jsxDEV(\"ul\", {\n                className: \"list-unstyled\",\n                children: [(data.servers || []).slice(0, 3).map((server, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: [/*#__PURE__*/_jsxDEV(Badge, {\n                    bg: \"outline-primary\",\n                    className: \"me-1\",\n                    children: \"\\uD83D\\uDCE1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 236,\n                    columnNumber: 25\n                  }, this), server.server_name]\n                }, `server-${server.server_name || index}-${index}`, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 235,\n                  columnNumber: 23\n                }, this)), ((_data$servers2 = data.servers) === null || _data$servers2 === void 0 ? void 0 : _data$servers2.length) > 3 && /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: /*#__PURE__*/_jsxDEV(\"small\", {\n                    className: \"text-secondary fw-normal\",\n                    children: [\"+\", data.servers.length - 3, \" more...\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 241,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 241,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 233,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-muted\",\n                children: \"No servers configured\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 245,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 230,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 217,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 213,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 212,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 172,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      children: /*#__PURE__*/_jsxDEV(Col, {\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"shadow-sm\",\n          children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n            className: \"bg-info text-white\",\n            children: /*#__PURE__*/_jsxDEV(\"h5\", {\n              className: \"mb-0\",\n              children: \"\\uD83D\\uDCCA System Status\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 257,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 256,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n            children: /*#__PURE__*/_jsxDEV(Row, {\n              children: [/*#__PURE__*/_jsxDEV(Col, {\n                md: 4,\n                children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                  children: \"\\uD83D\\uDDC4\\uFE0F Database Connection:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 262,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Badge, {\n                  bg: state.databaseConnection.isConnected ? 'success' : 'danger',\n                  children: state.databaseConnection.isConnected ? '✅ Connected' : '❌ Disconnected'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 263,\n                  columnNumber: 19\n                }, this), state.databaseConnection.isConnected && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mt-2\",\n                  children: /*#__PURE__*/_jsxDEV(\"small\", {\n                    className: \"text-muted\",\n                    children: [\"Host: \", state.databaseConnection.host, /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 269,\n                      columnNumber: 62\n                    }, this), \"Database: \", state.databaseConnection.database]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 268,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 267,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 261,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Col, {\n                md: 4,\n                children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                  children: \"\\uD83C\\uDF10 Backend Status:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 276,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Badge, {\n                  bg: state.backendStatus === 'connected' ? 'success' : 'warning',\n                  children: state.backendStatus === 'connected' ? '✅ Online' : '⚠️ Checking...'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 277,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 275,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Col, {\n                md: 4,\n                children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                  children: \"\\uD83C\\uDFAF Categories:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 282,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Badge, {\n                  bg: \"primary\",\n                  children: [((_data$categories = data.categories) === null || _data$categories === void 0 ? void 0 : _data$categories.length) || 0, \" configured\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 283,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 281,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 260,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 259,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 255,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 254,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 253,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 77,\n    columnNumber: 5\n  }, this);\n};\n_s(Dashboard, \"OU4wSgydqKiujk3TCAPEZJ0D6Fw=\", false, function () {\n  return [useApp];\n});\n_c = Dashboard;\nexport default Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "Row", "Col", "Card", "<PERSON><PERSON>", "Table", "Badge", "Spinner", "<PERSON><PERSON>", "useApp", "databaseAPI", "jsxDEV", "_jsxDEV", "Dashboard", "_s", "_data$stats", "_data$stats$tvSeries", "_data$stats2", "_data$stats2$movies", "_data$stats3", "_data$stats3$episodes", "_data$stats4", "_data$stats4$liveStre", "_data$contentByType", "_data$recentSeries", "_data$recentSeries2", "_data$servers", "_data$servers2", "_data$categories", "state", "dashboardData", "setDashboardData", "recentSeries", "servers", "categories", "contentByType", "loading", "setLoading", "window", "debugLog", "databaseConnection", "isConnected", "loadDashboardData", "result", "getDashboardData", "success", "data", "console", "error", "message", "handleRefresh", "defaultData", "totalSeries", "totalEpisodes", "totalStreams", "style", "width", "max<PERSON><PERSON><PERSON>", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "host", "database", "animation", "size", "variant", "onClick", "disabled", "Heading", "md", "bg", "text", "Body", "stats", "tvSeries", "toLocaleString", "movies", "episodes", "liveStreams", "length", "lg", "Header", "map", "contentType", "index", "_contentType$count", "count", "type", "striped", "hover", "series", "title", "episode_count", "year", "id", "slice", "server", "server_name", "backendStatus", "_c", "$RefreshReg$"], "sources": ["F:/WORKSPACE/XUI IMPORTER/xui-importer/src/components/Dashboard.js"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\r\nimport { <PERSON>, Col, Card, But<PERSON>, Table, Badge, Spinner, Alert } from 'react-bootstrap';\r\nimport { useApp } from '../context/AppContext';\r\nimport { databaseAPI } from '../services/apiService';\r\n\r\nconst Dashboard = () => {\r\n  const { state } = useApp();\r\n  const [dashboardData, setDashboardData] = useState({\r\n    recentSeries: [],\r\n    servers: [],\r\n    categories: [],\r\n    contentByType: []\r\n  });\r\n  const [loading, setLoading] = useState(false);\r\n\r\n  useEffect(() => {\r\n    if (window.debugLog) {\r\n      window.debugLog('📊 Dashboard component loaded', 'success');\r\n    }\r\n    \r\n    // Cargar datos si hay conexión a BD\r\n    if (state.databaseConnection.isConnected) {\r\n      loadDashboardData();\r\n    }\r\n  }, [state.databaseConnection.isConnected]);\r\n\r\n  const loadDashboardData = async () => {\r\n    setLoading(true);\r\n    try {\r\n      if (window.debugLog) {\r\n        window.debugLog('📈 Loading dashboard data from database...', 'info');\r\n      }\r\n      \r\n      const result = await databaseAPI.getDashboardData();\r\n      \r\n      if (result.success) {\r\n        setDashboardData(result.data);\r\n        if (window.debugLog) {\r\n          window.debugLog('✅ Dashboard data loaded successfully', 'success');\r\n        }\r\n      } else {\r\n        console.error('Error loading dashboard data:', result.message);\r\n        if (window.debugLog) {\r\n          window.debugLog(`❌ Error loading dashboard: ${result.message}`, 'error');\r\n        }\r\n      }\r\n    } catch (error) {\r\n      console.error('Error loading dashboard data:', error);\r\n      if (window.debugLog) {\r\n        window.debugLog(`❌ Dashboard error: ${error.message}`, 'error');\r\n      }\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleRefresh = () => {\r\n    if (window.debugLog) {\r\n      window.debugLog('🔄 Refreshing dashboard data...', 'info');\r\n    }\r\n    loadDashboardData();\r\n  };\r\n\r\n  // Datos por defecto si no hay conexión\r\n  const defaultData = {\r\n    totalSeries: 0,\r\n    totalEpisodes: 0,\r\n    totalStreams: 0,\r\n    categories: [],\r\n    servers: [],\r\n    recentSeries: []\r\n  };\r\n\r\n  const data = dashboardData || defaultData;\r\n\r\n  return (\r\n    <div style={{ width: '100%', maxWidth: 'none' }}>\r\n      <div className=\"d-flex justify-content-between align-items-center mb-4\">\r\n        <div>\r\n          <h1 className=\"text-danger\">📊 Dashboard - RGS IMPORT TOOL XUI</h1>\r\n          {state.databaseConnection.isConnected && (\r\n            <small className=\"text-success fw-bold\">\r\n              ✅ Connected to {state.databaseConnection.host}:{state.databaseConnection.database}\r\n            </small>\r\n          )}\r\n        </div>\r\n        <div>\r\n          {loading && <Spinner animation=\"border\" size=\"sm\" className=\"me-2\" />}\r\n          <Button \r\n            variant=\"success\" \r\n            size=\"sm\" \r\n            onClick={handleRefresh}\r\n            disabled={loading || !state.databaseConnection.isConnected}\r\n          >\r\n            🔄 Refresh Data\r\n          </Button>\r\n        </div>\r\n      </div>\r\n\r\n      {!state.databaseConnection.isConnected && (\r\n        <Alert variant=\"warning\" className=\"mb-4\">\r\n          <Alert.Heading>🔗 No Database Connection</Alert.Heading>\r\n          <p>Connect to your XUI database in the <strong>Connections</strong> tab to view real data.</p>\r\n        </Alert>\r\n      )}\r\n      \r\n      <Row className=\"mb-4\">\r\n        <Col md={3}>\r\n          <Card bg=\"success\" text=\"white\" className=\"mb-2 shadow\">\r\n            <Card.Body className=\"text-center\">\r\n              <h5>📺 TV Series</h5>\r\n              <h2 className=\"display-4\">{data.stats?.tvSeries?.toLocaleString() || 0}</h2>\r\n              <small>Series Available</small>\r\n            </Card.Body>\r\n          </Card>\r\n        </Col>\r\n        <Col md={3}>\r\n          <Card bg=\"info\" text=\"white\" className=\"mb-2 shadow\">\r\n            <Card.Body className=\"text-center\">\r\n              <h5>🎬 Movies</h5>\r\n              <h2 className=\"display-4\">{data.stats?.movies?.toLocaleString() || 0}</h2>\r\n              <small>Movies Available</small>\r\n            </Card.Body>\r\n          </Card>\r\n        </Col>\r\n        <Col md={3}>\r\n          <Card bg=\"warning\" text=\"white\" className=\"mb-2 shadow\">\r\n            <Card.Body className=\"text-center\">\r\n              <h5>🎞️ Episodes</h5>\r\n              <h2 className=\"display-4\">{data.stats?.episodes?.toLocaleString() || 0}</h2>\r\n              <small>Total Episodes</small>\r\n            </Card.Body>\r\n          </Card>\r\n        </Col>\r\n        <Col md={3}>\r\n          <Card bg=\"danger\" text=\"white\" className=\"mb-2 shadow\">\r\n            <Card.Body className=\"text-center\">\r\n              <h5>📺 Live TV</h5>\r\n              <h2 className=\"display-4\">{data.stats?.liveStreams?.toLocaleString() || 0}</h2>\r\n              <small>Live Channels</small>\r\n            </Card.Body>\r\n          </Card>\r\n        </Col>\r\n      </Row>\r\n\r\n      {/* Content Breakdown by Type */}\r\n      {data.contentByType?.length > 0 && (\r\n        <Row className=\"mb-4\">\r\n          <Col lg={12}>\r\n            <Card className=\"shadow-sm\">\r\n              <Card.Header className=\"bg-dark text-white\">\r\n                <h5 className=\"mb-0\">📊 Content Breakdown by Type</h5>\r\n              </Card.Header>\r\n              <Card.Body>\r\n                <Row>\r\n                  {data.contentByType.map((contentType, index) => (\r\n                    <Col md={4} key={`content-type-${contentType.type}-${index}`} className=\"mb-3\">\r\n                      <div className=\"text-center p-3 border rounded\">\r\n                        <h3 className=\"mb-1\">{contentType.count?.toLocaleString() || 0}</h3>\r\n                        <Badge bg=\"primary\" className=\"mb-2\">{contentType.type}</Badge>\r\n                        <p className=\"mb-0 text-secondary fw-normal\">{contentType.type || 'Unknown Type'}</p>\r\n                      </div>\r\n                    </Col>\r\n                  ))}\r\n                </Row>\r\n              </Card.Body>\r\n            </Card>\r\n          </Col>\r\n        </Row>\r\n      )}\r\n      \r\n      <Row className=\"mb-4\">\r\n        <Col lg={8}>\r\n          <Card className=\"shadow-sm\">\r\n            <Card.Header className=\"bg-danger text-white\">\r\n              <h5 className=\"mb-0\">📚 Recent Series {data.recentSeries?.length > 0 && `(${data.recentSeries.length})`}</h5>\r\n            </Card.Header>\r\n            <Card.Body>\r\n              {data.recentSeries?.length > 0 ? (\r\n                <Table striped hover>\r\n                  <thead>\r\n                    <tr>\r\n                      <th>📺 Series Name</th>\r\n                      <th>🎞️ Episodes</th>\r\n                      <th>📅 Added</th>\r\n                      <th>🏷️ Category</th>\r\n                    </tr>\r\n                  </thead>\r\n                  <tbody>\r\n                    {data.recentSeries.map((series, index) => (\r\n                      <tr key={`series-${series.title || series.id || index}`}>\r\n                        <td><strong>{series.title || 'Unknown Series'}</strong></td>\r\n                        <td><Badge bg=\"info\">{series.episode_count || 0} eps</Badge></td>\r\n                        <td>{series.year || 'Invalid Date'}</td>\r\n                        <td><Badge bg=\"secondary\">Unknown</Badge></td>\r\n                      </tr>\r\n                    ))}\r\n                  </tbody>\r\n                </Table>\r\n              ) : (\r\n                <p className=\"text-secondary text-center py-3 fw-normal\">\r\n                  {state.databaseConnection.isConnected \r\n                    ? 'No series found in database' \r\n                    : 'Connect to database to view series'\r\n                  }\r\n                </p>\r\n              )}\r\n            </Card.Body>\r\n          </Card>\r\n        </Col>\r\n\r\n        <Col lg={4}>\r\n          <Card className=\"shadow-sm h-100\">\r\n            <Card.Header className=\"bg-success text-white\">\r\n              <h5 className=\"mb-0\">⚡ Quick Actions</h5>\r\n            </Card.Header>\r\n            <Card.Body className=\"d-flex flex-column\">\r\n              <div className=\"mb-3\">\r\n                <Button variant=\"primary\" className=\"w-100 mb-2\" size=\"lg\">\r\n                  📥 Import M3U File\r\n                </Button>\r\n                <Button variant=\"info\" className=\"w-100 mb-2\">\r\n                  🔗 Manage Connections\r\n                </Button>\r\n                <Button variant=\"warning\" className=\"w-100 mb-2\">\r\n                  📊 View Import History\r\n                </Button>\r\n              </div>\r\n              \r\n              <div className=\"mt-auto\">\r\n                <h6>🖥️ Available Servers:</h6>\r\n                {data.servers?.length > 0 ? (\r\n                  <ul className=\"list-unstyled\">\r\n                    {(data.servers || []).slice(0, 3).map((server, index) => (\r\n                      <li key={`server-${server.server_name || index}-${index}`}>\r\n                        <Badge bg=\"outline-primary\" className=\"me-1\">📡</Badge>\r\n                        {server.server_name}\r\n                      </li>\r\n                    ))}\r\n                    {data.servers?.length > 3 && (\r\n                      <li><small className=\"text-secondary fw-normal\">+{data.servers.length - 3} more...</small></li>\r\n                    )}\r\n                  </ul>\r\n                ) : (\r\n                  <p className=\"text-muted\">No servers configured</p>\r\n                )}\r\n              </div>\r\n            </Card.Body>\r\n          </Card>\r\n        </Col>\r\n      </Row>\r\n\r\n      <Row>\r\n        <Col>\r\n          <Card className=\"shadow-sm\">\r\n            <Card.Header className=\"bg-info text-white\">\r\n              <h5 className=\"mb-0\">📊 System Status</h5>\r\n            </Card.Header>\r\n            <Card.Body>\r\n              <Row>\r\n                <Col md={4}>\r\n                  <h6>🗄️ Database Connection:</h6>\r\n                  <Badge bg={state.databaseConnection.isConnected ? 'success' : 'danger'}>\r\n                    {state.databaseConnection.isConnected ? '✅ Connected' : '❌ Disconnected'}\r\n                  </Badge>\r\n                  {state.databaseConnection.isConnected && (\r\n                    <div className=\"mt-2\">\r\n                      <small className=\"text-muted\">\r\n                        Host: {state.databaseConnection.host}<br/>\r\n                        Database: {state.databaseConnection.database}\r\n                      </small>\r\n                    </div>\r\n                  )}\r\n                </Col>\r\n                <Col md={4}>\r\n                  <h6>🌐 Backend Status:</h6>\r\n                  <Badge bg={state.backendStatus === 'connected' ? 'success' : 'warning'}>\r\n                    {state.backendStatus === 'connected' ? '✅ Online' : '⚠️ Checking...'}\r\n                  </Badge>\r\n                </Col>\r\n                <Col md={4}>\r\n                  <h6>🎯 Categories:</h6>\r\n                  <Badge bg=\"primary\">{data.categories?.length || 0} configured</Badge>\r\n                </Col>\r\n              </Row>\r\n            </Card.Body>\r\n          </Card>\r\n        </Col>\r\n      </Row>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Dashboard;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAEC,MAAM,EAAEC,KAAK,EAAEC,KAAK,EAAEC,OAAO,EAAEC,KAAK,QAAQ,iBAAiB;AACtF,SAASC,MAAM,QAAQ,uBAAuB;AAC9C,SAASC,WAAW,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErD,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,WAAA,EAAAC,oBAAA,EAAAC,YAAA,EAAAC,mBAAA,EAAAC,YAAA,EAAAC,qBAAA,EAAAC,YAAA,EAAAC,qBAAA,EAAAC,mBAAA,EAAAC,kBAAA,EAAAC,mBAAA,EAAAC,aAAA,EAAAC,cAAA,EAAAC,gBAAA;EACtB,MAAM;IAAEC;EAAM,CAAC,GAAGpB,MAAM,CAAC,CAAC;EAC1B,MAAM,CAACqB,aAAa,EAAEC,gBAAgB,CAAC,GAAG/B,QAAQ,CAAC;IACjDgC,YAAY,EAAE,EAAE;IAChBC,OAAO,EAAE,EAAE;IACXC,UAAU,EAAE,EAAE;IACdC,aAAa,EAAE;EACjB,CAAC,CAAC;EACF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGrC,QAAQ,CAAC,KAAK,CAAC;EAE7CD,SAAS,CAAC,MAAM;IACd,IAAIuC,MAAM,CAACC,QAAQ,EAAE;MACnBD,MAAM,CAACC,QAAQ,CAAC,+BAA+B,EAAE,SAAS,CAAC;IAC7D;;IAEA;IACA,IAAIV,KAAK,CAACW,kBAAkB,CAACC,WAAW,EAAE;MACxCC,iBAAiB,CAAC,CAAC;IACrB;EACF,CAAC,EAAE,CAACb,KAAK,CAACW,kBAAkB,CAACC,WAAW,CAAC,CAAC;EAE1C,MAAMC,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpCL,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,IAAIC,MAAM,CAACC,QAAQ,EAAE;QACnBD,MAAM,CAACC,QAAQ,CAAC,4CAA4C,EAAE,MAAM,CAAC;MACvE;MAEA,MAAMI,MAAM,GAAG,MAAMjC,WAAW,CAACkC,gBAAgB,CAAC,CAAC;MAEnD,IAAID,MAAM,CAACE,OAAO,EAAE;QAClBd,gBAAgB,CAACY,MAAM,CAACG,IAAI,CAAC;QAC7B,IAAIR,MAAM,CAACC,QAAQ,EAAE;UACnBD,MAAM,CAACC,QAAQ,CAAC,sCAAsC,EAAE,SAAS,CAAC;QACpE;MACF,CAAC,MAAM;QACLQ,OAAO,CAACC,KAAK,CAAC,+BAA+B,EAAEL,MAAM,CAACM,OAAO,CAAC;QAC9D,IAAIX,MAAM,CAACC,QAAQ,EAAE;UACnBD,MAAM,CAACC,QAAQ,CAAC,8BAA8BI,MAAM,CAACM,OAAO,EAAE,EAAE,OAAO,CAAC;QAC1E;MACF;IACF,CAAC,CAAC,OAAOD,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrD,IAAIV,MAAM,CAACC,QAAQ,EAAE;QACnBD,MAAM,CAACC,QAAQ,CAAC,sBAAsBS,KAAK,CAACC,OAAO,EAAE,EAAE,OAAO,CAAC;MACjE;IACF,CAAC,SAAS;MACRZ,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMa,aAAa,GAAGA,CAAA,KAAM;IAC1B,IAAIZ,MAAM,CAACC,QAAQ,EAAE;MACnBD,MAAM,CAACC,QAAQ,CAAC,iCAAiC,EAAE,MAAM,CAAC;IAC5D;IACAG,iBAAiB,CAAC,CAAC;EACrB,CAAC;;EAED;EACA,MAAMS,WAAW,GAAG;IAClBC,WAAW,EAAE,CAAC;IACdC,aAAa,EAAE,CAAC;IAChBC,YAAY,EAAE,CAAC;IACfpB,UAAU,EAAE,EAAE;IACdD,OAAO,EAAE,EAAE;IACXD,YAAY,EAAE;EAChB,CAAC;EAED,MAAMc,IAAI,GAAGhB,aAAa,IAAIqB,WAAW;EAEzC,oBACEvC,OAAA;IAAK2C,KAAK,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,QAAQ,EAAE;IAAO,CAAE;IAAAC,QAAA,gBAC9C9C,OAAA;MAAK+C,SAAS,EAAC,wDAAwD;MAAAD,QAAA,gBACrE9C,OAAA;QAAA8C,QAAA,gBACE9C,OAAA;UAAI+C,SAAS,EAAC,aAAa;UAAAD,QAAA,EAAC;QAAkC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EAClElC,KAAK,CAACW,kBAAkB,CAACC,WAAW,iBACnC7B,OAAA;UAAO+C,SAAS,EAAC,sBAAsB;UAAAD,QAAA,GAAC,sBACvB,EAAC7B,KAAK,CAACW,kBAAkB,CAACwB,IAAI,EAAC,GAAC,EAACnC,KAAK,CAACW,kBAAkB,CAACyB,QAAQ;QAAA;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5E,CACR;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACNnD,OAAA;QAAA8C,QAAA,GACGtB,OAAO,iBAAIxB,OAAA,CAACL,OAAO;UAAC2D,SAAS,EAAC,QAAQ;UAACC,IAAI,EAAC,IAAI;UAACR,SAAS,EAAC;QAAM;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACrEnD,OAAA,CAACR,MAAM;UACLgE,OAAO,EAAC,SAAS;UACjBD,IAAI,EAAC,IAAI;UACTE,OAAO,EAAEnB,aAAc;UACvBoB,QAAQ,EAAElC,OAAO,IAAI,CAACP,KAAK,CAACW,kBAAkB,CAACC,WAAY;UAAAiB,QAAA,EAC5D;QAED;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAEL,CAAClC,KAAK,CAACW,kBAAkB,CAACC,WAAW,iBACpC7B,OAAA,CAACJ,KAAK;MAAC4D,OAAO,EAAC,SAAS;MAACT,SAAS,EAAC,MAAM;MAAAD,QAAA,gBACvC9C,OAAA,CAACJ,KAAK,CAAC+D,OAAO;QAAAb,QAAA,EAAC;MAAyB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAe,CAAC,eACxDnD,OAAA;QAAA8C,QAAA,GAAG,sCAAoC,eAAA9C,OAAA;UAAA8C,QAAA,EAAQ;QAAW;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,2BAAuB;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzF,CACR,eAEDnD,OAAA,CAACX,GAAG;MAAC0D,SAAS,EAAC,MAAM;MAAAD,QAAA,gBACnB9C,OAAA,CAACV,GAAG;QAACsE,EAAE,EAAE,CAAE;QAAAd,QAAA,eACT9C,OAAA,CAACT,IAAI;UAACsE,EAAE,EAAC,SAAS;UAACC,IAAI,EAAC,OAAO;UAACf,SAAS,EAAC,aAAa;UAAAD,QAAA,eACrD9C,OAAA,CAACT,IAAI,CAACwE,IAAI;YAAChB,SAAS,EAAC,aAAa;YAAAD,QAAA,gBAChC9C,OAAA;cAAA8C,QAAA,EAAI;YAAY;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACrBnD,OAAA;cAAI+C,SAAS,EAAC,WAAW;cAAAD,QAAA,EAAE,EAAA3C,WAAA,GAAA+B,IAAI,CAAC8B,KAAK,cAAA7D,WAAA,wBAAAC,oBAAA,GAAVD,WAAA,CAAY8D,QAAQ,cAAA7D,oBAAA,uBAApBA,oBAAA,CAAsB8D,cAAc,CAAC,CAAC,KAAI;YAAC;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC5EnD,OAAA;cAAA8C,QAAA,EAAO;YAAgB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNnD,OAAA,CAACV,GAAG;QAACsE,EAAE,EAAE,CAAE;QAAAd,QAAA,eACT9C,OAAA,CAACT,IAAI;UAACsE,EAAE,EAAC,MAAM;UAACC,IAAI,EAAC,OAAO;UAACf,SAAS,EAAC,aAAa;UAAAD,QAAA,eAClD9C,OAAA,CAACT,IAAI,CAACwE,IAAI;YAAChB,SAAS,EAAC,aAAa;YAAAD,QAAA,gBAChC9C,OAAA;cAAA8C,QAAA,EAAI;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAClBnD,OAAA;cAAI+C,SAAS,EAAC,WAAW;cAAAD,QAAA,EAAE,EAAAzC,YAAA,GAAA6B,IAAI,CAAC8B,KAAK,cAAA3D,YAAA,wBAAAC,mBAAA,GAAVD,YAAA,CAAY8D,MAAM,cAAA7D,mBAAA,uBAAlBA,mBAAA,CAAoB4D,cAAc,CAAC,CAAC,KAAI;YAAC;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC1EnD,OAAA;cAAA8C,QAAA,EAAO;YAAgB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNnD,OAAA,CAACV,GAAG;QAACsE,EAAE,EAAE,CAAE;QAAAd,QAAA,eACT9C,OAAA,CAACT,IAAI;UAACsE,EAAE,EAAC,SAAS;UAACC,IAAI,EAAC,OAAO;UAACf,SAAS,EAAC,aAAa;UAAAD,QAAA,eACrD9C,OAAA,CAACT,IAAI,CAACwE,IAAI;YAAChB,SAAS,EAAC,aAAa;YAAAD,QAAA,gBAChC9C,OAAA;cAAA8C,QAAA,EAAI;YAAY;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACrBnD,OAAA;cAAI+C,SAAS,EAAC,WAAW;cAAAD,QAAA,EAAE,EAAAvC,YAAA,GAAA2B,IAAI,CAAC8B,KAAK,cAAAzD,YAAA,wBAAAC,qBAAA,GAAVD,YAAA,CAAY6D,QAAQ,cAAA5D,qBAAA,uBAApBA,qBAAA,CAAsB0D,cAAc,CAAC,CAAC,KAAI;YAAC;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC5EnD,OAAA;cAAA8C,QAAA,EAAO;YAAc;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNnD,OAAA,CAACV,GAAG;QAACsE,EAAE,EAAE,CAAE;QAAAd,QAAA,eACT9C,OAAA,CAACT,IAAI;UAACsE,EAAE,EAAC,QAAQ;UAACC,IAAI,EAAC,OAAO;UAACf,SAAS,EAAC,aAAa;UAAAD,QAAA,eACpD9C,OAAA,CAACT,IAAI,CAACwE,IAAI;YAAChB,SAAS,EAAC,aAAa;YAAAD,QAAA,gBAChC9C,OAAA;cAAA8C,QAAA,EAAI;YAAU;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACnBnD,OAAA;cAAI+C,SAAS,EAAC,WAAW;cAAAD,QAAA,EAAE,EAAArC,YAAA,GAAAyB,IAAI,CAAC8B,KAAK,cAAAvD,YAAA,wBAAAC,qBAAA,GAAVD,YAAA,CAAY4D,WAAW,cAAA3D,qBAAA,uBAAvBA,qBAAA,CAAyBwD,cAAc,CAAC,CAAC,KAAI;YAAC;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC/EnD,OAAA;cAAA8C,QAAA,EAAO;YAAa;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGL,EAAAxC,mBAAA,GAAAuB,IAAI,CAACX,aAAa,cAAAZ,mBAAA,uBAAlBA,mBAAA,CAAoB2D,MAAM,IAAG,CAAC,iBAC7BtE,OAAA,CAACX,GAAG;MAAC0D,SAAS,EAAC,MAAM;MAAAD,QAAA,eACnB9C,OAAA,CAACV,GAAG;QAACiF,EAAE,EAAE,EAAG;QAAAzB,QAAA,eACV9C,OAAA,CAACT,IAAI;UAACwD,SAAS,EAAC,WAAW;UAAAD,QAAA,gBACzB9C,OAAA,CAACT,IAAI,CAACiF,MAAM;YAACzB,SAAS,EAAC,oBAAoB;YAAAD,QAAA,eACzC9C,OAAA;cAAI+C,SAAS,EAAC,MAAM;cAAAD,QAAA,EAAC;YAA4B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C,CAAC,eACdnD,OAAA,CAACT,IAAI,CAACwE,IAAI;YAAAjB,QAAA,eACR9C,OAAA,CAACX,GAAG;cAAAyD,QAAA,EACDZ,IAAI,CAACX,aAAa,CAACkD,GAAG,CAAC,CAACC,WAAW,EAAEC,KAAK;gBAAA,IAAAC,kBAAA;gBAAA,oBACzC5E,OAAA,CAACV,GAAG;kBAACsE,EAAE,EAAE,CAAE;kBAAmDb,SAAS,EAAC,MAAM;kBAAAD,QAAA,eAC5E9C,OAAA;oBAAK+C,SAAS,EAAC,gCAAgC;oBAAAD,QAAA,gBAC7C9C,OAAA;sBAAI+C,SAAS,EAAC,MAAM;sBAAAD,QAAA,EAAE,EAAA8B,kBAAA,GAAAF,WAAW,CAACG,KAAK,cAAAD,kBAAA,uBAAjBA,kBAAA,CAAmBV,cAAc,CAAC,CAAC,KAAI;oBAAC;sBAAAlB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACpEnD,OAAA,CAACN,KAAK;sBAACmE,EAAE,EAAC,SAAS;sBAACd,SAAS,EAAC,MAAM;sBAAAD,QAAA,EAAE4B,WAAW,CAACI;oBAAI;sBAAA9B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eAC/DnD,OAAA;sBAAG+C,SAAS,EAAC,+BAA+B;sBAAAD,QAAA,EAAE4B,WAAW,CAACI,IAAI,IAAI;oBAAc;sBAAA9B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClF;gBAAC,GALS,gBAAgBuB,WAAW,CAACI,IAAI,IAAIH,KAAK,EAAE;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAMvD,CAAC;cAAA,CACP;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAEDnD,OAAA,CAACX,GAAG;MAAC0D,SAAS,EAAC,MAAM;MAAAD,QAAA,gBACnB9C,OAAA,CAACV,GAAG;QAACiF,EAAE,EAAE,CAAE;QAAAzB,QAAA,eACT9C,OAAA,CAACT,IAAI;UAACwD,SAAS,EAAC,WAAW;UAAAD,QAAA,gBACzB9C,OAAA,CAACT,IAAI,CAACiF,MAAM;YAACzB,SAAS,EAAC,sBAAsB;YAAAD,QAAA,eAC3C9C,OAAA;cAAI+C,SAAS,EAAC,MAAM;cAAAD,QAAA,GAAC,6BAAiB,EAAC,EAAAlC,kBAAA,GAAAsB,IAAI,CAACd,YAAY,cAAAR,kBAAA,uBAAjBA,kBAAA,CAAmB0D,MAAM,IAAG,CAAC,IAAI,IAAIpC,IAAI,CAACd,YAAY,CAACkD,MAAM,GAAG;YAAA;cAAAtB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClG,CAAC,eACdnD,OAAA,CAACT,IAAI,CAACwE,IAAI;YAAAjB,QAAA,EACP,EAAAjC,mBAAA,GAAAqB,IAAI,CAACd,YAAY,cAAAP,mBAAA,uBAAjBA,mBAAA,CAAmByD,MAAM,IAAG,CAAC,gBAC5BtE,OAAA,CAACP,KAAK;cAACsF,OAAO;cAACC,KAAK;cAAAlC,QAAA,gBAClB9C,OAAA;gBAAA8C,QAAA,eACE9C,OAAA;kBAAA8C,QAAA,gBACE9C,OAAA;oBAAA8C,QAAA,EAAI;kBAAc;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACvBnD,OAAA;oBAAA8C,QAAA,EAAI;kBAAY;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACrBnD,OAAA;oBAAA8C,QAAA,EAAI;kBAAQ;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACjBnD,OAAA;oBAAA8C,QAAA,EAAI;kBAAY;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,eACRnD,OAAA;gBAAA8C,QAAA,EACGZ,IAAI,CAACd,YAAY,CAACqD,GAAG,CAAC,CAACQ,MAAM,EAAEN,KAAK,kBACnC3E,OAAA;kBAAA8C,QAAA,gBACE9C,OAAA;oBAAA8C,QAAA,eAAI9C,OAAA;sBAAA8C,QAAA,EAASmC,MAAM,CAACC,KAAK,IAAI;oBAAgB;sBAAAlC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAS;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC5DnD,OAAA;oBAAA8C,QAAA,eAAI9C,OAAA,CAACN,KAAK;sBAACmE,EAAE,EAAC,MAAM;sBAAAf,QAAA,GAAEmC,MAAM,CAACE,aAAa,IAAI,CAAC,EAAC,MAAI;oBAAA;sBAAAnC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACjEnD,OAAA;oBAAA8C,QAAA,EAAKmC,MAAM,CAACG,IAAI,IAAI;kBAAc;oBAAApC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACxCnD,OAAA;oBAAA8C,QAAA,eAAI9C,OAAA,CAACN,KAAK;sBAACmE,EAAE,EAAC,WAAW;sBAAAf,QAAA,EAAC;oBAAO;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA,GAJvC,UAAU8B,MAAM,CAACC,KAAK,IAAID,MAAM,CAACI,EAAE,IAAIV,KAAK,EAAE;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAKnD,CACL;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,gBAERnD,OAAA;cAAG+C,SAAS,EAAC,2CAA2C;cAAAD,QAAA,EACrD7B,KAAK,CAACW,kBAAkB,CAACC,WAAW,GACjC,6BAA6B,GAC7B;YAAoC;cAAAmB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEvC;UACJ;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAENnD,OAAA,CAACV,GAAG;QAACiF,EAAE,EAAE,CAAE;QAAAzB,QAAA,eACT9C,OAAA,CAACT,IAAI;UAACwD,SAAS,EAAC,iBAAiB;UAAAD,QAAA,gBAC/B9C,OAAA,CAACT,IAAI,CAACiF,MAAM;YAACzB,SAAS,EAAC,uBAAuB;YAAAD,QAAA,eAC5C9C,OAAA;cAAI+C,SAAS,EAAC,MAAM;cAAAD,QAAA,EAAC;YAAe;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B,CAAC,eACdnD,OAAA,CAACT,IAAI,CAACwE,IAAI;YAAChB,SAAS,EAAC,oBAAoB;YAAAD,QAAA,gBACvC9C,OAAA;cAAK+C,SAAS,EAAC,MAAM;cAAAD,QAAA,gBACnB9C,OAAA,CAACR,MAAM;gBAACgE,OAAO,EAAC,SAAS;gBAACT,SAAS,EAAC,YAAY;gBAACQ,IAAI,EAAC,IAAI;gBAAAT,QAAA,EAAC;cAE3D;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTnD,OAAA,CAACR,MAAM;gBAACgE,OAAO,EAAC,MAAM;gBAACT,SAAS,EAAC,YAAY;gBAAAD,QAAA,EAAC;cAE9C;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTnD,OAAA,CAACR,MAAM;gBAACgE,OAAO,EAAC,SAAS;gBAACT,SAAS,EAAC,YAAY;gBAAAD,QAAA,EAAC;cAEjD;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAENnD,OAAA;cAAK+C,SAAS,EAAC,SAAS;cAAAD,QAAA,gBACtB9C,OAAA;gBAAA8C,QAAA,EAAI;cAAsB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,EAC9B,EAAArC,aAAA,GAAAoB,IAAI,CAACb,OAAO,cAAAP,aAAA,uBAAZA,aAAA,CAAcwD,MAAM,IAAG,CAAC,gBACvBtE,OAAA;gBAAI+C,SAAS,EAAC,eAAe;gBAAAD,QAAA,GAC1B,CAACZ,IAAI,CAACb,OAAO,IAAI,EAAE,EAAEiE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACb,GAAG,CAAC,CAACc,MAAM,EAAEZ,KAAK,kBAClD3E,OAAA;kBAAA8C,QAAA,gBACE9C,OAAA,CAACN,KAAK;oBAACmE,EAAE,EAAC,iBAAiB;oBAACd,SAAS,EAAC,MAAM;oBAAAD,QAAA,EAAC;kBAAE;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,EACtDoC,MAAM,CAACC,WAAW;gBAAA,GAFZ,UAAUD,MAAM,CAACC,WAAW,IAAIb,KAAK,IAAIA,KAAK,EAAE;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAGrD,CACL,CAAC,EACD,EAAApC,cAAA,GAAAmB,IAAI,CAACb,OAAO,cAAAN,cAAA,uBAAZA,cAAA,CAAcuD,MAAM,IAAG,CAAC,iBACvBtE,OAAA;kBAAA8C,QAAA,eAAI9C,OAAA;oBAAO+C,SAAS,EAAC,0BAA0B;oBAAAD,QAAA,GAAC,GAAC,EAACZ,IAAI,CAACb,OAAO,CAACiD,MAAM,GAAG,CAAC,EAAC,UAAQ;kBAAA;oBAAAtB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAC/F;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,gBAELnD,OAAA;gBAAG+C,SAAS,EAAC,YAAY;gBAAAD,QAAA,EAAC;cAAqB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CACnD;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENnD,OAAA,CAACX,GAAG;MAAAyD,QAAA,eACF9C,OAAA,CAACV,GAAG;QAAAwD,QAAA,eACF9C,OAAA,CAACT,IAAI;UAACwD,SAAS,EAAC,WAAW;UAAAD,QAAA,gBACzB9C,OAAA,CAACT,IAAI,CAACiF,MAAM;YAACzB,SAAS,EAAC,oBAAoB;YAAAD,QAAA,eACzC9C,OAAA;cAAI+C,SAAS,EAAC,MAAM;cAAAD,QAAA,EAAC;YAAgB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B,CAAC,eACdnD,OAAA,CAACT,IAAI,CAACwE,IAAI;YAAAjB,QAAA,eACR9C,OAAA,CAACX,GAAG;cAAAyD,QAAA,gBACF9C,OAAA,CAACV,GAAG;gBAACsE,EAAE,EAAE,CAAE;gBAAAd,QAAA,gBACT9C,OAAA;kBAAA8C,QAAA,EAAI;gBAAwB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACjCnD,OAAA,CAACN,KAAK;kBAACmE,EAAE,EAAE5C,KAAK,CAACW,kBAAkB,CAACC,WAAW,GAAG,SAAS,GAAG,QAAS;kBAAAiB,QAAA,EACpE7B,KAAK,CAACW,kBAAkB,CAACC,WAAW,GAAG,aAAa,GAAG;gBAAgB;kBAAAmB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnE,CAAC,EACPlC,KAAK,CAACW,kBAAkB,CAACC,WAAW,iBACnC7B,OAAA;kBAAK+C,SAAS,EAAC,MAAM;kBAAAD,QAAA,eACnB9C,OAAA;oBAAO+C,SAAS,EAAC,YAAY;oBAAAD,QAAA,GAAC,QACtB,EAAC7B,KAAK,CAACW,kBAAkB,CAACwB,IAAI,eAACpD,OAAA;sBAAAgD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,cAChC,EAAClC,KAAK,CAACW,kBAAkB,CAACyB,QAAQ;kBAAA;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACNnD,OAAA,CAACV,GAAG;gBAACsE,EAAE,EAAE,CAAE;gBAAAd,QAAA,gBACT9C,OAAA;kBAAA8C,QAAA,EAAI;gBAAkB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC3BnD,OAAA,CAACN,KAAK;kBAACmE,EAAE,EAAE5C,KAAK,CAACwE,aAAa,KAAK,WAAW,GAAG,SAAS,GAAG,SAAU;kBAAA3C,QAAA,EACpE7B,KAAK,CAACwE,aAAa,KAAK,WAAW,GAAG,UAAU,GAAG;gBAAgB;kBAAAzC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/D,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACNnD,OAAA,CAACV,GAAG;gBAACsE,EAAE,EAAE,CAAE;gBAAAd,QAAA,gBACT9C,OAAA;kBAAA8C,QAAA,EAAI;gBAAc;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACvBnD,OAAA,CAACN,KAAK;kBAACmE,EAAE,EAAC,SAAS;kBAAAf,QAAA,GAAE,EAAA9B,gBAAA,GAAAkB,IAAI,CAACZ,UAAU,cAAAN,gBAAA,uBAAfA,gBAAA,CAAiBsD,MAAM,KAAI,CAAC,EAAC,aAAW;gBAAA;kBAAAtB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACjD,EAAA,CA9RID,SAAS;EAAA,QACKJ,MAAM;AAAA;AAAA6F,EAAA,GADpBzF,SAAS;AAgSf,eAAeA,SAAS;AAAC,IAAAyF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}