﻿{
    "success":  true,
    "data":  {
                 "has_categories_table":  true,
                 "categories_table_found":  [
                                                "streams_categories",
                                                "watch_categories"
                                            ],
                 "total_categories":  0,
                 "sample_categories":  [

                                       ],
                 "category_usage":  [
                                        {
                                            "category_id":  "[1964]",
                                            "usage_count":  2297,
                                            "sample_stream":  "¡A<PERSON>, <PERSON><PERSON>!"
                                        },
                                        {
                                            "category_id":  "[1763]",
                                            "usage_count":  2242,
                                            "sample_stream":  "¡A volar, joven!"
                                        },
                                        {
                                            "category_id":  "[1762]",
                                            "usage_count":  2142,
                                            "sample_stream":  "¡A Ganar!"
                                        },
                                        {
                                            "category_id":  "[1767]",
                                            "usage_count":  1333,
                                            "sample_stream":  "¡Pooka!"
                                        },
                                        {
                                            "category_id":  "[1761]",
                                            "usage_count":  1241,
                                            "sample_stream":  "¡Bang Bang! Viviendo sin reglas"
                                        },
                                        {
                                            "category_id":  "[1764]",
                                            "usage_count":  1144,
                                            "sample_stream":  "¡Esto es guerra!"
                                        },
                                        {
                                            "category_id":  "[1766]",
                                            "usage_count":  960,
                                            "sample_stream":  "¡Madre!"
                                        },
                                        {
                                            "category_id":  "[1706]",
                                            "usage_count":  724,
                                            "sample_stream":  "¡Aloha Scooby-Doo!"
                                        },
                                        {
                                            "category_id":  "[1796]",
                                            "usage_count":  617,
                                            "sample_stream":  "¡La Sociedad de la Virtud Salva la Navidad!"
                                        },
                                        {
                                            "category_id":  "[1770]",
                                            "usage_count":  596,
                                            "sample_stream":  "¡Sobreviven!"
                                        }
                                    ],
                 "all_tables":  [
                                    "access_codes",
                                    "blocked_asns",
                                    "blocked_ips",
                                    "blocked_isps",
                                    "blocked_uas",
                                    "bouquets",
                                    "crontab",
                                    "detect_restream",
                                    "detect_restream_logs",
                                    "enigma2_actions",
                                    "enigma2_devices",
                                    "epg",
                                    "epg_api",
                                    "epg_channels",
                                    "epg_data",
                                    "epg_languages",
                                    "hmac_keys",
                                    "lines",
                                    "lines_activity",
                                    "lines_divergence",
                                    "lines_live",
                                    "lines_logs",
                                    "login_logs",
                                    "mag_claims",
                                    "mag_devices",
                                    "mag_events",
                                    "mag_logs",
                                    "mysql_syslog",
                                    "ondemand_check",
                                    "output_devices",
                                    "output_formats",
                                    "panel_logs",
                                    "panel_stats",
                                    "profiles",
                                    "providers",
                                    "providers_streams",
                                    "queue",
                                    "recordings",
                                    "rtmp_ips",
                                    "servers",
                                    "servers_stats",
                                    "settings",
                                    "signals",
                                    "streams",
                                    "streams_arguments",
                                    "streams_categories",
                                    "streams_episodes",
                                    "streams_errors",
                                    "streams_logs",
                                    "streams_options",
                                    "streams_series",
                                    "streams_servers",
                                    "streams_stats",
                                    "streams_types",
                                    "syskill_log",
                                    "tickets",
                                    "tickets_replies",
                                    "users",
                                    "users_credits_logs",
                                    "users_groups",
                                    "users_logs",
                                    "users_packages",
                                    "users_subreseller",
                                    "watch_categories",
                                    "watch_folders",
                                    "watch_logs",
                                    "watch_refresh"
                                ]
             }
}
