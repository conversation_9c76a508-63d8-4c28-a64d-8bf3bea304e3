import React, { useEffect, useState } from 'react';
import { <PERSON>, Col, Card, But<PERSON>, Table, Badge, Spinner, Alert } from 'react-bootstrap';
import { useApp } from '../context/AppContext';
import { databaseAPI } from '../services/apiService';

const Dashboard = () => {
  const { state } = useApp();
  const [dashboardData, setDashboardData] = useState({
    recentSeries: [],
    servers: [],
    categories: [],
    contentByType: []
  });
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (window.debugLog) {
      window.debugLog('📊 Dashboard component loaded', 'success');
    }
    
    // Cargar datos si hay conexión a BD
    if (state.databaseConnection.isConnected) {
      loadDashboardData();
    }
  }, [state.databaseConnection.isConnected]);

  const loadDashboardData = async () => {
    setLoading(true);
    try {
      if (window.debugLog) {
        window.debugLog('📈 Loading dashboard data from database...', 'info');
      }
      
      const result = await databaseAPI.getDashboardData();
      
      if (result.success) {
        setDashboardData(result.data);
        if (window.debugLog) {
          window.debugLog('✅ Dashboard data loaded successfully', 'success');
        }
      } else {
        console.error('Error loading dashboard data:', result.message);
        if (window.debugLog) {
          window.debugLog(`❌ Error loading dashboard: ${result.message}`, 'error');
        }
      }
    } catch (error) {
      console.error('Error loading dashboard data:', error);
      if (window.debugLog) {
        window.debugLog(`❌ Dashboard error: ${error.message}`, 'error');
      }
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = () => {
    if (window.debugLog) {
      window.debugLog('🔄 Refreshing dashboard data...', 'info');
    }
    loadDashboardData();
  };

  // Datos por defecto si no hay conexión
  const defaultData = {
    totalSeries: 0,
    totalEpisodes: 0,
    totalStreams: 0,
    categories: [],
    servers: [],
    recentSeries: []
  };

  const data = dashboardData || defaultData;

  return (
    <div style={{ width: '100%', maxWidth: 'none' }}>
      <div className="d-flex justify-content-between align-items-center mb-4">
        <div>
          <h1 className="text-danger">📊 Dashboard - RGS IMPORT TOOL XUI</h1>
          {state.databaseConnection.isConnected && (
            <small className="text-success fw-bold">
              ✅ Connected to {state.databaseConnection.host}:{state.databaseConnection.database}
            </small>
          )}
        </div>
        <div>
          {loading && <Spinner animation="border" size="sm" className="me-2" />}
          <Button 
            variant="success" 
            size="sm" 
            onClick={handleRefresh}
            disabled={loading || !state.databaseConnection.isConnected}
          >
            🔄 Refresh Data
          </Button>
        </div>
      </div>

      {!state.databaseConnection.isConnected && (
        <Alert variant="warning" className="mb-4">
          <Alert.Heading>🔗 No Database Connection</Alert.Heading>
          <p>Connect to your XUI database in the <strong>Connections</strong> tab to view real data.</p>
        </Alert>
      )}
      
      <Row className="mb-4">
        <Col md={3}>
          <Card bg="success" text="white" className="mb-2 shadow">
            <Card.Body className="text-center">
              <h5>📺 TV Series</h5>
              <h2 className="display-4">{data.stats?.tvSeries?.toLocaleString() || 0}</h2>
              <small>Series Available</small>
            </Card.Body>
          </Card>
        </Col>
        <Col md={3}>
          <Card bg="info" text="white" className="mb-2 shadow">
            <Card.Body className="text-center">
              <h5>🎬 Movies</h5>
              <h2 className="display-4">{data.stats?.movies?.toLocaleString() || 0}</h2>
              <small>Movies Available</small>
            </Card.Body>
          </Card>
        </Col>
        <Col md={3}>
          <Card bg="warning" text="white" className="mb-2 shadow">
            <Card.Body className="text-center">
              <h5>🎞️ Episodes</h5>
              <h2 className="display-4">{data.stats?.episodes?.toLocaleString() || 0}</h2>
              <small>Total Episodes</small>
            </Card.Body>
          </Card>
        </Col>
        <Col md={3}>
          <Card bg="danger" text="white" className="mb-2 shadow">
            <Card.Body className="text-center">
              <h5>📺 Live TV</h5>
              <h2 className="display-4">{data.stats?.liveStreams?.toLocaleString() || 0}</h2>
              <small>Live Channels</small>
            </Card.Body>
          </Card>
        </Col>
      </Row>

      {/* Content Breakdown by Type */}
      {data.contentByType?.length > 0 && (
        <Row className="mb-4">
          <Col lg={12}>
            <Card className="shadow-sm">
              <Card.Header className="bg-dark text-white">
                <h5 className="mb-0">📊 Content Breakdown by Type</h5>
              </Card.Header>
              <Card.Body>
                <Row>
                  {data.contentByType.map((contentType, index) => (
                    <Col md={4} key={`content-type-${contentType.type}-${index}`} className="mb-3">
                      <div className="text-center p-3 border rounded">
                        <h3 className="mb-1">{contentType.count?.toLocaleString() || 0}</h3>
                        <Badge bg="primary" className="mb-2">{contentType.type}</Badge>
                        <p className="mb-0 text-secondary fw-normal">{contentType.type || 'Unknown Type'}</p>
                      </div>
                    </Col>
                  ))}
                </Row>
              </Card.Body>
            </Card>
          </Col>
        </Row>
      )}
      
      <Row className="mb-4">
        <Col lg={8}>
          <Card className="shadow-sm">
            <Card.Header className="bg-danger text-white">
              <h5 className="mb-0">📚 Recent Series {data.recentSeries?.length > 0 && `(${data.recentSeries.length})`}</h5>
            </Card.Header>
            <Card.Body>
              {data.recentSeries?.length > 0 ? (
                <Table striped hover>
                  <thead>
                    <tr>
                      <th>📺 Series Name</th>
                      <th>🎞️ Episodes</th>
                      <th>📅 Added</th>
                      <th>🏷️ Category</th>
                    </tr>
                  </thead>
                  <tbody>
                    {data.recentSeries.map((series, index) => (
                      <tr key={`series-${series.title || series.id || index}`}>
                        <td><strong>{series.title || 'Unknown Series'}</strong></td>
                        <td><Badge bg="info">{series.episode_count || 0} eps</Badge></td>
                        <td>{series.year || 'Invalid Date'}</td>
                        <td><Badge bg="secondary">Unknown</Badge></td>
                      </tr>
                    ))}
                  </tbody>
                </Table>
              ) : (
                <p className="text-secondary text-center py-3 fw-normal">
                  {state.databaseConnection.isConnected 
                    ? 'No series found in database' 
                    : 'Connect to database to view series'
                  }
                </p>
              )}
            </Card.Body>
          </Card>
        </Col>

        <Col lg={4}>
          <Card className="shadow-sm h-100">
            <Card.Header className="bg-success text-white">
              <h5 className="mb-0">⚡ Quick Actions</h5>
            </Card.Header>
            <Card.Body className="d-flex flex-column">
              <div className="mb-3">
                <Button variant="primary" className="w-100 mb-2" size="lg">
                  📥 Import M3U File
                </Button>
                <Button variant="info" className="w-100 mb-2">
                  🔗 Manage Connections
                </Button>
                <Button variant="warning" className="w-100 mb-2">
                  📊 View Import History
                </Button>
              </div>
              
              <div className="mt-auto">
                <h6>🖥️ Available Servers:</h6>
                {data.servers?.length > 0 ? (
                  <ul className="list-unstyled">
                    {(data.servers || []).slice(0, 3).map((server, index) => (
                      <li key={`server-${server.server_name || index}-${index}`}>
                        <Badge bg="outline-primary" className="me-1">📡</Badge>
                        {server.server_name}
                      </li>
                    ))}
                    {data.servers?.length > 3 && (
                      <li><small className="text-secondary fw-normal">+{data.servers.length - 3} more...</small></li>
                    )}
                  </ul>
                ) : (
                  <p className="text-secondary fw-normal">No servers configured</p>
                )}
              </div>
            </Card.Body>
          </Card>
        </Col>
      </Row>

      <Row>
        <Col>
          <Card className="shadow-sm">
            <Card.Header className="bg-info text-white">
              <h5 className="mb-0">📊 System Status</h5>
            </Card.Header>
            <Card.Body>
              <Row>
                <Col md={4}>
                  <h6>🗄️ Database Connection:</h6>
                  <Badge bg={state.databaseConnection.isConnected ? 'success' : 'danger'}>
                    {state.databaseConnection.isConnected ? '✅ Connected' : '❌ Disconnected'}
                  </Badge>
                  {state.databaseConnection.isConnected && (
                    <div className="mt-2">
                      <small className="text-secondary fw-normal">
                        Host: {state.databaseConnection.host}<br/>
                        Database: {state.databaseConnection.database}
                      </small>
                    </div>
                  )}
                </Col>
                <Col md={4}>
                  <h6>🌐 Backend Status:</h6>
                  <Badge bg={state.backendStatus === 'connected' ? 'success' : 'warning'}>
                    {state.backendStatus === 'connected' ? '✅ Online' : '⚠️ Checking...'}
                  </Badge>
                </Col>
                <Col md={4}>
                  <h6>🎯 Categories:</h6>
                  <Badge bg="primary">{data.categories?.length || 0} configured</Badge>
                </Col>
              </Row>
            </Card.Body>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default Dashboard;
