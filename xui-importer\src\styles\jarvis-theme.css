/* TEMA JARVIS - XUI IMPORTER */
/* Inspirado en la interfaz de JARVIS de Iron Man */

/* Variables de colores JARVIS */
:root {
  --jarvis-bg-primary: #0a0a0a;
  --jarvis-bg-secondary: #1a1a2e;
  --jarvis-bg-tertiary: #16213e;
  --jarvis-cyan: #00d4ff;
  --jarvis-cyan-bright: #00ffff;
  --jarvis-cyan-dark: #0099cc;
  --jarvis-cyan-glow: rgba(0, 212, 255, 0.3);
  --jarvis-cyan-light: rgba(0, 212, 255, 0.1);
  --jarvis-text-primary: #ffffff;
  --jarvis-text-secondary: #b8e6ff;
  --jarvis-text-muted: #7fb3d3;
  --jarvis-border: #00d4ff;
  --jarvis-success: #00ff88;
  --jarvis-warning: #ffaa00;
  --jarvis-danger: #ff4444;
  --jarvis-info: #00d4ff;
}

/* Fuente JARVIS */
* {
  font-family: 'Courier New', 'Consolas', 'Monaco', monospace !important;
}

/* Fondo principal con efecto de circuitos */
html, body, #root {
  background: var(--jarvis-bg-primary) !important;
  background-image: 
    radial-gradient(circle at 20% 50%, rgba(0, 212, 255, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(0, 212, 255, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 40% 80%, rgba(0, 212, 255, 0.05) 0%, transparent 50%);
  color: var(--jarvis-text-primary) !important;
  margin: 0 !important;
  padding: 0 !important;
  min-height: 100vh !important;
}

/* Animación de glow pulsante */
@keyframes jarvis-glow {
  0%, 100% { box-shadow: 0 0 5px var(--jarvis-cyan), 0 0 10px var(--jarvis-cyan), 0 0 15px var(--jarvis-cyan); }
  50% { box-shadow: 0 0 10px var(--jarvis-cyan), 0 0 20px var(--jarvis-cyan), 0 0 30px var(--jarvis-cyan); }
}

@keyframes jarvis-pulse {
  0%, 100% { opacity: 0.8; }
  50% { opacity: 1; }
}

@keyframes jarvis-scan {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* Contenedores principales */
.container, .container-fluid {
  background-color: transparent !important;
  color: var(--jarvis-text-primary) !important;
}

/* Cards estilo JARVIS */
.card {
  background: linear-gradient(145deg, var(--jarvis-bg-secondary), var(--jarvis-bg-tertiary)) !important;
  border: 2px solid var(--jarvis-cyan) !important;
  border-radius: 10px !important;
  color: var(--jarvis-text-primary) !important;
  box-shadow: 
    0 0 20px var(--jarvis-cyan-glow),
    inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
  position: relative !important;
  overflow: hidden !important;
  transition: all 0.3s ease !important;
}

.card:hover {
  border-color: var(--jarvis-cyan-bright) !important;
  box-shadow: 
    0 0 30px var(--jarvis-cyan-glow),
    0 0 40px var(--jarvis-cyan-glow),
    inset 0 1px 0 rgba(255, 255, 255, 0.2) !important;
  transform: translateY(-2px) !important;
}

.card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg, transparent, var(--jarvis-cyan-bright), transparent);
  animation: jarvis-scan 3s infinite;
}

.card-header {
  background: linear-gradient(145deg, var(--jarvis-bg-tertiary), var(--jarvis-bg-secondary)) !important;
  border-bottom: 1px solid var(--jarvis-cyan) !important;
  color: var(--jarvis-cyan-bright) !important;
  font-weight: 600 !important;
  text-transform: uppercase !important;
  letter-spacing: 1px !important;
  position: relative !important;
}

.card-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--jarvis-cyan), transparent);
  animation: jarvis-pulse 2s infinite;
}

.card-body {
  background: transparent !important;
  color: var(--jarvis-text-primary) !important;
}

/* Formularios estilo JARVIS */
.form-control, input, textarea, select {
  background: rgba(22, 33, 62, 0.8) !important;
  border: 1px solid var(--jarvis-cyan) !important;
  border-radius: 5px !important;
  color: var(--jarvis-text-primary) !important;
  font-family: 'Courier New', 'Consolas', monospace !important;
  transition: all 0.3s ease !important;
}

.form-control:focus, input:focus, textarea:focus, select:focus {
  background: rgba(22, 33, 62, 1) !important;
  border-color: var(--jarvis-cyan-bright) !important;
  box-shadow: 
    0 0 0 0.2rem var(--jarvis-cyan-light),
    0 0 10px var(--jarvis-cyan-glow) !important;
  color: var(--jarvis-text-primary) !important;
  animation: jarvis-glow 2s infinite;
}

.form-control::placeholder, input::placeholder, textarea::placeholder {
  color: var(--jarvis-text-muted) !important;
  font-style: italic !important;
}

/* Botones estilo JARVIS */
.btn-primary, .btn-danger {
  background: linear-gradient(145deg, var(--jarvis-cyan), var(--jarvis-cyan-dark)) !important;
  border: 2px solid var(--jarvis-cyan-bright) !important;
  color: #000000 !important;
  font-weight: 600 !important;
  text-transform: uppercase !important;
  letter-spacing: 1px !important;
  border-radius: 25px !important;
  padding: 10px 20px !important;
  position: relative !important;
  overflow: hidden !important;
  transition: all 0.3s ease !important;
}

.btn-primary:hover, .btn-danger:hover {
  background: linear-gradient(145deg, var(--jarvis-cyan-bright), var(--jarvis-cyan)) !important;
  border-color: var(--jarvis-cyan-bright) !important;
  box-shadow: 
    0 0 20px var(--jarvis-cyan-glow),
    0 0 30px var(--jarvis-cyan-glow) !important;
  transform: translateY(-2px) !important;
}

.btn-primary::before, .btn-danger::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn-primary:hover::before, .btn-danger:hover::before {
  left: 100%;
}

/* Sidebar estilo JARVIS */
.sidebar {
  background: linear-gradient(180deg, var(--jarvis-bg-primary), var(--jarvis-bg-secondary)) !important;
  border-right: 3px solid var(--jarvis-cyan) !important;
  box-shadow: 
    5px 0 20px var(--jarvis-cyan-glow),
    inset -1px 0 0 rgba(0, 212, 255, 0.3) !important;
  position: relative !important;
}

.sidebar::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 1px;
  height: 100%;
  background: linear-gradient(180deg, transparent, var(--jarvis-cyan), transparent);
  animation: jarvis-pulse 3s infinite;
}

.sidebar h4 {
  color: var(--jarvis-cyan-bright) !important;
  text-transform: uppercase !important;
  letter-spacing: 2px !important;
  border-bottom: 2px solid var(--jarvis-cyan) !important;
  text-shadow: 0 0 10px var(--jarvis-cyan-glow) !important;
}

.sidebar .btn {
  border: 1px solid var(--jarvis-cyan) !important;
  background: rgba(22, 33, 62, 0.5) !important;
  color: var(--jarvis-text-secondary) !important;
  transition: all 0.3s ease !important;
  margin-bottom: 5px !important;
}

.sidebar .btn:hover {
  background: var(--jarvis-cyan-light) !important;
  border-color: var(--jarvis-cyan-bright) !important;
  color: var(--jarvis-cyan-bright) !important;
  box-shadow: 0 0 15px var(--jarvis-cyan-glow) !important;
  transform: translateX(10px) !important;
}

.sidebar .btn-danger {
  background: linear-gradient(145deg, var(--jarvis-cyan), var(--jarvis-cyan-dark)) !important;
  color: #000000 !important;
  animation: jarvis-glow 3s infinite !important;
}

/* Main content */
.main-content {
  background: transparent !important;
  position: relative !important;
}

/* Títulos estilo JARVIS */
h1, h2, h3, h4, h5, h6 {
  color: var(--jarvis-cyan-bright) !important;
  font-weight: 700 !important;
  text-transform: uppercase !important;
  letter-spacing: 2px !important;
  text-shadow: 0 0 10px var(--jarvis-cyan-glow) !important;
  position: relative !important;
}

h1::after, h2::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 50px;
  height: 2px;
  background: var(--jarvis-cyan);
  box-shadow: 0 0 5px var(--jarvis-cyan);
}

/* Texto */
p, span, div, label {
  color: var(--jarvis-text-primary) !important;
}

.text-muted {
  color: var(--jarvis-text-muted) !important;
}

.text-secondary {
  color: var(--jarvis-text-secondary) !important;
}

.text-danger {
  color: var(--jarvis-danger) !important;
  text-shadow: 0 0 5px var(--jarvis-danger) !important;
}

.text-success {
  color: var(--jarvis-success) !important;
  text-shadow: 0 0 5px var(--jarvis-success) !important;
}

/* Eliminar fondos blancos */
.bg-white, .bg-light {
  background: var(--jarvis-bg-secondary) !important;
}

*[style*="background-color: white"],
*[style*="background-color: #fff"],
*[style*="background-color: #ffffff"] {
  background: var(--jarvis-bg-tertiary) !important;
  color: var(--jarvis-text-primary) !important;
}

/* CORRECCIONES DE ESPACIADO Y LAYOUT */

/* Contenedores principales */
.container, .container-fluid {
  padding: 15px !important;
  margin: 0 auto !important;
}

.row {
  margin: 0 !important;
  padding: 0 !important;
}

.col, [class*="col-"] {
  padding: 8px !important;
}

/* Cards - Espaciado mejorado */
.card {
  margin-bottom: 20px !important;
  padding: 0 !important;
}

.card-header {
  padding: 12px 20px !important;
  margin: 0 !important;
}

.card-body {
  padding: 20px !important;
  margin: 0 !important;
}

/* Botones - Espaciado consistente */
.btn {
  margin: 5px !important;
  padding: 8px 16px !important;
}

.btn-sm {
  padding: 6px 12px !important;
  margin: 3px !important;
}

.btn-lg {
  padding: 12px 24px !important;
  margin: 8px !important;
}

/* Formularios - Espaciado mejorado */
.form-group {
  margin-bottom: 15px !important;
}

.form-control {
  margin-bottom: 10px !important;
  padding: 10px 15px !important;
}

.form-label, label {
  margin-bottom: 5px !important;
  display: block !important;
}

/* Sidebar - Espaciado interno */
.sidebar {
  padding: 20px 15px !important;
}

.sidebar .btn {
  width: 100% !important;
  margin-bottom: 8px !important;
  text-align: left !important;
}

.sidebar h4 {
  margin: 0 0 15px 0 !important;
  padding-bottom: 10px !important;
}

/* Dashboard cards - Espaciado específico */
.dashboard-card {
  margin: 10px !important;
  padding: 15px !important;
}

/* Stats cards - Espaciado uniforme */
.stats-card {
  margin: 8px !important;
  padding: 20px !important;
  text-align: center !important;
}

.stats-card h3 {
  margin: 10px 0 5px 0 !important;
}

.stats-card p {
  margin: 0 !important;
}

/* Quick actions - Espaciado vertical */
.quick-actions {
  padding: 15px !important;
}

.quick-actions .btn {
  margin: 5px 0 !important;
  width: 100% !important;
}

/* System status - Espaciado horizontal */
.system-status {
  padding: 15px !important;
}

.status-item {
  margin: 10px 0 !important;
  padding: 8px 0 !important;
  border-bottom: 1px solid var(--jarvis-border) !important;
}

.status-item:last-child {
  border-bottom: none !important;
}

/* Títulos - Espaciado consistente */
h1 {
  margin: 20px 0 15px 0 !important;
  padding: 0 !important;
}

h2 {
  margin: 15px 0 12px 0 !important;
  padding: 0 !important;
}

h3 {
  margin: 12px 0 10px 0 !important;
  padding: 0 !important;
}

h4 {
  margin: 10px 0 8px 0 !important;
  padding: 0 !important;
}

/* Párrafos y texto */
p {
  margin: 8px 0 !important;
  line-height: 1.5 !important;
}

/* Listas */
ul, ol {
  margin: 10px 0 !important;
  padding-left: 20px !important;
}

li {
  margin: 5px 0 !important;
}

/* Tablas - Espaciado mejorado */
.table {
  margin: 15px 0 !important;
}

.table td, .table th {
  padding: 12px 15px !important;
  vertical-align: middle !important;
}

/* Alertas - Espaciado interno */
.alert {
  margin: 15px 0 !important;
  padding: 15px 20px !important;
}

/* Progress bars - Espaciado */
.progress {
  margin: 10px 0 !important;
  height: 8px !important;
}

/* Badges - Espaciado */
.badge {
  margin: 2px !important;
  padding: 6px 10px !important;
}

/* Navegación - Espaciado */
.nav {
  margin: 0 !important;
  padding: 0 !important;
}

.nav-item {
  margin: 0 5px !important;
}

.nav-link {
  padding: 10px 15px !important;
}

/* Modales - Espaciado interno */
.modal-header {
  padding: 15px 20px !important;
}

.modal-body {
  padding: 20px !important;
}

.modal-footer {
  padding: 15px 20px !important;
}

/* Responsive - Espaciado en móviles */
@media (max-width: 768px) {
  .container, .container-fluid {
    padding: 10px !important;
  }

  .card-body {
    padding: 15px !important;
  }

  .sidebar {
    padding: 15px 10px !important;
  }

  .btn {
    margin: 3px !important;
    padding: 6px 12px !important;
  }
}
