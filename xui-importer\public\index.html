<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="%PUBLIC_URL%/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#000000" />
    <meta
      name="description"
      content="Web site created using create-react-app"
    />
    <link rel="apple-touch-icon" href="%PUBLIC_URL%/logo192.png" />
    <!--
      manifest.json provides metadata used when your web app is installed on a
      user's mobile device or desktop. See https://developers.google.com/web/fundamentals/web-app-manifest/
    -->
    <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />
    <!--
      Notice the use of %PUBLIC_URL% in the tags above.
      It will be replaced with the URL of the `public` folder during the build.
      Only files inside the `public` folder can be referenced from the HTML.

      Unlike "/favicon.ico" or "favicon.ico", "%PUBLIC_URL%/favicon.ico" will
      work correctly both with client-side routing and a non-root public URL.
      Learn how to configure a non-root public URL by running `npm run build`.
    -->
    <title>XUI Importer - JARVIS Interface</title>
    <!-- TEMA JARVIS PERMANENTE -->
    <link rel="stylesheet" href="%PUBLIC_URL%/jarvis-theme.css">
    <style>
      /* Forzar tema JARVIS desde el inicio */
      html, body, #root {
        background: #0a0a0a !important;
        color: #ffffff !important;
        font-family: 'Orbitron', 'Courier New', monospace !important;
      }
    </style>
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>

    <!-- Script JARVIS para detectar y corregir elementos -->
    <script>
      // Función para aplicar tema JARVIS y corregir elementos blancos
      function applyJarvisTheme() {
        const elements = document.querySelectorAll('*');
        elements.forEach(element => {
          const computedStyle = window.getComputedStyle(element);
          const bgColor = computedStyle.backgroundColor;

          // Detectar fondos blancos y aplicar tema JARVIS
          if (bgColor === 'rgb(255, 255, 255)' ||
              bgColor === 'white' ||
              bgColor === '#ffffff' ||
              bgColor === '#fff') {

            // No cambiar botones, badges o alertas específicos
            if (!element.classList.contains('btn-light') &&
                !element.classList.contains('badge-light') &&
                !element.classList.contains('alert-light')) {

              // Aplicar colores JARVIS según el tipo de elemento
              if (element.tagName === 'INPUT' ||
                  element.tagName === 'TEXTAREA' ||
                  element.tagName === 'SELECT' ||
                  element.classList.contains('form-control') ||
                  element.classList.contains('form-select')) {
                element.style.backgroundColor = 'rgba(22, 33, 62, 0.8)';
                element.style.color = '#ffffff';
                element.style.borderColor = '#00d4ff';
                element.style.fontFamily = 'Orbitron, Courier New, monospace';
              } else {
                element.style.backgroundColor = '#1a1a2e';
                element.style.color = '#ffffff';
                element.style.fontFamily = 'Orbitron, Courier New, monospace';
              }
            }
          }
        });

        // Aplicar fuente JARVIS a todos los elementos
        document.body.style.fontFamily = 'Orbitron, Courier New, monospace';
      }

      // Función para efectos de sonido JARVIS (opcional)
      function playJarvisSound(type = 'click') {
        // Aquí se pueden agregar sonidos de JARVIS si se desea
        console.log(`🤖 JARVIS: ${type} detected`);
      }

      // Agregar efectos de hover JARVIS
      function addJarvisEffects() {
        const buttons = document.querySelectorAll('.btn, button');
        buttons.forEach(btn => {
          btn.addEventListener('mouseenter', () => playJarvisSound('hover'));
          btn.addEventListener('click', () => playJarvisSound('click'));
        });
      }

      // Ejecutar cuando el DOM esté listo
      document.addEventListener('DOMContentLoaded', () => {
        applyJarvisTheme();
        addJarvisEffects();
        console.log('🤖 JARVIS Interface Activated');
      });

      // Ejecutar periódicamente para elementos dinámicos
      setInterval(applyJarvisTheme, 1000);

      // Ejecutar cuando React termine de renderizar
      setTimeout(() => {
        applyJarvisTheme();
        addJarvisEffects();
      }, 2000);
    </script>
    <!--
      This HTML file is a template.
      If you open it directly in the browser, you will see an empty page.

      You can add webfonts, meta tags, or analytics to this file.
      The build step will place the bundled scripts into the <body> tag.

      To begin the development, run `npm start` or `yarn start`.
      To create a production bundle, use `npm run build` or `yarn build`.
    -->
  </body>
</html>
