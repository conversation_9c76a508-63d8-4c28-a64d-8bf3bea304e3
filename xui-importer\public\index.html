<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="%PUBLIC_URL%/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#000000" />
    <meta
      name="description"
      content="Web site created using create-react-app"
    />
    <link rel="apple-touch-icon" href="%PUBLIC_URL%/logo192.png" />
    <!--
      manifest.json provides metadata used when your web app is installed on a
      user's mobile device or desktop. See https://developers.google.com/web/fundamentals/web-app-manifest/
    -->
    <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />
    <!--
      Notice the use of %PUBLIC_URL% in the tags above.
      It will be replaced with the URL of the `public` folder during the build.
      Only files inside the `public` folder can be referenced from the HTML.

      Unlike "/favicon.ico" or "favicon.ico", "%PUBLIC_URL%/favicon.ico" will
      work correctly both with client-side routing and a non-root public URL.
      Learn how to configure a non-root public URL by running `npm run build`.
    -->
    <title>XUI Importer - Dark Theme</title>
    <style>
      /* Forzar tema oscuro desde el inicio */
      html, body, #root {
        background-color: #1a1a1a !important;
        color: #ffffff !important;
      }
    </style>
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>

    <!-- Script para detectar y corregir elementos blancos -->
    <script>
      // Función para detectar y corregir elementos con fondo blanco
      function fixWhiteElements() {
        const elements = document.querySelectorAll('*');
        elements.forEach(element => {
          const computedStyle = window.getComputedStyle(element);
          const bgColor = computedStyle.backgroundColor;

          // Detectar fondos blancos
          if (bgColor === 'rgb(255, 255, 255)' ||
              bgColor === 'white' ||
              bgColor === '#ffffff' ||
              bgColor === '#fff') {

            // No cambiar botones, badges o alertas específicos
            if (!element.classList.contains('btn-light') &&
                !element.classList.contains('badge-light') &&
                !element.classList.contains('alert-light')) {

              // Determinar el fondo apropiado según el tipo de elemento
              if (element.tagName === 'INPUT' ||
                  element.tagName === 'TEXTAREA' ||
                  element.tagName === 'SELECT' ||
                  element.classList.contains('form-control') ||
                  element.classList.contains('form-select')) {
                element.style.backgroundColor = '#3a3a3a';
                element.style.color = '#ffffff';
                element.style.borderColor = '#444444';
              } else {
                element.style.backgroundColor = '#2d2d2d';
                element.style.color = '#ffffff';
              }
            }
          }
        });
      }

      // Ejecutar cuando el DOM esté listo
      document.addEventListener('DOMContentLoaded', fixWhiteElements);

      // Ejecutar periódicamente para elementos dinámicos
      setInterval(fixWhiteElements, 1000);

      // Ejecutar cuando React termine de renderizar
      setTimeout(fixWhiteElements, 2000);
    </script>
    <!--
      This HTML file is a template.
      If you open it directly in the browser, you will see an empty page.

      You can add webfonts, meta tags, or analytics to this file.
      The build step will place the bundled scripts into the <body> tag.

      To begin the development, run `npm start` or `yarn start`.
      To create a production bundle, use `npm run build` or `yarn build`.
    -->
  </body>
</html>
